name: Build and publish container image to Docker and GitHub Container Registry

on:
  workflow_dispatch:
    inputs:
      push:
        description: 'Push the built image to registries'
        required: true
        default: false
        type: boolean
      tag:
        description: 'Tag to publish'
        required: true
        type: string
      tag_as_latest:
        description: 'Tag as latest'
        required: false
        default: false
        type: boolean

  workflow_call:
    inputs:
      push:
        description: 'Push the built image to registries'
        required: true
        default: false
        type: boolean
      tag:
        description: 'Tag to publish'
        required: true
        type: string
      tag_as_latest:
        description: 'Tag as latest'
        required: false
        default: false
        type: boolean

permissions:
  contents: read
  id-token: write
  packages: write

env:
  GHCR_IMAGE_NAME: "ghcr.io/chroma-core/chroma"
  DOCKERHUB_IMAGE_NAME: "chromadb/chroma"

jobs:
  build:
    name: Build image for ${{ matrix.platform }}
    runs-on: ${{ matrix.runner }}
    strategy:
      fail-fast: false
      matrix:
        platform: [amd64, arm64]
        include:
          - platform: amd64
            runner: blacksmith-16vcpu-ubuntu-2204
            docker_platform: linux/amd64
          - platform: arm64
            runner: blacksmith-16vcpu-ubuntu-2204-arm
            docker_platform: linux/arm64
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Docker
        uses: ./.github/actions/docker
        with:
          ghcr-username: ${{ github.actor }}
          ghcr-password: ${{ secrets.GITHUB_TOKEN }}
          dockerhub-username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub-password: ${{ secrets.DOCKERHUB_TOKEN }}
          # Calling useblacksmith/build-push-action twice in the same job leads to strange errors
          setup-blacksmith: false

      - name: Compute arch-specific tags
        id: tags
        shell: bash
        run: |
          arch_tag="${{ inputs.tag }}-${{ matrix.platform }}"
          ghcr="${{ env.GHCR_IMAGE_NAME }}:${arch_tag}"
          dhub="${{ env.DOCKERHUB_IMAGE_NAME }}:${arch_tag}"
          echo "arch_tag=$arch_tag"      >> $GITHUB_OUTPUT

          # expose *matrix-unique* step outputs
          echo "ghcr_tag_${{ matrix.platform }}=$ghcr"         >> $GITHUB_OUTPUT
          echo "dockerhub_tag_${{ matrix.platform }}=$dhub"    >> $GITHUB_OUTPUT

          # these two tags are what the build-push action will publish for *this* arch
          echo "tags=$ghcr,$dhub" >> $GITHUB_OUTPUT

      - name: Pre-pull base images
        shell: bash
        run: |
          set -euo pipefail

          # Pre-pull base images to speed up the build
          docker pull --platform ${{ matrix.docker_platform }} rust:1.81.0
          docker pull --platform ${{ matrix.docker_platform }} debian:stable-slim

      - name: Build and push image
        uses: useblacksmith/build-push-action@v1.1
        with:
          context: .
          file: rust/Dockerfile
          target: cli
          platforms: ${{ matrix.docker_platform }}
          push: ${{ inputs.push }}
          build-args: |
            RELEASE_MODE=1
          tags: ${{ steps.tags.outputs.tags }}
    outputs:
      ghcr_tag_amd64:      ${{ steps.tags.outputs.ghcr_tag_amd64 }}
      ghcr_tag_arm64:      ${{ steps.tags.outputs.ghcr_tag_arm64 }}
      dockerhub_tag_amd64: ${{ steps.tags.outputs.dockerhub_tag_amd64 }}
      dockerhub_tag_arm64: ${{ steps.tags.outputs.dockerhub_tag_arm64 }}

  merge:
    name: Merge platform manifests
    runs-on: blacksmith-4vcpu-ubuntu-2204
    if: ${{ inputs.push == true }}
    needs:
      - build
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up Docker
        uses: ./.github/actions/docker
        with:
          ghcr-username: ${{ github.actor }}
          ghcr-password: ${{ secrets.GITHUB_TOKEN }}
          dockerhub-username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub-password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Create and push manifest
        shell: bash
        run: |
          set -euo pipefail

          # Pull the per-arch tags from job-level outputs
          ghcr_amd64='${{ needs.build.outputs.ghcr_tag_amd64 }}'
          ghcr_arm64='${{ needs.build.outputs.ghcr_tag_arm64 }}'
          dhub_amd64='${{ needs.build.outputs.dockerhub_tag_amd64 }}'
          dhub_arm64='${{ needs.build.outputs.dockerhub_tag_arm64 }}'

          base_tag='${{ inputs.tag }}'
          ghcr_base="${{ env.GHCR_IMAGE_NAME }}:${base_tag}"
          dhub_base="${{ env.DOCKERHUB_IMAGE_NAME }}:${base_tag}"

          docker buildx imagetools create --tag "$ghcr_base" $ghcr_amd64 $ghcr_arm64
          docker buildx imagetools create --tag "$dhub_base"  $dhub_amd64 $dhub_arm64

          if [[ "${{ inputs.tag_as_latest }}" == "true" ]]; then
            docker buildx imagetools create --tag "${{ env.GHCR_IMAGE_NAME }}:latest"  $ghcr_amd64 $ghcr_arm64
            docker buildx imagetools create --tag "${{ env.DOCKERHUB_IMAGE_NAME }}:latest" $dhub_amd64 $dhub_arm64
          fi

      - name: Inspect image
        run: |
          docker buildx imagetools inspect ${{ env.GHCR_IMAGE_NAME }}:${{ inputs.tag }}
          docker buildx imagetools inspect ${{ env.DOCKERHUB_IMAGE_NAME }}:${{ inputs.tag }}
