{"name": "@chroma-core/all", "version": "0.1.7", "private": false, "description": "All AI embedding providers for Chroma", "main": "dist/cjs/index.cjs", "types": "dist/index.d.ts", "module": "dist/index.legacy-esm.js", "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}}}, "files": ["src", "dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup", "watch": "tsup --watch", "typecheck": "tsc --noEmit"}, "devDependencies": {"rimraf": "^5.0.0", "tsup": "^8.3.5"}, "dependencies": {"@chroma-core/ai-embeddings-common": "workspace:^", "@chroma-core/cloudflare-worker-ai": "workspace:^", "@chroma-core/cohere": "workspace:^", "@chroma-core/default-embed": "workspace:^", "@chroma-core/google-gemini": "workspace:^", "@chroma-core/huggingface-server": "workspace:^", "@chroma-core/jina": "workspace:^", "@chroma-core/ollama": "workspace:^", "@chroma-core/openai": "workspace:^", "@chroma-core/together-ai": "workspace:^", "@chroma-core/voyageai": "workspace:^", "@chroma-core/mistral": "workspace:^"}, "engines": {"node": ">=20"}, "publishConfig": {"access": "public"}}