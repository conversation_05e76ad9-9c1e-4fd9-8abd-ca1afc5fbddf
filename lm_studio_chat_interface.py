#!/usr/bin/env python3
"""
LM Studio Chat Interface for Agent Zero
Easy-to-use interface for sending prompts to LM Studio models
"""

import sys
import json
from pathlib import Path
from typing import Dict, Any, Optional

# Add lib to path
sys.path.append(str(Path(__file__).parent / "lib"))

class LMStudioChatInterface:
    """Simple chat interface for LM Studio integration"""
    
    def __init__(self):
        self.current_model = None
        self.conversation_history = []
        self.available_models = []
        self.lm_manager = None
        
    def initialize(self) -> bool:
        """Initialize LM Studio connection"""
        try:
            from lib.ai.lm_studio_integration import LMStudioManager
            
            self.lm_manager = LMStudioManager()
            connection = self.lm_manager.check_connection()
            
            if connection["success"]:
                models_result = self.lm_manager.get_available_models()
                if models_result["success"]:
                    self.available_models = models_result["models"]
                    print("✅ LM Studio connected successfully!")
                    print(f"📋 Found {len(self.available_models)} models")
                    return True
            
            print("❌ Failed to connect to LM Studio")
            print("Make sure LM Studio is running on localhost:1234")
            return False
            
        except Exception as e:
            print(f"❌ Initialization error: {str(e)}")
            return False
    
    def show_models(self):
        """Show available models"""
        if not self.available_models:
            print("❌ No models available. Make sure LM Studio is running.")
            return
        
        print("\n📋 Available Models:")
        print("-" * 50)
        
        # Group by specialization
        code_models = [m for m in self.available_models if "code" in m["specialization"].lower()]
        reasoning_models = [m for m in self.available_models if "reasoning" in m["specialization"].lower()]
        general_models = [m for m in self.available_models if "instruction" in m["specialization"].lower()]
        
        if code_models:
            print("🖥️  CODING MODELS:")
            for i, model in enumerate(code_models, 1):
                marker = "👑" if model["size"] == "13B" else "⭐" if "7B" in model["size"] else "📦"
                print(f"   {i}. {marker} {model['name']}")
                print(f"      Size: {model['size']} | Use: {model['recommended_use']}")
        
        if reasoning_models:
            print("\n🧠 REASONING MODELS:")
            for i, model in enumerate(reasoning_models, 1):
                marker = "👑" if model["size"] == "13B" else "⭐" if "7B" in model["size"] else "📦"
                print(f"   {i}. {marker} {model['name']}")
                print(f"      Size: {model['size']} | Use: {model['recommended_use']}")
        
        if general_models:
            print("\n💬 GENERAL MODELS:")
            for i, model in enumerate(general_models, 1):
                marker = "👑" if model["size"] == "13B" else "⭐" if "7B" in model["size"] else "📦"
                print(f"   {i}. {marker} {model['name']}")
                print(f"      Size: {model['size']} | Use: {model['recommended_use']}")
        
        if self.current_model:
            print(f"\n🎯 Current model: {self.current_model}")
    
    def select_model(self, model_name: str = None) -> bool:
        """Select a model for chat"""
        if not model_name:
            self.show_models()
            print("\n🎯 Enter model name (or part of it):")
            model_name = input("> ").strip()
        
        # Find matching model
        matching_models = [
            m for m in self.available_models 
            if model_name.lower() in m["name"].lower()
        ]
        
        if not matching_models:
            print(f"❌ No model found matching '{model_name}'")
            return False
        
        if len(matching_models) > 1:
            print(f"🔍 Multiple models found for '{model_name}':")
            for i, model in enumerate(matching_models, 1):
                print(f"   {i}. {model['name']}")
            
            try:
                choice = int(input("Select number: ")) - 1
                if 0 <= choice < len(matching_models):
                    selected_model = matching_models[choice]
                else:
                    print("❌ Invalid selection")
                    return False
            except ValueError:
                print("❌ Invalid input")
                return False
        else:
            selected_model = matching_models[0]
        
        self.current_model = selected_model["id"]
        result = self.lm_manager.set_current_model(self.current_model)
        
        if result["success"]:
            print(f"✅ Selected model: {selected_model['name']}")
            print(f"   Specialization: {selected_model['specialization']}")
            print(f"   Best for: {selected_model['recommended_use']}")
            return True
        else:
            print(f"❌ Failed to select model: {result['error']}")
            return False
    
    def auto_select_model(self, task_type: str) -> bool:
        """Automatically select best model for task type"""
        if not self.lm_manager:
            return False
        
        recommendations = self.lm_manager.get_model_recommendations(task_type)
        
        if recommendations:
            best_model = recommendations[0]
            self.current_model = best_model["id"]
            result = self.lm_manager.set_current_model(self.current_model)
            
            if result["success"]:
                print(f"🎯 Auto-selected best model for {task_type}:")
                print(f"   Model: {best_model['name']}")
                print(f"   Score: {best_model['score']}/10")
                print(f"   Reason: {best_model['recommended_use']}")
                return True
        
        print(f"❌ No suitable model found for {task_type}")
        return False
    
    def send_prompt(self, prompt: str, temperature: float = 0.7, max_tokens: int = 2048) -> Optional[str]:
        """Send prompt to current model"""
        if not self.current_model:
            print("❌ No model selected. Use 'select' command first.")
            return None
        
        if not prompt.strip():
            print("❌ Empty prompt")
            return None
        
        print(f"🤖 Sending to {self.current_model}...")
        print("⏳ Generating response...")
        
        result = self.lm_manager.send_prompt(prompt, self.current_model, max_tokens, temperature)
        
        if result["success"]:
            response = result["response"]
            tokens_used = result.get("tokens_used", 0)
            
            # Add to conversation history
            self.conversation_history.append({
                "prompt": prompt,
                "response": response,
                "model": self.current_model,
                "tokens": tokens_used
            })
            
            print(f"\n✅ Response (Tokens: {tokens_used}):")
            print("-" * 50)
            print(response)
            print("-" * 50)
            
            return response
        else:
            print(f"❌ Error: {result['error']}")
            return None
    
    def show_conversation_history(self):
        """Show conversation history"""
        if not self.conversation_history:
            print("📝 No conversation history")
            return
        
        print(f"\n📝 Conversation History ({len(self.conversation_history)} messages):")
        print("=" * 60)
        
        for i, entry in enumerate(self.conversation_history, 1):
            print(f"\n{i}. Model: {entry['model']} | Tokens: {entry['tokens']}")
            print(f"   Prompt: {entry['prompt'][:100]}{'...' if len(entry['prompt']) > 100 else ''}")
            print(f"   Response: {entry['response'][:100]}{'...' if len(entry['response']) > 100 else ''}")
    
    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history = []
        print("✅ Conversation history cleared")
    
    def show_help(self):
        """Show help information"""
        help_text = """
🚀 LM Studio Chat Interface Commands:

📋 MODEL MANAGEMENT:
   models          - Show available models
   select [name]   - Select a model for chat
   auto [task]     - Auto-select best model for task type
                     Tasks: coding, reasoning, general, debugging

💬 CHAT COMMANDS:
   chat            - Start interactive chat mode
   send [prompt]   - Send single prompt
   temp [0.1-2.0]  - Set temperature (creativity)
   tokens [num]    - Set max tokens

📝 HISTORY:
   history         - Show conversation history
   clear           - Clear conversation history

🎯 QUICK TASKS:
   code [prompt]   - Auto-select coding model and send prompt
   reason [prompt] - Auto-select reasoning model and send prompt
   debug [prompt]  - Auto-select debugging model and send prompt

🔧 SYSTEM:
   status          - Show connection status
   help            - Show this help
   quit/exit       - Exit interface

💡 EXAMPLES:
   > auto coding
   > send "Write a Python function to sort a list"
   > code "Create a REST API with FastAPI"
   > reason "Explain quantum computing"
   > debug "Fix this Python error: NameError"
"""
        print(help_text)
    
    def interactive_mode(self):
        """Start interactive chat mode"""
        print("\n💬 Interactive Chat Mode (type 'exit' to quit)")
        print("=" * 50)
        
        while True:
            try:
                prompt = input("\n🎯 You: ").strip()
                
                if prompt.lower() in ['exit', 'quit', 'q']:
                    break
                
                if prompt:
                    self.send_prompt(prompt)
                
            except KeyboardInterrupt:
                print("\n👋 Chat interrupted")
                break
            except EOFError:
                break
        
        print("👋 Exiting chat mode")
    
    def run_cli(self):
        """Run command line interface"""
        print("🚀 LM Studio Chat Interface for Agent Zero")
        print("=" * 50)
        
        if not self.initialize():
            return
        
        self.show_help()
        
        while True:
            try:
                command = input("\n🎯 Command: ").strip().split()
                
                if not command:
                    continue
                
                cmd = command[0].lower()
                args = command[1:] if len(command) > 1 else []
                
                if cmd in ['quit', 'exit', 'q']:
                    break
                elif cmd == 'help':
                    self.show_help()
                elif cmd == 'models':
                    self.show_models()
                elif cmd == 'select':
                    model_name = ' '.join(args) if args else None
                    self.select_model(model_name)
                elif cmd == 'auto':
                    task_type = args[0] if args else 'general'
                    self.auto_select_model(task_type)
                elif cmd == 'send':
                    prompt = ' '.join(args)
                    self.send_prompt(prompt)
                elif cmd == 'chat':
                    self.interactive_mode()
                elif cmd == 'history':
                    self.show_conversation_history()
                elif cmd == 'clear':
                    self.clear_history()
                elif cmd == 'status':
                    connection = self.lm_manager.check_connection()
                    print(f"Status: {connection}")
                elif cmd == 'temp':
                    if args:
                        try:
                            temp = float(args[0])
                            print(f"✅ Temperature set to {temp}")
                        except ValueError:
                            print("❌ Invalid temperature value")
                elif cmd == 'tokens':
                    if args:
                        try:
                            tokens = int(args[0])
                            print(f"✅ Max tokens set to {tokens}")
                        except ValueError:
                            print("❌ Invalid token value")
                elif cmd == 'code':
                    self.auto_select_model('coding')
                    prompt = ' '.join(args)
                    if prompt:
                        self.send_prompt(prompt)
                elif cmd == 'reason':
                    self.auto_select_model('reasoning')
                    prompt = ' '.join(args)
                    if prompt:
                        self.send_prompt(prompt)
                elif cmd == 'debug':
                    self.auto_select_model('coding')
                    prompt = f"Debug this issue: {' '.join(args)}"
                    if args:
                        self.send_prompt(prompt)
                else:
                    print(f"❌ Unknown command: {cmd}")
                    print("Type 'help' for available commands")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except EOFError:
                break
        
        print("👋 LM Studio Chat Interface closed")


def main():
    """Main function"""
    interface = LMStudioChatInterface()
    interface.run_cli()


if __name__ == "__main__":
    main()
