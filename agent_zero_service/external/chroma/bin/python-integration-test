#!/usr/bin/env bash

set -e

export CHROMA_PORT=8000

function cleanup {
  docker compose -f docker-compose.test.yml down --rmi local --volumes
}

trap cleanup EXIT

docker compose -f docker-compose.test.yml --env-file ${ENV_FILE:-compose-env.linux} up --build -d

export CHROMA_INTEGRATION_TEST_ONLY=1
export CHROMA_SERVER_HOST=localhost
export CHROMA_SERVER_HTTP_PORT=8000
export CHROMA_SERVER_NOFILE=65535

echo testing: python -m pytest "$@"
python -m pytest "$@"

docker compose down
