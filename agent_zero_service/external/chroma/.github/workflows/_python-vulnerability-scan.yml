name: Scan for Python Vulnerabilities

on:
  workflow_call:

jobs:
  bandit-scan:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
      - name: Setup
        uses: ./.github/actions/python
      - uses: ./.github/actions/bandit-scan/
        with:
          input-dir: '.'
          format: 'json'
          bandit-config: 'bandit.yaml'
          output-file: 'bandit-report.json'
      - name: Upload Bandit Report
        uses: actions/upload-artifact@v4
        with:
          name: bandit-artifact
          path: |
            bandit-report.json
