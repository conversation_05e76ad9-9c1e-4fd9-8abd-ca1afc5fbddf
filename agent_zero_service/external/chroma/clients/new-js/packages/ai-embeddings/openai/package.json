{"name": "@chroma-core/openai", "version": "0.1.7", "private": false, "description": "OpenAI embedding provider for Chroma", "main": "dist/cjs/openai.cjs", "types": "dist/openai.d.ts", "module": "dist/openai.legacy-esm.js", "type": "module", "exports": {".": {"import": {"types": "./dist/openai.d.ts", "default": "./dist/openai.mjs"}, "require": {"types": "./dist/cjs/openai.d.cts", "default": "./dist/cjs/openai.cjs"}}}, "files": ["src", "dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup", "watch": "tsup --watch", "test": "jest"}, "devDependencies": {"@jest/globals": "^29.7.0", "dotenv": "^16.3.1", "jest": "^29.7.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsup": "^8.3.5"}, "peerDependencies": {"chromadb": "workspace:^"}, "dependencies": {"@chroma-core/ai-embeddings-common": "workspace:^", "openai": "^4.102.0"}, "engines": {"node": ">=20"}, "publishConfig": {"access": "public"}}