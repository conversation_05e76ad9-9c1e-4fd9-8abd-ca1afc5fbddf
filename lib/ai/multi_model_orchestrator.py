"""
Multi-Model Orchestrator for Agent Zero
Coordinates between OpenRouter, LM Studio, and professional AI tools
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum


class ModelProvider(Enum):
    OPENROUTER = "openrouter"
    LM_STUDIO = "lm_studio"
    PROFESSIONAL_PROMPTS = "professional_prompts"


@dataclass
class ModelCapability:
    provider: ModelProvider
    model_id: str
    specialization: str
    strength_score: int
    cost_score: int  # 1-10, 10 = most cost effective
    speed_score: int  # 1-10, 10 = fastest


class MultiModelOrchestrator:
    """Orchestrates multiple AI models for optimal task execution"""
    
    def __init__(self):
        self.model_capabilities = self._initialize_model_capabilities()
        self.task_history = []
        self.performance_metrics = {}
        
    def _initialize_model_capabilities(self) -> List[ModelCapability]:
        """Initialize model capabilities database"""
        return [
            # OpenRouter Models
            ModelCapability(
                provider=ModelProvider.OPENROUTER,
                model_id="deepseek/deepseek-chat",
                specialization="general_reasoning",
                strength_score=9,
                cost_score=8,
                speed_score=7
            ),
            ModelCapability(
                provider=ModelProvider.OPENROUTER,
                model_id="deepseek/deepseek-coder",
                specialization="code_generation",
                strength_score=10,
                cost_score=8,
                speed_score=7
            ),
            
            # LM Studio Models (will be populated dynamically)
            ModelCapability(
                provider=ModelProvider.LM_STUDIO,
                model_id="codellama-instruct",
                specialization="code_generation",
                strength_score=8,
                cost_score=10,  # Local = free
                speed_score=6
            ),
            ModelCapability(
                provider=ModelProvider.LM_STUDIO,
                model_id="mistral-instruct",
                specialization="general_reasoning",
                strength_score=8,
                cost_score=10,
                speed_score=8
            ),
            
            # Professional Prompts
            ModelCapability(
                provider=ModelProvider.PROFESSIONAL_PROMPTS,
                model_id="cursor_prompts",
                specialization="code_assistance",
                strength_score=9,
                cost_score=10,
                speed_score=9
            ),
            ModelCapability(
                provider=ModelProvider.PROFESSIONAL_PROMPTS,
                model_id="v0_prompts",
                specialization="ui_generation",
                strength_score=10,
                cost_score=10,
                speed_score=9
            )
        ]
    
    async def orchestrate_task(self, task: str, task_type: str, 
                              preferences: Dict[str, Any] = None) -> Dict[str, Any]:
        """Orchestrate a task across multiple models"""
        
        preferences = preferences or {}
        priority = preferences.get("priority", "balanced")  # speed, cost, quality, balanced
        
        # Step 1: Analyze task and select optimal models
        selected_models = self._select_models_for_task(task_type, priority)
        
        # Step 2: Execute task with selected models
        results = await self._execute_with_models(task, selected_models)
        
        # Step 3: Combine and optimize results
        final_result = self._combine_results(results, task_type)
        
        # Step 4: Update performance metrics
        self._update_metrics(task_type, selected_models, final_result)
        
        return {
            "task": task,
            "task_type": task_type,
            "models_used": [m.model_id for m in selected_models],
            "result": final_result,
            "execution_details": results,
            "optimization_applied": True
        }
    
    def _select_models_for_task(self, task_type: str, priority: str) -> List[ModelCapability]:
        """Select optimal models based on task type and priority"""
        
        # Filter models by specialization
        relevant_models = []
        
        for model in self.model_capabilities:
            if self._is_model_relevant(model, task_type):
                relevant_models.append(model)
        
        # Sort by priority
        if priority == "speed":
            relevant_models.sort(key=lambda m: m.speed_score, reverse=True)
        elif priority == "cost":
            relevant_models.sort(key=lambda m: m.cost_score, reverse=True)
        elif priority == "quality":
            relevant_models.sort(key=lambda m: m.strength_score, reverse=True)
        else:  # balanced
            relevant_models.sort(
                key=lambda m: (m.strength_score + m.cost_score + m.speed_score) / 3,
                reverse=True
            )
        
        # Return top 3 models for orchestration
        return relevant_models[:3]
    
    def _is_model_relevant(self, model: ModelCapability, task_type: str) -> bool:
        """Check if model is relevant for task type"""
        task_mapping = {
            "coding": ["code_generation", "code_assistance"],
            "debugging": ["code_generation", "code_assistance"],
            "ui_design": ["ui_generation", "code_generation"],
            "reasoning": ["general_reasoning"],
            "analysis": ["general_reasoning"],
            "general": ["general_reasoning", "code_assistance"]
        }
        
        relevant_specializations = task_mapping.get(task_type.lower(), ["general_reasoning"])
        return model.specialization in relevant_specializations
    
    async def _execute_with_models(self, task: str, models: List[ModelCapability]) -> List[Dict[str, Any]]:
        """Execute task with selected models"""
        results = []
        
        for model in models:
            try:
                if model.provider == ModelProvider.OPENROUTER:
                    result = await self._execute_openrouter(task, model)
                elif model.provider == ModelProvider.LM_STUDIO:
                    result = await self._execute_lm_studio(task, model)
                elif model.provider == ModelProvider.PROFESSIONAL_PROMPTS:
                    result = await self._execute_professional_prompts(task, model)
                else:
                    result = {"error": f"Unknown provider: {model.provider}"}
                
                results.append({
                    "model": model.model_id,
                    "provider": model.provider.value,
                    "result": result,
                    "success": "error" not in result
                })
                
            except Exception as e:
                results.append({
                    "model": model.model_id,
                    "provider": model.provider.value,
                    "result": {"error": str(e)},
                    "success": False
                })
        
        return results
    
    async def _execute_openrouter(self, task: str, model: ModelCapability) -> Dict[str, Any]:
        """Execute task with OpenRouter model"""
        # This would integrate with your existing OpenRouter setup
        return {
            "response": f"OpenRouter response from {model.model_id} for: {task}",
            "provider": "openrouter",
            "model": model.model_id
        }
    
    async def _execute_lm_studio(self, task: str, model: ModelCapability) -> Dict[str, Any]:
        """Execute task with LM Studio model"""
        try:
            from ai.lm_studio_integration import LMStudioManager
            
            lm = LMStudioManager()
            result = lm.send_prompt(task, model.model_id)
            
            return {
                "response": result.get("response", ""),
                "provider": "lm_studio",
                "model": model.model_id,
                "success": result.get("success", False)
            }
            
        except Exception as e:
            return {"error": f"LM Studio execution failed: {str(e)}"}
    
    async def _execute_professional_prompts(self, task: str, model: ModelCapability) -> Dict[str, Any]:
        """Execute task with professional prompts"""
        try:
            from ai.system_prompt_loader import SystemPromptLoader
            
            loader = SystemPromptLoader()
            
            if model.model_id == "cursor_prompts":
                prompt = loader.get_cursor_system_prompt()
            elif model.model_id == "v0_prompts":
                prompt = loader.get_v0_system_prompt()
            else:
                prompt = loader.create_agent_zero_prompt("Cursor Prompts", "coding")
            
            enhanced_task = f"{prompt}\n\nTask: {task}"
            
            return {
                "response": enhanced_task,
                "provider": "professional_prompts",
                "model": model.model_id,
                "prompt_length": len(enhanced_task)
            }
            
        except Exception as e:
            return {"error": f"Professional prompts execution failed: {str(e)}"}
    
    def _combine_results(self, results: List[Dict[str, Any]], task_type: str) -> Dict[str, Any]:
        """Combine results from multiple models"""
        successful_results = [r for r in results if r["success"]]
        
        if not successful_results:
            return {
                "error": "All models failed to execute",
                "failed_results": results
            }
        
        # For now, return the best result based on provider priority
        # In a real implementation, you might use more sophisticated combination logic
        
        priority_order = [
            ModelProvider.PROFESSIONAL_PROMPTS.value,
            ModelProvider.LM_STUDIO.value,
            ModelProvider.OPENROUTER.value
        ]
        
        for provider in priority_order:
            for result in successful_results:
                if result["provider"] == provider:
                    return {
                        "primary_result": result["result"],
                        "primary_model": result["model"],
                        "alternative_results": [r for r in successful_results if r != result],
                        "combination_strategy": "priority_based"
                    }
        
        # Fallback to first successful result
        return {
            "primary_result": successful_results[0]["result"],
            "primary_model": successful_results[0]["model"],
            "alternative_results": successful_results[1:],
            "combination_strategy": "fallback"
        }
    
    def _update_metrics(self, task_type: str, models: List[ModelCapability], result: Dict[str, Any]):
        """Update performance metrics for future optimization"""
        if task_type not in self.performance_metrics:
            self.performance_metrics[task_type] = {}
        
        for model in models:
            model_key = f"{model.provider.value}:{model.model_id}"
            if model_key not in self.performance_metrics[task_type]:
                self.performance_metrics[task_type][model_key] = {
                    "success_count": 0,
                    "total_attempts": 0,
                    "avg_quality": 0
                }
            
            metrics = self.performance_metrics[task_type][model_key]
            metrics["total_attempts"] += 1
            
            if "error" not in result:
                metrics["success_count"] += 1
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get performance report for all models"""
        report = {
            "total_tasks": len(self.task_history),
            "task_types": list(self.performance_metrics.keys()),
            "model_performance": {}
        }
        
        for task_type, models in self.performance_metrics.items():
            report["model_performance"][task_type] = {}
            for model_key, metrics in models.items():
                success_rate = metrics["success_count"] / metrics["total_attempts"] if metrics["total_attempts"] > 0 else 0
                report["model_performance"][task_type][model_key] = {
                    "success_rate": f"{success_rate:.2%}",
                    "total_attempts": metrics["total_attempts"]
                }
        
        return report
    
    def recommend_setup(self) -> Dict[str, Any]:
        """Recommend optimal 3-model setup"""
        return {
            "recommended_setup": {
                "primary_coding": {
                    "provider": "LM Studio",
                    "model": "CodeLlama-13B-Instruct",
                    "reason": "Local, fast, specialized for coding"
                },
                "reasoning_analysis": {
                    "provider": "OpenRouter",
                    "model": "deepseek/deepseek-chat",
                    "reason": "Advanced reasoning, cost-effective"
                },
                "ui_generation": {
                    "provider": "Professional Prompts",
                    "model": "v0_prompts + LM Studio",
                    "reason": "Best UI generation practices + local execution"
                }
            },
            "orchestration_strategy": "Task-based routing with fallback",
            "benefits": [
                "Cost optimization (local models for heavy tasks)",
                "Speed optimization (local execution)",
                "Quality optimization (specialized models per task)",
                "Reliability (multiple fallback options)"
            ]
        }


# Helper function for Agent Zero integration
async def orchestrate_ai_task(task: str, task_type: str, preferences: Dict[str, Any] = None) -> Dict[str, Any]:
    """Main orchestration function for Agent Zero"""
    orchestrator = MultiModelOrchestrator()
    return await orchestrator.orchestrate_task(task, task_type, preferences)


# Example usage
if __name__ == "__main__":
    async def test_orchestrator():
        orchestrator = MultiModelOrchestrator()
        
        # Test coding task
        result = await orchestrator.orchestrate_task(
            "Create a Python function to parse CSV files",
            "coding",
            {"priority": "quality"}
        )
        
        print("Orchestration result:", result)
        
        # Get recommendations
        recommendations = orchestrator.recommend_setup()
        print("Setup recommendations:", recommendations)
    
    # Run test
    asyncio.run(test_orchestrator())
