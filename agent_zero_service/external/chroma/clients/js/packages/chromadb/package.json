{"name": "chromadb", "version": "2.4.7", "description": "A JavaScript interface for chroma with embedding functions as bundled dependencies", "license": "Apache-2.0", "type": "module", "main": "dist/cjs/chromadb.cjs", "types": "dist/chromadb.d.ts", "module": "dist/chromadb.legacy-esm.js", "exports": {".": {"import": {"types": "./dist/chromadb.d.ts", "default": "./dist/chromadb.mjs"}, "require": {"types": "./dist/cjs/chromadb.d.cts", "default": "./dist/cjs/chromadb.cjs"}}}, "files": ["src", "dist"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "NODE_OPTIONS=--enable-source-maps tsup", "watch": "NODE_OPTIONS=--enable-source-maps tsup --watch", "test": "jest --runInBand"}, "dependencies": {"@google/generative-ai": "^0.1.1", "@xenova/transformers": "^2.17.2", "chromadb-default-embed": "^2.14.0", "cliui": "^8.0.1", "cohere-ai": "^7.0.0", "isomorphic-fetch": "^3.0.0", "ollama": "^0.5.0", "openai": "^4.0.0", "semver": "^7.7.1", "voyageai": "^0.0.3-1"}, "devDependencies": {"@internal/chromadb-core": "workspace:*", "@types/semver": "^7.7.0", "jest": "^29.5.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "tsup": "^7.2.0", "typescript": "^5.0.4"}, "optionalDependencies": {"chromadb-js-bindings-darwin-arm64": "^0.1.3", "chromadb-js-bindings-darwin-x64": "^0.1.3", "chromadb-js-bindings-linux-arm64-gnu": "^0.1.3", "chromadb-js-bindings-linux-x64-gnu": "^0.1.3", "chromadb-js-bindings-win32-x64-msvc": "^0.1.3"}, "engines": {"node": ">=14.17.0"}, "bin": {"chroma": "dist/cli.mjs"}}