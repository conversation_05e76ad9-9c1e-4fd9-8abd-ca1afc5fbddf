[project]
name = "chromadb-client"
dynamic = ["version"]

authors = [
  { name = "<PERSON>", email = "<EMAIL>" },
]
description = "Chroma Client."
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
  "Programming Language :: Python :: 3",
  "License :: OSI Approved :: Apache Software License",
  "Operating System :: OS Independent",
]
dependencies = [
  'numpy >= 1.22.5',
  'opentelemetry-api>=1.2.0',
  'opentelemetry-exporter-otlp-proto-grpc>=1.2.0',
  'opentelemetry-sdk>=1.2.0',
  'overrides >= 7.3.1',
  'posthog >= 2.4.0',
  'pydantic>=1.9',
  'typing_extensions >= 4.5.0',
  'tenacity>=8.2.3',
  'PyYAML>=6.0.0',
  'orjson>=3.9.12',
  'httpx>=0.27.0',
  'jsonschema>=4.19.0',
  'pybase64>=1.4.1',
]

[tool.black]
line-length = 88
required-version = "23.3.0"                         # <PERSON> will refuse to run if it's not this version.
target-version = ['py39', 'py310', 'py311']

[tool.pytest.ini_options]
pythonpath = ["."]

[project.urls]
"Homepage" = "https://github.com/chroma-core/chroma"
"Bug Tracker" = "https://github.com/chroma-core/chroma/issues"

[build-system]
requires = ["setuptools>=61.0", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[tool.setuptools_scm]
local_scheme = "no-local-version"
tag_regex = '^(?P<version>[0-9]+\.[0-9]+\.[0-9]+)$'
git_describe_command = 'git describe --tags --long --match "[0-9]*.[0-9]*.[0-9]*"'

[tool.setuptools]
packages = ["chromadb"]
