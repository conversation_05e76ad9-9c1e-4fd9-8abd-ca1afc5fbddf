{"name": "@chroma-core/ollama", "version": "0.1.7", "private": false, "description": "Ollama embedding provider for Chroma", "main": "dist/cjs/ollama.cjs", "types": "dist/ollama.d.ts", "module": "dist/ollama.legacy-esm.js", "type": "module", "exports": {".": {"import": {"types": "./dist/ollama.d.ts", "default": "./dist/ollama.mjs"}, "require": {"types": "./dist/cjs/ollama.d.cts", "default": "./dist/cjs/ollama.cjs"}}}, "files": ["src", "dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup", "watch": "tsup --watch", "test": "jest"}, "devDependencies": {"@jest/globals": "^29.7.0", "dotenv": "^16.3.1", "jest": "^29.7.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsup": "^8.3.5"}, "peerDependencies": {"chromadb": "workspace:^"}, "dependencies": {"@chroma-core/ai-embeddings-common": "workspace:^", "ollama": "^0.5.15", "testcontainers": "^10.9.0"}, "engines": {"node": ">=20"}, "publishConfig": {"access": "public"}}