import { beforeEach, describe, expect, it, jest } from "@jest/globals";
import { TogetherAIEmbeddingFunction } from "./index";

describe("TogetherAIEmbeddingFunction", () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  const MODEL = "WhereIsAI/UAE-Large-V1";

  const defaultParametersTest = "should initialize with default parameters";
  if (!process.env.TOGETHER_API_KEY) {
    it.skip(defaultParametersTest, () => {});
  } else {
    it(defaultParametersTest, () => {
      const embedder = new TogetherAIEmbeddingFunction({ modelName: MODEL });
      expect(embedder.name).toBe("together-ai");

      const config = embedder.getConfig();
      expect(config.model_name).toBe(MODEL);
      expect(config.api_key_env_var).toBe("TOGETHER_API_KEY");
    });
  }

  const customParametersTest = "should initialize with custom parameters";
  if (!process.env.TOGETHER_API_KEY) {
    it.skip(customParametersTest, () => {});
  } else {
    it(customParametersTest, () => {
      const embedder = new TogetherAIEmbeddingFunction({
        modelName: "custom-model",
      });

      const config = embedder.getConfig();
      expect(config.model_name).toBe("custom-model");
    });
  }

  it("should initialize with custom error for a API key", () => {
    const originalEnv = process.env.TOGETHER_API_KEY;
    delete process.env.TOGETHER_API_KEY;

    try {
      expect(() => {
        new TogetherAIEmbeddingFunction({ modelName: MODEL });
      }).toThrow("TogetherAI API key is required");
    } finally {
      if (originalEnv) {
        process.env.TOGETHER_API_KEY = originalEnv;
      }
    }
  });

  it("should use custom API key environment variable", () => {
    process.env.CUSTOM_TOGETHER_API_KEY = "test-api-key";

    try {
      const embedder = new TogetherAIEmbeddingFunction({
        modelName: MODEL,
        apiKeyEnvVar: "CUSTOM_TOGETHER_API_KEY",
      });

      expect(embedder.getConfig().api_key_env_var).toBe(
        "CUSTOM_TOGETHER_API_KEY",
      );
    } finally {
      delete process.env.CUSTOM_TOGETHER_API_KEY;
    }
  });

  const buildFromConfigTest = "should build from config";
  if (!process.env.TOGETHER_API_KEY) {
    it.skip(buildFromConfigTest, () => {});
  } else {
    it(buildFromConfigTest, () => {
      const config = {
        api_key_env_var: "TOGETHER_API_KEY",
        model_name: "config-model",
      };

      const embedder = TogetherAIEmbeddingFunction.buildFromConfig(config);

      expect(embedder.getConfig()).toEqual(config);
    });

    const generateEmbeddingsTest = "should generate embeddings";
    if (!process.env.TOGETHER_API_KEY) {
      it.skip(generateEmbeddingsTest, () => {});
    } else {
      it(generateEmbeddingsTest, async () => {
        const embedder = new TogetherAIEmbeddingFunction({ modelName: MODEL });
        const texts = ["Hello world", "Test text"];
        const embeddings = await embedder.generate(texts);

        expect(embeddings.length).toBe(texts.length);

        embeddings.forEach((embedding) => {
          expect(embedding.length).toBeGreaterThan(0);
        });

        expect(embeddings[0]).not.toEqual(embeddings[1]);
      });
    }
  }
});
