#!/usr/bin/env python3
"""
Agent Zero MCP Server
Provides integration between LM Studio and Agent Zero framework
"""

import asyncio
import json
import sys
import os
from typing import Any, Dict, List, Optional
from pathlib import Path

# Add the parent directory to the path to import agent zero modules
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from mcp.server import Server
    from mcp.types import (
        Resource,
        Tool,
        TextContent,
        ImageContent,
        EmbeddedResource,
        LoggingLevel
    )
    import mcp.server.stdio
except ImportError:
    print("MCP library not found. Install with: pip install mcp", file=sys.stderr)
    sys.exit(1)

# Initialize the MCP server
server = Server("agent-zero")

@server.list_resources()
async def list_resources() -> List[Resource]:
    """List available Agent Zero resources"""
    return [
        Resource(
            uri="agent-zero://status",
            name="Agent Zero Status",
            description="Current status of Agent Zero framework",
            mimeType="text/plain"
        ),
        Resource(
            uri="agent-zero://config",
            name="Agent Zero Configuration", 
            description="Current Agent Zero configuration",
            mimeType="application/json"
        ),
        Resource(
            uri="agent-zero://logs",
            name="Agent Zero Logs",
            description="Recent Agent Zero logs",
            mimeType="text/plain"
        )
    ]

@server.read_resource()
async def read_resource(uri: str) -> str:
    """Read Agent Zero resource content"""
    if uri == "agent-zero://status":
        return get_agent_zero_status()
    elif uri == "agent-zero://config":
        return get_agent_zero_config()
    elif uri == "agent-zero://logs":
        return get_agent_zero_logs()
    else:
        raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def list_tools() -> List[Tool]:
    """List available Agent Zero tools"""
    return [
        Tool(
            name="agent_zero_execute",
            description="Execute a command in Agent Zero framework",
            inputSchema={
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "Command to execute in Agent Zero"
                    },
                    "context": {
                        "type": "string", 
                        "description": "Optional context for the command"
                    }
                },
                "required": ["command"]
            }
        ),
        Tool(
            name="agent_zero_status",
            description="Get current Agent Zero status and health",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": []
            }
        ),
        Tool(
            name="agent_zero_restart",
            description="Restart Agent Zero services",
            inputSchema={
                "type": "object", 
                "properties": {
                    "force": {
                        "type": "boolean",
                        "description": "Force restart even if running"
                    }
                },
                "required": []
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Execute Agent Zero tools"""
    if name == "agent_zero_execute":
        result = execute_agent_zero_command(
            arguments.get("command", ""),
            arguments.get("context", "")
        )
        return [TextContent(type="text", text=result)]
    
    elif name == "agent_zero_status":
        status = get_agent_zero_status()
        return [TextContent(type="text", text=status)]
    
    elif name == "agent_zero_restart":
        force = arguments.get("force", False)
        result = restart_agent_zero(force)
        return [TextContent(type="text", text=result)]
    
    else:
        raise ValueError(f"Unknown tool: {name}")

def get_agent_zero_status() -> str:
    """Get current Agent Zero status"""
    try:
        # Check if Agent Zero is running
        agent_zero_dir = Path(__file__).parent.parent.parent
        
        status = {
            "status": "running",
            "directory": str(agent_zero_dir),
            "python_env": sys.executable,
            "version": "1.0.0"
        }
        
        return json.dumps(status, indent=2)
    except Exception as e:
        return f"Error getting status: {str(e)}"

def get_agent_zero_config() -> str:
    """Get Agent Zero configuration"""
    try:
        agent_zero_dir = Path(__file__).parent.parent.parent
        config_file = agent_zero_dir / "config.yaml"
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                return f.read()
        else:
            return "No config.yaml found"
    except Exception as e:
        return f"Error reading config: {str(e)}"

def get_agent_zero_logs() -> str:
    """Get recent Agent Zero logs"""
    try:
        agent_zero_dir = Path(__file__).parent.parent.parent
        log_dir = agent_zero_dir / "logs"
        
        if log_dir.exists():
            log_files = list(log_dir.glob("*.log"))
            if log_files:
                latest_log = max(log_files, key=lambda x: x.stat().st_mtime)
                with open(latest_log, 'r') as f:
                    lines = f.readlines()
                    return ''.join(lines[-50:])  # Last 50 lines
            else:
                return "No log files found"
        else:
            return "No logs directory found"
    except Exception as e:
        return f"Error reading logs: {str(e)}"

def execute_agent_zero_command(command: str, context: str = "") -> str:
    """Execute a command in Agent Zero"""
    try:
        # This is a placeholder - implement actual Agent Zero command execution
        return f"Executed command: {command}\nContext: {context}\nResult: Command executed successfully"
    except Exception as e:
        return f"Error executing command: {str(e)}"

def restart_agent_zero(force: bool = False) -> str:
    """Restart Agent Zero services"""
    try:
        # This is a placeholder - implement actual restart logic
        return f"Agent Zero restart {'forced' if force else 'requested'}: Success"
    except Exception as e:
        return f"Error restarting Agent Zero: {str(e)}"

async def main():
    """Main entry point for the MCP server"""
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            server.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
