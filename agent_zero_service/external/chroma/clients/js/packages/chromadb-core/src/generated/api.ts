/* eslint-disable */
// tslint:disable
/**
 * chroma-frontend
 *
 *
 * OpenAPI spec version: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator+.
 * https://github.com/karlvr/openapi-generator-plus
 * Do not edit the class manually.
 */

import { Configuration } from "./configuration";
import {
  BASE_PATH,
  COLLECTION_FORMATS,
  FetchAPI,
  FetchArgs,
  BaseAPI,
  RequiredError,
  defaultFetch,
} from "./runtime";
import { Api } from "./models";

export type FactoryFunction<T> = (
  configuration?: Configuration,
  basePath?: string,
  fetch?: FetchAPI,
) => T;

/**
 * ApiApi - fetch parameter creator
 * @export
 */
export const ApiApiFetchParamCreator = function (
  configuration?: Configuration,
) {
  return {
    /**
     * @summary Adds records to a collection.
     * @param {string} tenant
     * @param {string} database
     * @param {string} collectionId
     * @param {Api.AddCollectionRecordsPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionAdd(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.AddCollectionRecordsPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling collectionAdd.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling collectionAdd.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling collectionAdd.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling collectionAdd.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/add`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Retrieves the number of records in a collection.
     * @param {string} tenant <p>Tenant ID for the collection</p>
     * @param {string} database <p>Database containing this collection</p>
     * @param {string} collectionId <p>Collection ID whose records are counted</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionCount(
      tenant: string,
      database: string,
      collectionId: string,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling collectionCount.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling collectionCount.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling collectionCount.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/count`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Deletes records in a collection. Can filter by IDs or metadata.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>Collection ID</p>
     * @param {Api.DeleteCollectionRecordsPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionDelete(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.DeleteCollectionRecordsPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling collectionDelete.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling collectionDelete.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling collectionDelete.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling collectionDelete.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/delete`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Retrieves records from a collection by ID or metadata filter.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name for the collection</p>
     * @param {string} collectionId <p>Collection ID to fetch records from</p>
     * @param {Api.GetRequestPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionGet(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.GetRequestPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling collectionGet.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling collectionGet.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling collectionGet.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling collectionGet.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/get`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Query a collection in a variety of ways, including vector search, metadata filtering, and full-text search
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name containing the collection</p>
     * @param {string} collectionId <p>Collection ID to query</p>
     * @param {number} [limit] <p>Limit for pagination</p>
     * @param {number} [offset] <p>Offset for pagination</p>
     * @param {Api.QueryRequestPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionQuery(
      tenant: string,
      database: string,
      collectionId: string,
      limit: number | undefined,
      offset: number | undefined,
      request: Api.QueryRequestPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling collectionQuery.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling collectionQuery.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling collectionQuery.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling collectionQuery.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/query`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      if (limit !== undefined) {
        localVarQueryParameter.append("limit", String(limit));
      }

      if (offset !== undefined) {
        localVarQueryParameter.append("offset", String(offset));
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Updates records in a collection by ID.
     * @param {string} tenant
     * @param {string} database
     * @param {string} collectionId
     * @param {Api.UpdateCollectionRecordsPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionUpdate(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.UpdateCollectionRecordsPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling collectionUpdate.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling collectionUpdate.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling collectionUpdate.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling collectionUpdate.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/update`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Upserts records in a collection (create if not exists, otherwise update).
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>Collection ID</p>
     * @param {Api.UpsertCollectionRecordsPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionUpsert(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.UpsertCollectionRecordsPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling collectionUpsert.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling collectionUpsert.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling collectionUpsert.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling collectionUpsert.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/upsert`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Retrieves the total number of collections in a given database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name to count collections from</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    countCollections(
      tenant: string,
      database: string,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling countCollections.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling countCollections.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections_count`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Creates a new collection under the specified database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name containing the new collection</p>
     * @param {Api.CreateCollectionPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    createCollection(
      tenant: string,
      database: string,
      request: Api.CreateCollectionPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling createCollection.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling createCollection.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling createCollection.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Creates a new database for a given tenant.
     * @param {string} tenant <p>Tenant ID to associate with the new database</p>
     * @param {Api.CreateDatabasePayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    createDatabase(
      tenant: string,
      request: Api.CreateDatabasePayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling createDatabase.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling createDatabase.",
        );
      }
      let localVarPath = `/api/v2/tenants/{tenant}/databases`.replace(
        "{tenant}",
        encodeURIComponent(String(tenant)),
      );
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Creates a new tenant.
     * @param {Api.CreateTenantPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    createTenant(
      request: Api.CreateTenantPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling createTenant.",
        );
      }
      let localVarPath = `/api/v2/tenants`;
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Deletes a collection in a given database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>UUID of the collection to delete</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteCollection(
      tenant: string,
      database: string,
      collectionId: string,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling deleteCollection.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling deleteCollection.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling deleteCollection.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "DELETE" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Deletes a specific database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Name of the database to delete</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteDatabase(
      tenant: string,
      database: string,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling deleteDatabase.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling deleteDatabase.",
        );
      }
      let localVarPath = `/api/v2/tenants/{tenant}/databases/{database}`
        .replace("{tenant}", encodeURIComponent(String(tenant)))
        .replace("{database}", encodeURIComponent(String(database)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "DELETE" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Forks an existing collection.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>UUID of the collection to update</p>
     * @param {Api.ForkCollectionPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    forkCollection(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.ForkCollectionPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling forkCollection.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling forkCollection.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling forkCollection.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling forkCollection.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/fork`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Retrieves a collection by ID or name.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>UUID of the collection</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    getCollection(
      tenant: string,
      database: string,
      collectionId: string,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling getCollection.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling getCollection.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling getCollection.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Retrieves a specific database by name.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Name of the database to retrieve</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDatabase(
      tenant: string,
      database: string,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling getDatabase.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling getDatabase.",
        );
      }
      let localVarPath = `/api/v2/tenants/{tenant}/databases/{database}`
        .replace("{tenant}", encodeURIComponent(String(tenant)))
        .replace("{database}", encodeURIComponent(String(database)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Returns an existing tenant by name.
     * @param {string} tenantName <p>Tenant name or ID to retrieve</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    getTenant(tenantName: string, options: RequestInit = {}): FetchArgs {
      // verify required parameter 'tenantName' is not null or undefined
      if (tenantName === null || tenantName === undefined) {
        throw new RequiredError(
          "tenantName",
          "Required parameter tenantName was null or undefined when calling getTenant.",
        );
      }
      let localVarPath = `/api/v2/tenants/{tenant_name}`.replace(
        "{tenant_name}",
        encodeURIComponent(String(tenantName)),
      );
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Retrieves the current user's identity, tenant, and databases.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    getUserIdentity(options: RequestInit = {}): FetchArgs {
      let localVarPath = `/api/v2/auth/identity`;
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Health check endpoint that returns 200 if the server and executor are ready
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthcheck(options: RequestInit = {}): FetchArgs {
      let localVarPath = `/api/v2/healthcheck`;
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Heartbeat endpoint that returns a nanosecond timestamp of the current time.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    heartbeat(options: RequestInit = {}): FetchArgs {
      let localVarPath = `/api/v2/heartbeat`;
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Lists all collections in the specified database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name to list collections from</p>
     * @param {number} [limit] <p>Limit for pagination</p>
     * @param {number} [offset] <p>Offset for pagination</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    listCollections(
      tenant: string,
      database: string,
      limit: number | undefined,
      offset: number | undefined,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling listCollections.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling listCollections.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      if (limit !== undefined) {
        localVarQueryParameter.append("limit", String(limit));
      }

      if (offset !== undefined) {
        localVarQueryParameter.append("offset", String(offset));
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Lists all databases for a given tenant.
     * @param {string} tenant <p>Tenant ID to list databases for</p>
     * @param {number} [limit] <p>Limit for pagination</p>
     * @param {number} [offset] <p>Offset for pagination</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    listDatabases(
      tenant: string,
      limit: number | undefined,
      offset: number | undefined,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling listDatabases.",
        );
      }
      let localVarPath = `/api/v2/tenants/{tenant}/databases`.replace(
        "{tenant}",
        encodeURIComponent(String(tenant)),
      );
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      if (limit !== undefined) {
        localVarQueryParameter.append("limit", String(limit));
      }

      if (offset !== undefined) {
        localVarQueryParameter.append("offset", String(offset));
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Pre-flight checks endpoint reporting basic readiness info.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    preFlightChecks(options: RequestInit = {}): FetchArgs {
      let localVarPath = `/api/v2/pre-flight-checks`;
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Reset endpoint allowing authorized users to reset the database.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    reset(options: RequestInit = {}): FetchArgs {
      let localVarPath = `/api/v2/reset`;
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "POST" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Updates an existing collection's name or metadata.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>UUID of the collection to update</p>
     * @param {Api.UpdateCollectionPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateCollection(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.UpdateCollectionPayload,
      options: RequestInit = {},
    ): FetchArgs {
      // verify required parameter 'tenant' is not null or undefined
      if (tenant === null || tenant === undefined) {
        throw new RequiredError(
          "tenant",
          "Required parameter tenant was null or undefined when calling updateCollection.",
        );
      }
      // verify required parameter 'database' is not null or undefined
      if (database === null || database === undefined) {
        throw new RequiredError(
          "database",
          "Required parameter database was null or undefined when calling updateCollection.",
        );
      }
      // verify required parameter 'collectionId' is not null or undefined
      if (collectionId === null || collectionId === undefined) {
        throw new RequiredError(
          "collectionId",
          "Required parameter collectionId was null or undefined when calling updateCollection.",
        );
      }
      // verify required parameter 'request' is not null or undefined
      if (request === null || request === undefined) {
        throw new RequiredError(
          "request",
          "Required parameter request was null or undefined when calling updateCollection.",
        );
      }
      let localVarPath =
        `/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}`
          .replace("{tenant}", encodeURIComponent(String(tenant)))
          .replace("{database}", encodeURIComponent(String(database)))
          .replace("{collection_id}", encodeURIComponent(String(collectionId)));
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "PUT" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarHeaderParameter.set("Content-Type", "application/json");

      localVarRequestOptions.headers = localVarHeaderParameter;

      if (request !== undefined) {
        localVarRequestOptions.body = JSON.stringify(request || {});
      }

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
    /**
     * @summary Returns the version of the server.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    version(options: RequestInit = {}): FetchArgs {
      let localVarPath = `/api/v2/version`;
      const localVarPathQueryStart = localVarPath.indexOf("?");
      const localVarRequestOptions: RequestInit = Object.assign(
        { method: "GET" },
        options,
      );
      const localVarHeaderParameter: Headers = options.headers
        ? new Headers(options.headers)
        : new Headers();
      const localVarQueryParameter = new URLSearchParams(
        localVarPathQueryStart !== -1
          ? localVarPath.substring(localVarPathQueryStart + 1)
          : "",
      );
      if (localVarPathQueryStart !== -1) {
        localVarPath = localVarPath.substring(0, localVarPathQueryStart);
      }

      localVarRequestOptions.headers = localVarHeaderParameter;

      const localVarQueryParameterString = localVarQueryParameter.toString();
      if (localVarQueryParameterString) {
        localVarPath += "?" + localVarQueryParameterString;
      }
      return {
        url: localVarPath,
        options: localVarRequestOptions,
      };
    },
  };
};

/**
 * ApiApi - functional programming interface
 * @export
 */
export const ApiApiFp = function (configuration?: Configuration) {
  return {
    /**
     * @summary Adds records to a collection.
     * @param {string} tenant
     * @param {string} database
     * @param {string} collectionId
     * @param {Api.AddCollectionRecordsPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionAdd(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.AddCollectionRecordsPayload,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.AddCollectionRecordsResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).collectionAdd(tenant, database, collectionId, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 201) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 400) {
            return response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Retrieves the number of records in a collection.
     * @param {string} tenant <p>Tenant ID for the collection</p>
     * @param {string} database <p>Database containing this collection</p>
     * @param {string} collectionId <p>Collection ID whose records are counted</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionCount(
      tenant: string,
      database: string,
      collectionId: string,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<number> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).collectionCount(tenant, database, collectionId, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Deletes records in a collection. Can filter by IDs or metadata.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>Collection ID</p>
     * @param {Api.DeleteCollectionRecordsPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionDelete(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.DeleteCollectionRecordsPayload,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.DeleteCollectionRecordsResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).collectionDelete(tenant, database, collectionId, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Retrieves records from a collection by ID or metadata filter.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name for the collection</p>
     * @param {string} collectionId <p>Collection ID to fetch records from</p>
     * @param {Api.GetRequestPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionGet(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.GetRequestPayload,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.GetResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).collectionGet(tenant, database, collectionId, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Query a collection in a variety of ways, including vector search, metadata filtering, and full-text search
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name containing the collection</p>
     * @param {string} collectionId <p>Collection ID to query</p>
     * @param {number} [limit] <p>Limit for pagination</p>
     * @param {number} [offset] <p>Offset for pagination</p>
     * @param {Api.QueryRequestPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionQuery(
      tenant: string,
      database: string,
      collectionId: string,
      limit: number | undefined,
      offset: number | undefined,
      request: Api.QueryRequestPayload,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.QueryResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).collectionQuery(
        tenant,
        database,
        collectionId,
        limit,
        offset,
        request,
        options,
      );
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Updates records in a collection by ID.
     * @param {string} tenant
     * @param {string} database
     * @param {string} collectionId
     * @param {Api.UpdateCollectionRecordsPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionUpdate(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.UpdateCollectionRecordsPayload,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.UpdateCollectionRecordsResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).collectionUpdate(tenant, database, collectionId, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 404) {
            return response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Upserts records in a collection (create if not exists, otherwise update).
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>Collection ID</p>
     * @param {Api.UpsertCollectionRecordsPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    collectionUpsert(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.UpsertCollectionRecordsPayload,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.UpsertCollectionRecordsResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).collectionUpsert(tenant, database, collectionId, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Retrieves the total number of collections in a given database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name to count collections from</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    countCollections(
      tenant: string,
      database: string,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<number> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).countCollections(tenant, database, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Creates a new collection under the specified database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name containing the new collection</p>
     * @param {Api.CreateCollectionPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    createCollection(
      tenant: string,
      database: string,
      request: Api.CreateCollectionPayload,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.Collection> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).createCollection(tenant, database, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Creates a new database for a given tenant.
     * @param {string} tenant <p>Tenant ID to associate with the new database</p>
     * @param {Api.CreateDatabasePayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    createDatabase(
      tenant: string,
      request: Api.CreateDatabasePayload,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.CreateDatabaseResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).createDatabase(tenant, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Creates a new tenant.
     * @param {Api.CreateTenantPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    createTenant(
      request: Api.CreateTenantPayload,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.CreateTenantResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).createTenant(request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Deletes a collection in a given database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>UUID of the collection to delete</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteCollection(
      tenant: string,
      database: string,
      collectionId: string,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.UpdateCollectionResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).deleteCollection(tenant, database, collectionId, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Deletes a specific database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Name of the database to delete</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    deleteDatabase(
      tenant: string,
      database: string,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.DeleteDatabaseResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).deleteDatabase(tenant, database, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Forks an existing collection.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>UUID of the collection to update</p>
     * @param {Api.ForkCollectionPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    forkCollection(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.ForkCollectionPayload,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.Collection> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).forkCollection(tenant, database, collectionId, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Retrieves a collection by ID or name.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>UUID of the collection</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    getCollection(
      tenant: string,
      database: string,
      collectionId: string,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.Collection> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).getCollection(tenant, database, collectionId, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Retrieves a specific database by name.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Name of the database to retrieve</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    getDatabase(
      tenant: string,
      database: string,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.Database> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).getDatabase(tenant, database, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Returns an existing tenant by name.
     * @param {string} tenantName <p>Tenant name or ID to retrieve</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    getTenant(
      tenantName: string,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.GetTenantResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).getTenant(tenantName, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Retrieves the current user's identity, tenant, and databases.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    getUserIdentity(
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.GetUserIdentityResponse> {
      const localVarFetchArgs =
        ApiApiFetchParamCreator(configuration).getUserIdentity(options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Health check endpoint that returns 200 if the server and executor are ready
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    healthcheck(
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<string> {
      const localVarFetchArgs =
        ApiApiFetchParamCreator(configuration).healthcheck(options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 503) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Heartbeat endpoint that returns a nanosecond timestamp of the current time.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    heartbeat(
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.HeartbeatResponse> {
      const localVarFetchArgs =
        ApiApiFetchParamCreator(configuration).heartbeat(options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Lists all collections in the specified database.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name to list collections from</p>
     * @param {number} [limit] <p>Limit for pagination</p>
     * @param {number} [offset] <p>Offset for pagination</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    listCollections(
      tenant: string,
      database: string,
      limit: number | undefined,
      offset: number | undefined,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.Vec2[]> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).listCollections(tenant, database, limit, offset, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Lists all databases for a given tenant.
     * @param {string} tenant <p>Tenant ID to list databases for</p>
     * @param {number} [limit] <p>Limit for pagination</p>
     * @param {number} [offset] <p>Offset for pagination</p>
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    listDatabases(
      tenant: string,
      limit: number | undefined,
      offset: number | undefined,
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.Vec2[]> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).listDatabases(tenant, limit, offset, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Pre-flight checks endpoint reporting basic readiness info.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    preFlightChecks(
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<Api.ChecklistResponse> {
      const localVarFetchArgs =
        ApiApiFetchParamCreator(configuration).preFlightChecks(options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Reset endpoint allowing authorized users to reset the database.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    reset(
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<boolean> {
      const localVarFetchArgs =
        ApiApiFetchParamCreator(configuration).reset(options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Updates an existing collection's name or metadata.
     * @param {string} tenant <p>Tenant ID</p>
     * @param {string} database <p>Database name</p>
     * @param {string} collectionId <p>UUID of the collection to update</p>
     * @param {Api.UpdateCollectionPayload} request
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    updateCollection(
      tenant: string,
      database: string,
      collectionId: string,
      request: Api.UpdateCollectionPayload,
      options?: RequestInit,
    ): (
      fetch?: FetchAPI,
      basePath?: string,
    ) => Promise<Api.UpdateCollectionResponse> {
      const localVarFetchArgs = ApiApiFetchParamCreator(
        configuration,
      ).updateCollection(tenant, database, collectionId, request, options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          if (response.status === 401) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 404) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          if (response.status === 500) {
            if (mimeType === "application/json") {
              throw response;
            }
            throw response;
          }
          throw response;
        });
      };
    },
    /**
     * @summary Returns the version of the server.
     * @param {RequestInit} [options] Override http request option.
     * @throws {RequiredError}
     */
    version(
      options?: RequestInit,
    ): (fetch?: FetchAPI, basePath?: string) => Promise<string> {
      const localVarFetchArgs =
        ApiApiFetchParamCreator(configuration).version(options);
      return (fetch: FetchAPI = defaultFetch, basePath: string = BASE_PATH) => {
        return fetch(
          basePath + localVarFetchArgs.url,
          localVarFetchArgs.options,
        ).then((response) => {
          const contentType = response.headers.get("Content-Type");
          const mimeType = contentType
            ? contentType.replace(/;.*/, "")
            : undefined;

          if (response.status === 200) {
            if (mimeType === "application/json") {
              return response.json() as any;
            }
            throw response;
          }
          throw response;
        });
      };
    },
  };
};

/**
 * ApiApi - factory interface
 * @export
 */
export const ApiApiFactory: FactoryFunction<ApiApi> = function (
  configuration?: Configuration,
  basePath?: string,
  fetch?: FetchAPI,
) {
  return new ApiApi(configuration, basePath, fetch);
};

/**
 * ApiApi - object-oriented interface
 * @export
 * @class ApiApi
 * @extends {BaseAPI}
 */
export class ApiApi extends BaseAPI {
  /**
   * @summary Adds records to a collection.
   * @param {string} tenant
   * @param {string} database
   * @param {string} collectionId
   * @param {Api.AddCollectionRecordsPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public collectionAdd(
    tenant: string,
    database: string,
    collectionId: string,
    request: Api.AddCollectionRecordsPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).collectionAdd(
      tenant,
      database,
      collectionId,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Retrieves the number of records in a collection.
   * @param {string} tenant <p>Tenant ID for the collection</p>
   * @param {string} database <p>Database containing this collection</p>
   * @param {string} collectionId <p>Collection ID whose records are counted</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public collectionCount(
    tenant: string,
    database: string,
    collectionId: string,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).collectionCount(
      tenant,
      database,
      collectionId,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Deletes records in a collection. Can filter by IDs or metadata.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name</p>
   * @param {string} collectionId <p>Collection ID</p>
   * @param {Api.DeleteCollectionRecordsPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public collectionDelete(
    tenant: string,
    database: string,
    collectionId: string,
    request: Api.DeleteCollectionRecordsPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).collectionDelete(
      tenant,
      database,
      collectionId,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Retrieves records from a collection by ID or metadata filter.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name for the collection</p>
   * @param {string} collectionId <p>Collection ID to fetch records from</p>
   * @param {Api.GetRequestPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public collectionGet(
    tenant: string,
    database: string,
    collectionId: string,
    request: Api.GetRequestPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).collectionGet(
      tenant,
      database,
      collectionId,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Query a collection in a variety of ways, including vector search, metadata filtering, and full-text search
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name containing the collection</p>
   * @param {string} collectionId <p>Collection ID to query</p>
   * @param {number} [limit] <p>Limit for pagination</p>
   * @param {number} [offset] <p>Offset for pagination</p>
   * @param {Api.QueryRequestPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public collectionQuery(
    tenant: string,
    database: string,
    collectionId: string,
    limit: number | undefined,
    offset: number | undefined,
    request: Api.QueryRequestPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).collectionQuery(
      tenant,
      database,
      collectionId,
      limit,
      offset,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Updates records in a collection by ID.
   * @param {string} tenant
   * @param {string} database
   * @param {string} collectionId
   * @param {Api.UpdateCollectionRecordsPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public collectionUpdate(
    tenant: string,
    database: string,
    collectionId: string,
    request: Api.UpdateCollectionRecordsPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).collectionUpdate(
      tenant,
      database,
      collectionId,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Upserts records in a collection (create if not exists, otherwise update).
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name</p>
   * @param {string} collectionId <p>Collection ID</p>
   * @param {Api.UpsertCollectionRecordsPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public collectionUpsert(
    tenant: string,
    database: string,
    collectionId: string,
    request: Api.UpsertCollectionRecordsPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).collectionUpsert(
      tenant,
      database,
      collectionId,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Retrieves the total number of collections in a given database.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name to count collections from</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public countCollections(
    tenant: string,
    database: string,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).countCollections(
      tenant,
      database,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Creates a new collection under the specified database.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name containing the new collection</p>
   * @param {Api.CreateCollectionPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public createCollection(
    tenant: string,
    database: string,
    request: Api.CreateCollectionPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).createCollection(
      tenant,
      database,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Creates a new database for a given tenant.
   * @param {string} tenant <p>Tenant ID to associate with the new database</p>
   * @param {Api.CreateDatabasePayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public createDatabase(
    tenant: string,
    request: Api.CreateDatabasePayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).createDatabase(
      tenant,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Creates a new tenant.
   * @param {Api.CreateTenantPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public createTenant(request: Api.CreateTenantPayload, options?: RequestInit) {
    return ApiApiFp(this.configuration).createTenant(request, options)(
      this.fetch,
      this.basePath,
    );
  }

  /**
   * @summary Deletes a collection in a given database.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name</p>
   * @param {string} collectionId <p>UUID of the collection to delete</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public deleteCollection(
    tenant: string,
    database: string,
    collectionId: string,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).deleteCollection(
      tenant,
      database,
      collectionId,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Deletes a specific database.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Name of the database to delete</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public deleteDatabase(
    tenant: string,
    database: string,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).deleteDatabase(
      tenant,
      database,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Forks an existing collection.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name</p>
   * @param {string} collectionId <p>UUID of the collection to update</p>
   * @param {Api.ForkCollectionPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public forkCollection(
    tenant: string,
    database: string,
    collectionId: string,
    request: Api.ForkCollectionPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).forkCollection(
      tenant,
      database,
      collectionId,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Retrieves a collection by ID or name.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name</p>
   * @param {string} collectionId <p>UUID of the collection</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public getCollection(
    tenant: string,
    database: string,
    collectionId: string,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).getCollection(
      tenant,
      database,
      collectionId,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Retrieves a specific database by name.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Name of the database to retrieve</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public getDatabase(tenant: string, database: string, options?: RequestInit) {
    return ApiApiFp(this.configuration).getDatabase(
      tenant,
      database,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Returns an existing tenant by name.
   * @param {string} tenantName <p>Tenant name or ID to retrieve</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public getTenant(tenantName: string, options?: RequestInit) {
    return ApiApiFp(this.configuration).getTenant(tenantName, options)(
      this.fetch,
      this.basePath,
    );
  }

  /**
   * @summary Retrieves the current user's identity, tenant, and databases.
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public getUserIdentity(options?: RequestInit) {
    return ApiApiFp(this.configuration).getUserIdentity(options)(
      this.fetch,
      this.basePath,
    );
  }

  /**
   * @summary Health check endpoint that returns 200 if the server and executor are ready
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public healthcheck(options?: RequestInit) {
    return ApiApiFp(this.configuration).healthcheck(options)(
      this.fetch,
      this.basePath,
    );
  }

  /**
   * @summary Heartbeat endpoint that returns a nanosecond timestamp of the current time.
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public heartbeat(options?: RequestInit) {
    return ApiApiFp(this.configuration).heartbeat(options)(
      this.fetch,
      this.basePath,
    );
  }

  /**
   * @summary Lists all collections in the specified database.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name to list collections from</p>
   * @param {number} [limit] <p>Limit for pagination</p>
   * @param {number} [offset] <p>Offset for pagination</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public listCollections(
    tenant: string,
    database: string,
    limit: number | undefined,
    offset: number | undefined,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).listCollections(
      tenant,
      database,
      limit,
      offset,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Lists all databases for a given tenant.
   * @param {string} tenant <p>Tenant ID to list databases for</p>
   * @param {number} [limit] <p>Limit for pagination</p>
   * @param {number} [offset] <p>Offset for pagination</p>
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public listDatabases(
    tenant: string,
    limit: number | undefined,
    offset: number | undefined,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).listDatabases(
      tenant,
      limit,
      offset,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Pre-flight checks endpoint reporting basic readiness info.
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public preFlightChecks(options?: RequestInit) {
    return ApiApiFp(this.configuration).preFlightChecks(options)(
      this.fetch,
      this.basePath,
    );
  }

  /**
   * @summary Reset endpoint allowing authorized users to reset the database.
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public reset(options?: RequestInit) {
    return ApiApiFp(this.configuration).reset(options)(
      this.fetch,
      this.basePath,
    );
  }

  /**
   * @summary Updates an existing collection's name or metadata.
   * @param {string} tenant <p>Tenant ID</p>
   * @param {string} database <p>Database name</p>
   * @param {string} collectionId <p>UUID of the collection to update</p>
   * @param {Api.UpdateCollectionPayload} request
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public updateCollection(
    tenant: string,
    database: string,
    collectionId: string,
    request: Api.UpdateCollectionPayload,
    options?: RequestInit,
  ) {
    return ApiApiFp(this.configuration).updateCollection(
      tenant,
      database,
      collectionId,
      request,
      options,
    )(this.fetch, this.basePath);
  }

  /**
   * @summary Returns the version of the server.
   * @param {RequestInit} [options] Override http request option.
   * @throws {RequiredError}
   */
  public version(options?: RequestInit) {
    return ApiApiFp(this.configuration).version(options)(
      this.fetch,
      this.basePath,
    );
  }
}

/**
 * We sometimes represent dates as strings (in models) and as Dates (in parameters) so this
 * function converts them both to a string.
 */
function dateToString(value: Date | string | undefined): string | undefined {
  if (value instanceof Date) {
    return value.toISOString();
  } else if (typeof value === "string") {
    return value;
  } else {
    return undefined;
  }
}
