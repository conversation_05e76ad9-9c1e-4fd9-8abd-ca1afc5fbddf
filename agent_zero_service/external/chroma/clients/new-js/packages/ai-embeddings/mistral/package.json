{"name": "@chroma-core/mistral", "version": "0.1.7", "private": false, "description": "Voyage AI embedding provider for Chroma", "main": "dist/cjs/mistral.cjs", "types": "dist/mistral.d.ts", "module": "dist/mistral.legacy-esm.js", "type": "module", "exports": {".": {"import": {"types": "./dist/mistral.d.ts", "default": "./dist/mistral.mjs"}, "require": {"types": "./dist/cjs/mistral.d.cts", "default": "./dist/cjs/mistral.cjs"}}}, "files": ["src", "dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup", "watch": "tsup --watch", "test": "jest"}, "devDependencies": {"@jest/globals": "^29.7.0", "dotenv": "^16.3.1", "jest": "^29.7.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsup": "^8.3.5"}, "peerDependencies": {"chromadb": "workspace:^"}, "dependencies": {"@chroma-core/ai-embeddings-common": "workspace:^", "@mistralai/mistralai": "^1.7.1"}, "engines": {"node": ">=20"}, "publishConfig": {"access": "public"}}