{
  "git.ignoreLimitWarning": true,
  "editor.rulers": [
    79
  ],
  "editor.formatOnSave": true,
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": [
    "--line-length",
    "79"
  ],
  "files.exclude": {
    "**/__pycache__": true,
    "**/.ipynb_checkpoints": true,
    "**/.pytest_cache": true,
    "**/chroma.egg-info": true
  },
  "python.analysis.typeCheckingMode": "basic",
  "python.linting.flake8Enabled": true,
  "python.linting.enabled": true,
  "python.linting.flake8Args": [
    "--extend-ignore=E203",
    "--extend-ignore=E501",
    "--extend-ignore=E503",
    "--max-line-length=88"
  ],
  "python.testing.pytestArgs": [
    "."
  ],
  "python.testing.unittestEnabled": false,
  "python.testing.pytestEnabled": true,
  "editor.formatOnPaste": true,
  "python.linting.mypyEnabled": true,
  "python.linting.mypyCategorySeverity.note": "Error",
  "python.linting.mypyArgs": [
    "--follow-imports=silent",
    "--ignore-missing-imports",
    "--show-column-numbers",
    "--no-pretty",
    "--strict",
    "--disable-error-code=type-abstract"
  ],
  "protoc": {
    "options": [
      "--proto_path=idl/",
    ]
  },
  "rust-analyzer.cargo.buildScripts.enable": true,
  "files.associations": {
    "fstream": "cpp",
    "iosfwd": "cpp",
    "__hash_table": "cpp",
    "__locale": "cpp",
    "atomic": "cpp",
    "deque": "cpp",
    "filesystem": "cpp",
    "future": "cpp",
    "locale": "cpp",
    "random": "cpp",
    "regex": "cpp",
    "string": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "unordered_map": "cpp",
    "valarray": "cpp",
    "variant": "cpp",
    "vector": "cpp",
    "__string": "cpp",
    "istream": "cpp",
    "memory": "cpp",
    "optional": "cpp",
    "string_view": "cpp",
    "__bit_reference": "cpp",
    "__bits": "cpp",
    "__config": "cpp",
    "__debug": "cpp",
    "__errc": "cpp",
    "__mutex_base": "cpp",
    "__node_handle": "cpp",
    "__nullptr": "cpp",
    "__split_buffer": "cpp",
    "__threading_support": "cpp",
    "__tree": "cpp",
    "__tuple": "cpp",
    "array": "cpp",
    "bit": "cpp",
    "bitset": "cpp",
    "cctype": "cpp",
    "charconv": "cpp",
    "chrono": "cpp",
    "cinttypes": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "compare": "cpp",
    "complex": "cpp",
    "concepts": "cpp",
    "condition_variable": "cpp",
    "csignal": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cstring": "cpp",
    "ctime": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "exception": "cpp",
    "format": "cpp",
    "forward_list": "cpp",
    "initializer_list": "cpp",
    "iomanip": "cpp",
    "ios": "cpp",
    "iostream": "cpp",
    "limits": "cpp",
    "list": "cpp",
    "map": "cpp",
    "mutex": "cpp",
    "new": "cpp",
    "numeric": "cpp",
    "ostream": "cpp",
    "queue": "cpp",
    "ratio": "cpp",
    "set": "cpp",
    "sstream": "cpp",
    "stack": "cpp",
    "stdexcept": "cpp",
    "streambuf": "cpp",
    "system_error": "cpp",
    "typeindex": "cpp",
    "typeinfo": "cpp",
    "unordered_set": "cpp",
    "algorithm": "cpp"
  },
}
