{"name": "example-node", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "node app.js", "start": "node app.js", "dev:bundled": "node app-bundled.js", "dev:client": "node app-client.js", "rebuild": "cd ../.. && pnpm build && cd examples/node && rm -rf node_modules && pnpm install && pnpm dev"}, "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.1.1", "chromadb-client": "link:../../packages/chromadb-client", "chromadb-default-embed": "^2.13.2", "cohere-ai": "^5.0.2", "express": "^4.18.2", "openai": "^3.1.0"}}