"""
CSV Parser Utility for Agent Zero
Handles CSV file parsing and data manipulation
"""

import csv
import json
from typing import List, Dict, Any, Optional
from io import StringIO
import pandas as pd


class CSVParser:
    """Advanced CSV parsing utility for Agent Zero"""
    
    def __init__(self):
        self.data = []
        self.headers = []
        
    def parse_csv_string(self, csv_string: str, delimiter: str = ',') -> Dict[str, Any]:
        """Parse CSV from string"""
        try:
            csv_file = StringIO(csv_string)
            reader = csv.DictReader(csv_file, delimiter=delimiter)
            
            self.headers = reader.fieldnames or []
            self.data = list(reader)
            
            return {
                'success': True,
                'rows': len(self.data),
                'columns': len(self.headers),
                'headers': self.headers,
                'data': self.data[:5]  # Preview first 5 rows
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def parse_csv_file(self, file_path: str, delimiter: str = ',') -> Dict[str, Any]:
        """Parse CSV from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return self.parse_csv_string(file.read(), delimiter)
                
        except Exception as e:
            return {
                'success': False,
                'error': f"File error: {str(e)}"
            }
    
    def filter_data(self, column: str, value: Any) -> List[Dict[str, Any]]:
        """Filter data by column value"""
        return [row for row in self.data if row.get(column) == str(value)]
    
    def get_column_values(self, column: str) -> List[str]:
        """Get all values from a specific column"""
        return [row.get(column, '') for row in self.data]
    
    def get_unique_values(self, column: str) -> List[str]:
        """Get unique values from a column"""
        return list(set(self.get_column_values(column)))
    
    def to_json(self) -> str:
        """Convert data to JSON"""
        return json.dumps(self.data, indent=2)
    
    def to_pandas(self) -> Optional[Any]:
        """Convert to pandas DataFrame if available"""
        try:
            return pd.DataFrame(self.data)
        except ImportError:
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """Get basic statistics about the data"""
        if not self.data:
            return {'error': 'No data loaded'}
            
        stats = {
            'total_rows': len(self.data),
            'total_columns': len(self.headers),
            'headers': self.headers,
            'column_stats': {}
        }
        
        for header in self.headers:
            values = self.get_column_values(header)
            non_empty = [v for v in values if v.strip()]
            
            stats['column_stats'][header] = {
                'total_values': len(values),
                'non_empty_values': len(non_empty),
                'unique_values': len(set(non_empty)),
                'sample_values': list(set(non_empty))[:5]
            }
        
        return stats


def quick_csv_parse(csv_string: str, delimiter: str = ',') -> Dict[str, Any]:
    """Quick function to parse CSV string"""
    parser = CSVParser()
    return parser.parse_csv_string(csv_string, delimiter)


def csv_to_json(csv_string: str, delimiter: str = ',') -> str:
    """Convert CSV string directly to JSON"""
    parser = CSVParser()
    result = parser.parse_csv_string(csv_string, delimiter)
    
    if result['success']:
        return parser.to_json()
    else:
        return json.dumps({'error': result['error']})


# Example usage for Agent Zero
if __name__ == "__main__":
    # Test data
    test_csv = """name,age,city
John,25,New York
Jane,30,London
Bob,35,Paris"""
    
    parser = CSVParser()
    result = parser.parse_csv_string(test_csv)
    print("Parse result:", result)
    
    if result['success']:
        print("Stats:", parser.get_stats())
        print("Unique cities:", parser.get_unique_values('city'))
        print("JSON output:", parser.to_json())
