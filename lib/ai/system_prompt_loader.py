"""
System Prompt Loader for Agent Zero
Integrates professional system prompts from various AI tools
"""

import os
import json
from typing import Dict, List, Optional, Any
from pathlib import Path


class SystemPromptLoader:
    """Load and manage system prompts from the prompt collection"""
    
    def __init__(self, prompt_base_dir: str = "prompt/system-prompts-and-models-of-ai-tools-main"):
        self.base_dir = Path(prompt_base_dir)
        self.prompts_cache = {}
        self.available_tools = [
            "v0 Prompts and Tools",
            "Cursor Prompts", 
            "Devin AI",
            "Lovable",
            "Windsurf",
            "VSCode Agent",
            "Replit",
            "Same.dev",
            "Trae",
            "Manus Agent Tools & Prompt",
            "Open Source prompts"
        ]
    
    def load_prompt_file(self, tool_name: str, file_name: str) -> Optional[str]:
        """Load a specific prompt file from a tool directory"""
        try:
            tool_path = self.base_dir / tool_name
            if not tool_path.exists():
                return None
                
            # Try different file extensions
            for ext in ['.md', '.txt', '.json', '.py']:
                file_path = tool_path / f"{file_name}{ext}"
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Cache the prompt
                    cache_key = f"{tool_name}:{file_name}"
                    self.prompts_cache[cache_key] = content
                    return content
                    
            return None
            
        except Exception as e:
            print(f"Error loading prompt {tool_name}/{file_name}: {e}")
            return None
    
    def get_v0_system_prompt(self) -> Optional[str]:
        """Get v0's system prompt for UI generation"""
        return self.load_prompt_file("v0 Prompts and Tools", "system_prompt")
    
    def get_cursor_system_prompt(self) -> Optional[str]:
        """Get Cursor's system prompt for code assistance"""
        return self.load_prompt_file("Cursor Prompts", "system_prompt")
    
    def get_devin_system_prompt(self) -> Optional[str]:
        """Get Devin's system prompt for software engineering"""
        return self.load_prompt_file("Devin AI", "system_prompt")
    
    def get_windsurf_system_prompt(self) -> Optional[str]:
        """Get Windsurf's system prompt for development"""
        return self.load_prompt_file("Windsurf", "system_prompt")
    
    def list_available_prompts(self, tool_name: str) -> List[str]:
        """List all available prompt files for a specific tool"""
        try:
            tool_path = self.base_dir / tool_name
            if not tool_path.exists():
                return []
                
            files = []
            for file_path in tool_path.iterdir():
                if file_path.is_file() and file_path.suffix in ['.md', '.txt', '.json', '.py']:
                    files.append(file_path.stem)
            
            return sorted(files)
            
        except Exception as e:
            print(f"Error listing prompts for {tool_name}: {e}")
            return []
    
    def search_prompts(self, keyword: str) -> Dict[str, List[str]]:
        """Search for prompts containing a specific keyword"""
        results = {}
        
        for tool in self.available_tools:
            tool_results = []
            prompts = self.list_available_prompts(tool)
            
            for prompt_name in prompts:
                content = self.load_prompt_file(tool, prompt_name)
                if content and keyword.lower() in content.lower():
                    tool_results.append(prompt_name)
            
            if tool_results:
                results[tool] = tool_results
        
        return results
    
    def create_agent_zero_prompt(self, base_tool: str = "Cursor Prompts", 
                                 task_type: str = "coding") -> str:
        """Create a custom Agent Zero prompt based on professional tools"""
        
        base_prompt = self.load_prompt_file(base_tool, "system_prompt") or ""
        
        agent_zero_additions = f"""

# Agent Zero Enhanced System Prompt
# Based on {base_tool} with Agent Zero capabilities

## Core Capabilities:
- Advanced code generation and analysis
- Web browsing and automation
- File system operations
- API integrations
- Multi-modal processing
- Task scheduling and orchestration

## OpenRouter + DeepSeek Integration:
- Using DeepSeek models via OpenRouter for optimal performance
- Cost-effective AI operations
- Advanced reasoning capabilities

## Available Tools:
- Browser automation (lib/browser/)
- Data processing (lib/data/)
- AI utilities (lib/ai/)
- System prompts from professional tools

## Task Type: {task_type}

{base_prompt}

## Agent Zero Specific Instructions:
1. Always provide working, tested code
2. Use available libraries from lib/ directory
3. Leverage system prompts for best practices
4. Integrate with OpenRouter/DeepSeek for advanced reasoning
5. Maintain Dutch language support when requested
6. Follow Leon's development preferences (minimal comments, direct usable code)
"""
        
        return agent_zero_additions
    
    def get_tool_specific_prompt(self, tool_name: str, task_description: str) -> str:
        """Get a tool-specific prompt for a particular task"""
        
        tool_mapping = {
            "ui_generation": "v0 Prompts and Tools",
            "code_editing": "Cursor Prompts",
            "software_engineering": "Devin AI", 
            "app_building": "Lovable",
            "development_environment": "Windsurf",
            "code_assistance": "VSCode Agent"
        }
        
        selected_tool = tool_mapping.get(task_description.lower(), "Cursor Prompts")
        base_prompt = self.load_prompt_file(selected_tool, "system_prompt") or ""
        
        return f"""
# Task: {task_description}
# Using: {selected_tool} methodology

{base_prompt}

## Agent Zero Integration:
- Task: {task_description}
- Available resources: lib/, prompt/, cag/, augmented/
- OpenRouter/DeepSeek backend
- Dutch language support available
"""

    def export_prompts_summary(self) -> Dict[str, Any]:
        """Export a summary of all available prompts"""
        summary = {
            "total_tools": len(self.available_tools),
            "tools": {},
            "cache_size": len(self.prompts_cache)
        }
        
        for tool in self.available_tools:
            prompts = self.list_available_prompts(tool)
            summary["tools"][tool] = {
                "prompt_count": len(prompts),
                "prompts": prompts
            }
        
        return summary


# Helper functions for Agent Zero integration
def get_professional_prompt(tool_name: str, task_type: str = "coding") -> str:
    """Quick function to get a professional prompt for Agent Zero"""
    loader = SystemPromptLoader()
    return loader.create_agent_zero_prompt(tool_name, task_type)


def search_professional_prompts(keyword: str) -> Dict[str, List[str]]:
    """Search through all professional prompts"""
    loader = SystemPromptLoader()
    return loader.search_prompts(keyword)


# Example usage
if __name__ == "__main__":
    loader = SystemPromptLoader()
    
    # Get summary
    summary = loader.export_prompts_summary()
    print("Available tools:", summary["tools"].keys())
    
    # Create Agent Zero prompt
    prompt = loader.create_agent_zero_prompt("Cursor Prompts", "web_development")
    print("\nGenerated prompt preview:")
    print(prompt[:500] + "...")
