{"name": "chromadb-client", "version": "2.4.7", "description": "A JavaScript interface for chroma with embedding functions as peer dependencies", "license": "Apache-2.0", "type": "module", "main": "dist/cjs/chromadb-client.cjs", "types": "dist/chromadb-client.d.ts", "module": "dist/chromadb-client.legacy-esm.js", "exports": {".": {"import": {"types": "./dist/chromadb-client.d.ts", "default": "./dist/chromadb-client.mjs"}, "require": {"types": "./dist/cjs/chromadb-client.d.cts", "default": "./dist/cjs/chromadb-client.cjs"}}}, "files": ["src", "dist"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "NODE_OPTIONS=--enable-source-maps tsup", "watch": "NODE_OPTIONS=--enable-source-maps tsup --watch", "test": "jest --runInBand"}, "dependencies": {"cliui": "^8.0.1", "isomorphic-fetch": "^3.0.0"}, "peerDependencies": {"@google/generative-ai": "^0.1.1", "@xenova/transformers": "^2.17.2", "chromadb-default-embed": "^2.14.0", "cohere-ai": "^5.0.0 || ^6.0.0 || ^7.0.0", "openai": "^3.0.0 || ^4.0.0", "voyageai": "^0.0.3-1", "ollama": "^0.5.0"}, "peerDependenciesMeta": {"@google/generative-ai": {"optional": true}, "@xenova/transformers": {"optional": true}, "chromadb-default-embed": {"optional": true}, "cohere-ai": {"optional": true}, "openai": {"optional": true}, "voyageai": {"optional": true}, "ollama": {"optional": true}}, "devDependencies": {"@internal/chromadb-core": "workspace:*", "jest": "^29.5.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.0", "tsup": "^7.2.0", "typescript": "^5.0.4"}, "engines": {"node": ">=14.17.0"}}