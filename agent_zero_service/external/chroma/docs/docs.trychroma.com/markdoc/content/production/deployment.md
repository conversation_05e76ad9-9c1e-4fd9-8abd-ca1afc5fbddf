# Deployment

{% Banner type="tip" %}

**Hosted Chroma**

Chroma Cloud, our fully managed hosted service is here. [Sign up here](https://trychroma.com/signup) for early access.

{% /Banner %}

{% Banner type="tip" %}

If you are using Chroma in production, please fill out [this form](https://airtable.com/appqd02UuQXCK5AuY/pagr1D0NFQoNpUpNZ/form), and we will add you to a dedicated Slack workspace for supporting production users.
This is the best place to

1. Get support with building with Chroma in prod.
2. Stay up-to-date with exciting new features.
3. Get swag!

We would love to help you think through the design of your system, or if you would be a good fit for our upcoming distributed cloud service.

{% /Banner %}

You can run Chroma single-node in [client/server mode](./chroma-server/client-server-mode), and easily deploy it. In this section, we also show you how to make sure your Chroma server is secure and reliable, and how to understand its performance at scale.


### Containers
* [Docker](./containers/docker)
* Kubernetes - Coming Soon!

### Cloud Providers

* [AWS](./cloud-providers/aws)
* [GCP](./cloud-providers/gcp)
* [Azure](./cloud-providers/azure)

***

### Administration

Running a server in production requires a few additional steps to ensure the server is secure and reliable.

* [Performance](./administration/performance)
* [Observability](./administration/observability)
* [Migration](./administration/migration)
