{"cells": [{"cell_type": "markdown", "id": "eae631e46b4c1115", "metadata": {"collapsed": false}, "source": ["# Chroma Authentication\n", "\n", "This tutorial aims to explain how authentication can be setup in Chroma.\n", "\n", "> **Important**: The concept of authentication is only applicable to Client/Server deployments. If you are using Chroma in a standalone mode, authentication is not applicable.\n", "\n", "## Concepts\n", "\n", "### Architecture Overview\n", "\n", "![Authentication Architecture](assets/auth-architecture.png \"Authentication Architecture\")\n", "\n", "### Authentication Flow (Sequence)\n", "\n", "The authentication sequence is applied for every request. It is important to understand that credential computation or retrieval (e.g. from external auth providers) is only done once for the first authenticated request. Subsequent requests will use the same credentials.\n", "\n", "The authentication flow is as follows:\n", "\n", "![Authentication Flow](assets/auh-sequence.png \"Authentication Flow\")\n", "\n", "### Preemptive Authentication\n", "\n", "In its current release the authentication in Chroma works in a preemptive mode. This means that the client is responsible for sending the authentication information on every request. The server will not challenge the client for authentication.\n", "\n", "> **Warning**: There are security risks involved with preemptive authentication in that the client might unintentionally send credentials to malicious or unintended server. When deploying authentication users are encouraged to use HTTPS (always verify server certs), to use secure providers (e.g. JWT) \n", "> and apply good security practices.\n", "\n", "### Authentication Provider\n", "\n", "Authentication in Chroma is handled by Authentication Providers. Providers are pluggable modules that allow Chroma to abstract the authentication mechanism from the rest of the system.\n", "\n", "Chroma ships with the following build-in providers:\n", "- Basic authentication\n", "- Token-based authentication\n", "\n", "### Client-side Authentication\n", "\n", "Client-side authentication refers to the process of preparing and communicating credentials information on the client-side and sending that information the Chroma server.\n", "\n", "### Server-side Authentication and Authorization\n", "\n", "- Server-side *authentication* means validating a request's credentials and identifying the user who sent it.\n", "- Server-side *authorization* means checking whether the identity associated with a request is allowed to make the request. This depends on authentication, since it depends on knowing the identity of the requestor.\n"]}, {"cell_type": "markdown", "id": "87d45f79aed65e21", "metadata": {"collapsed": false}, "source": ["## Configuration\n", "\n", "### Server Configuration\n", "\n", "In order for the server to provide authentication and authorization., it needs several pieces of information.\n", "\n", "- `CHROMA_SERVER_AUTHN_PROVIDER` - Configures which authentication provider class to use. In this case we are using `chromadb.auth.basic_authn.BasicAuthenticationServerProvider`. This authentication provider expects requests to have a header of the form `{\"Authorization\": \"Basic <base64-encoded username:password>\"}`.\n", "- `CHROMA_SERVER_AUTHN_CREDENTIALS_FILE` - The path to the credentials file for the authentication provider. As you'll see below, we will put these credentials in `./server.htpasswd`.\n", "- `CHROMA_SERVER_AUTHN_CREDENTIALS` - Instead of a file, you can pass authentication configuration directly. We do not recommend this but it is suitable for testing and certain deployment environments.\n", "\n", "### Client Configuration\n", "\n", "Similarly on the client side we need to provide the following configuration parameters:\n", "\n", "- `CHROMA_CLIENT_AUTH_PROVIDER` - Configures which authentication provider class to use. In this case we are using `chromadb.auth.basic_authn.BasicAuthClientProvider`, which is built to work well with the server-side `BasicAuthenticationServerProvider`.\n", "- `CHROMA_CLIENT_AUTH_CREDENTIALS` - Auth credentials to be included as headers in each request to Chroma. In this case we are using `admin:admin`.\n"]}, {"cell_type": "markdown", "id": "af49d8c78f2f7347", "metadata": {"collapsed": false}, "source": ["## Setting Up\n", "\n", "### Before You Begin\n", "\n", "Make sure you have either `chromadb` or `chromadb-client` installed. You can do that by running the following command:\n", "\n", "```bash\n", "pip install chromadb\n", "```\n", "or\n", "\n", "```bash\n", "pip install chromadb-client\n", "```\n", "\n", "Make sure Chroma Server is running. Use one of the following methods to start the server:\n", "\n", "From the command line:\n", "\n", "> Note: The below options will configure the server to use Basic Authentication with the username `admin` and password `admin`.\n", "\n", "```bash\n", "export CHROMA_USER=admin\n", "export CHROMA_PASSWORD=admin\n", "docker run --rm --entrypoint htpasswd httpd:2 -Bbn ${CHROMA_USER} ${CHROMA_PASSWORD} > server.htpasswd\n", "CHROMA_SERVER_AUTHN_CREDENTIALS_FILE=\"./server.htpasswd\" \\\n", "CHROMA_SERVER_AUTHN_PROVIDER=\"chromadb.auth.basic_authn.BasicAuthenticationServerProvider\" \\\n", "uvicorn chromadb.app:app --workers 1 --host 0.0.0.0 --port 8000  --proxy-headers --log-config log_config.yml\n", "```\n", "\n", "With <PERSON>er Compose:\n", "\n", "> Note: You need to clone the git repository first and run the command from the repository root.\n", "\n", "```bash\n", "export CHROMA_USER=admin\n", "export CHROMA_PASSWORD=admin\n", "docker run --rm --entrypoint htpasswd httpd:2 -Bbn ${CHROMA_USER} ${CHROMA_PASSWORD} > server.htpasswd\n", "cat << EOF > .env\n", "CHROMA_SERVER_AUTH_CREDENTIALS_FILE=\"/chroma/server.htpasswd\"\n", "CHROMA_SERVER_AUTH_PROVIDER=\"chromadb.auth.basic_authn.BasicAuthenticationServerProvider\"\n", "EOF\n", "docker-compose up -d --build \n", "```\n"]}, {"cell_type": "markdown", "id": "fc77d909233f2645", "metadata": {"collapsed": false}, "source": ["## Basic Authentication"]}, {"cell_type": "code", "execution_count": 2, "id": "8f9307acce25f672", "metadata": {"ExecuteTime": {"end_time": "2023-08-22T00:33:16.354523Z", "start_time": "2023-08-22T00:33:15.715736Z"}, "collapsed": false}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import chromadb\n", "from chromadb import Settings\n", "\n", "client = chromadb.HttpClient(\n", "    settings=Settings(chroma_client_auth_provider=\"chromadb.auth.basic_authn.BasicAuthClientProvider\",\n", "                      chroma_client_auth_credentials=\"admin:admin\"))\n", "client.heartbeat()  # this should work with or without authentication - it is a public endpoint\n", "\n", "client.get_version()  # this should work with or without authentication - it is a public endpoint\n", "\n", "client.list_collections()  # this is a protected endpoint and requires authentication\n", "\n"]}, {"cell_type": "markdown", "id": "6b75f04e59cb1d42", "metadata": {"collapsed": false}, "source": ["#### Verifying Authentication (Negative Test)"]}, {"cell_type": "code", "execution_count": 3, "id": "c0c3240ed4d70a79", "metadata": {"ExecuteTime": {"end_time": "2023-08-22T00:33:19.119718Z", "start_time": "2023-08-22T00:33:19.097558Z"}, "collapsed": false}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["As expected, you are not authorized to access protected endpoints.\n"]}], "source": ["# Try to access a protected endpoint without authentication\n", "import sys\n", "\n", "client = chromadb.HttpClient()\n", "try:\n", "    client.list_collections()\n", "except Exception as e:\n", "    if \"Unauthorized\" in str(e):\n", "        print(\"As expected, you are not authorized to access protected endpoints.\", file=sys.stderr)\n", "    else:\n", "        raise e"]}, {"cell_type": "markdown", "id": "390aed41f019649b", "metadata": {"collapsed": false}, "source": ["## Token Authentication\n", "\n", "Chroma also ships with support for authentication via opaque tokens. To use this, use `CHROMA_SERVER_AUTHN_PROVIDER=\"chromadb.auth.token_authn.TokenAuthenticationServerProvider\"`. The token authentication provider expects a configuration file containing one or more users, each of which must have one or more tokens. See the bottom of `chroma/examples/basic_functionality/authz/authz.yaml` for an example.\n", "\n", "You may also pass a single token to the `TokenAuthenticationServerProvider`. If you do so, it will only allow requests containing exactly that token. Example:\n", "\n", "```\n", "CHROMA_SERVER_AUTHN_PROVIDER=\"chromadb.auth.token_authn.TokenAuthenticationServerProvider\"\n", "CHROMA_SERVER_AUTHN_CREDENTIALS=\"test-token\"\n", "```\n", "\n", "### De<PERSON><PERSON> (`Authorization` with `<PERSON><PERSON>`)"]}, {"cell_type": "code", "execution_count": 3, "id": "b218beb03ae1582e", "metadata": {"ExecuteTime": {"end_time": "2023-08-28T16:44:30.289045Z", "start_time": "2023-08-28T16:44:29.878090Z"}, "collapsed": false}, "outputs": [{"ename": "ConnectionError", "evalue": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x112d27250>: Failed to establish a new connection: [Errno 61] Connection refused'))", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mConnectionRefusedError\u001b[0m                    <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/connection.py:174\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    173\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 174\u001b[0m     conn \u001b[38;5;241m=\u001b[39m \u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_connection\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    175\u001b[0m \u001b[43m        \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_dns_host\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mport\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mextra_kw\u001b[49m\n\u001b[1;32m    176\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    178\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketTimeout:\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/util/connection.py:95\u001b[0m, in \u001b[0;36mcreate_connection\u001b[0;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[1;32m     94\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m err \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m---> 95\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m err\n\u001b[1;32m     97\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m socket\u001b[38;5;241m.\u001b[39merror(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgetaddrinfo returns an empty list\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/util/connection.py:85\u001b[0m, in \u001b[0;36mcreate_connection\u001b[0;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[1;32m     84\u001b[0m     sock\u001b[38;5;241m.\u001b[39mbind(source_address)\n\u001b[0;32m---> 85\u001b[0m \u001b[43msock\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43msa\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     86\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m sock\n", "\u001b[0;31mConnectionRefusedError\u001b[0m: [Errno 61] Connection refused", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mNewConnectionError\u001b[0m                        <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/connectionpool.py:714\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    713\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n\u001b[0;32m--> 714\u001b[0m httplib_response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    715\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    716\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    717\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    718\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    719\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    720\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    721\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    722\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    724\u001b[0m \u001b[38;5;66;03m# If we're going to release the connection in ``finally:``, then\u001b[39;00m\n\u001b[1;32m    725\u001b[0m \u001b[38;5;66;03m# the response doesn't need to know about the connection. Otherwise\u001b[39;00m\n\u001b[1;32m    726\u001b[0m \u001b[38;5;66;03m# it will also try to release it and we'll have a double-release\u001b[39;00m\n\u001b[1;32m    727\u001b[0m \u001b[38;5;66;03m# mess.\u001b[39;00m\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/connectionpool.py:415\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[1;32m    414\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 415\u001b[0m         \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mhttplib_request_kw\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    417\u001b[0m \u001b[38;5;66;03m# We are swallowing BrokenPipeError (errno.EPIPE) since the server is\u001b[39;00m\n\u001b[1;32m    418\u001b[0m \u001b[38;5;66;03m# legitimately able to close the connection after sending a valid response.\u001b[39;00m\n\u001b[1;32m    419\u001b[0m \u001b[38;5;66;03m# With this behaviour, the received response is still readable.\u001b[39;00m\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/connection.py:244\u001b[0m, in \u001b[0;36mHTTPConnection.request\u001b[0;34m(self, method, url, body, headers)\u001b[0m\n\u001b[1;32m    243\u001b[0m     headers[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUser-Agent\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m _get_default_user_agent()\n\u001b[0;32m--> 244\u001b[0m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mHTTPConnection\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.10.10/lib/python3.10/http/client.py:1282\u001b[0m, in \u001b[0;36mHTTPConnection.request\u001b[0;34m(self, method, url, body, headers, encode_chunked)\u001b[0m\n\u001b[1;32m   1281\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Send a complete request to the server.\"\"\"\u001b[39;00m\n\u001b[0;32m-> 1282\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_request\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencode_chunked\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.10.10/lib/python3.10/http/client.py:1328\u001b[0m, in \u001b[0;36mHTTPConnection._send_request\u001b[0;34m(self, method, url, body, headers, encode_chunked)\u001b[0m\n\u001b[1;32m   1327\u001b[0m     body \u001b[38;5;241m=\u001b[39m _encode(body, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbody\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m-> 1328\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mendheaders\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencode_chunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencode_chunked\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.10.10/lib/python3.10/http/client.py:1277\u001b[0m, in \u001b[0;36mHTTPConnection.endheaders\u001b[0;34m(self, message_body, encode_chunked)\u001b[0m\n\u001b[1;32m   1276\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m CannotSendHeader()\n\u001b[0;32m-> 1277\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_send_output\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmessage_body\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencode_chunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mencode_chunked\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.10.10/lib/python3.10/http/client.py:1037\u001b[0m, in \u001b[0;36mHTTPConnection._send_output\u001b[0;34m(self, message_body, encode_chunked)\u001b[0m\n\u001b[1;32m   1036\u001b[0m \u001b[38;5;28;01mdel\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_buffer[:]\n\u001b[0;32m-> 1037\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmsg\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1039\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m message_body \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1040\u001b[0m \n\u001b[1;32m   1041\u001b[0m     \u001b[38;5;66;03m# create a consistent interface to message_body\u001b[39;00m\n", "File \u001b[0;32m~/.pyenv/versions/3.10.10/lib/python3.10/http/client.py:975\u001b[0m, in \u001b[0;36mHTTPConnection.send\u001b[0;34m(self, data)\u001b[0m\n\u001b[1;32m    974\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mauto_open:\n\u001b[0;32m--> 975\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    976\u001b[0m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m:\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/connection.py:205\u001b[0m, in \u001b[0;36mHTTPConnection.connect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    204\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mconnect\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m--> 205\u001b[0m     conn \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_new_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    206\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_prepare_conn(conn)\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/connection.py:186\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    185\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m--> 186\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m NewConnectionError(\n\u001b[1;32m    187\u001b[0m         \u001b[38;5;28mself\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to establish a new connection: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m e\n\u001b[1;32m    188\u001b[0m     )\n\u001b[1;32m    190\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m conn\n", "\u001b[0;31mNewConnectionError\u001b[0m: <urllib3.connection.HTTPConnection object at 0x112d27250>: Failed to establish a new connection: [Errno 61] Connection refused", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mMaxRetryError\u001b[0m                             Traceback (most recent call last)", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/requests/adapters.py:489\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    488\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m chunked:\n\u001b[0;32m--> 489\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    490\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    491\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    492\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    493\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    494\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    495\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    496\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    497\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    498\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    499\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    500\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    502\u001b[0m \u001b[38;5;66;03m# Send the request.\u001b[39;00m\n\u001b[1;32m    503\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/connectionpool.py:798\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[1;32m    796\u001b[0m     e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, e)\n\u001b[0;32m--> 798\u001b[0m retries \u001b[38;5;241m=\u001b[39m \u001b[43mretries\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    799\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msys\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    801\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/urllib3/util/retry.py:592\u001b[0m, in \u001b[0;36mRetry.increment\u001b[0;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m new_retry\u001b[38;5;241m.\u001b[39mis_exhausted():\n\u001b[0;32m--> 592\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause))\n\u001b[1;32m    594\u001b[0m log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIncremented Retry for (url=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, url, new_retry)\n", "\u001b[0;31mMaxRetryError\u001b[0m: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x112d27250>: Failed to establish a new connection: [Errno 61] Connection refused'))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mConnectionError\u001b[0m                           Traceback (most recent call last)", "Cell \u001b[0;32mIn[3], line 6\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mchromadb\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Settings\n\u001b[1;32m      4\u001b[0m client \u001b[38;5;241m=\u001b[39m chromadb\u001b[38;5;241m.\u001b[39mHttpClient(\n\u001b[1;32m      5\u001b[0m     settings\u001b[38;5;241m=\u001b[39mSettings(chroma_client_auth_provider\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m<PERSON>en\u001b[39m\u001b[38;5;124m\"\u001b[39m, chroma_client_auth_credentials\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtest-token\u001b[39m\u001b[38;5;124m\"\u001b[39m))\n\u001b[0;32m----> 6\u001b[0m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheartbeat\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# this should work with or without authentication - it is a public endpoint\u001b[39;00m\n\u001b[1;32m      8\u001b[0m client\u001b[38;5;241m.\u001b[39mget_version()  \u001b[38;5;66;03m# this should work with or without authentication - it is a public endpoint\u001b[39;00m\n\u001b[1;32m     10\u001b[0m client\u001b[38;5;241m.\u001b[39mlist_collections()  \u001b[38;5;66;03m# this is a protected endpoint and requires authentication\u001b[39;00m\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/chromadb/api/fastapi.py:84\u001b[0m, in \u001b[0;36mFastAPI.heartbeat\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m     81\u001b[0m \u001b[38;5;129m@override\u001b[39m\n\u001b[1;32m     82\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mheartbeat\u001b[39m(\u001b[38;5;28mself\u001b[39m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mint\u001b[39m:\n\u001b[1;32m     83\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Returns the current server time in nanoseconds to check if the server is alive\"\"\"\u001b[39;00m\n\u001b[0;32m---> 84\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_session\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_api_url\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     85\u001b[0m     raise_chroma_error(resp)\n\u001b[1;32m     86\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mint\u001b[39m(resp\u001b[38;5;241m.\u001b[39mjson()[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mnanosecond heartbeat\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/requests/sessions.py:600\u001b[0m, in \u001b[0;36mSession.get\u001b[0;34m(self, url, **kwargs)\u001b[0m\n\u001b[1;32m    592\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a GET request. Returns :class:`Response` object.\u001b[39;00m\n\u001b[1;32m    593\u001b[0m \n\u001b[1;32m    594\u001b[0m \u001b[38;5;124;03m:param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[1;32m    595\u001b[0m \u001b[38;5;124;03m:param \\*\\*kwargs: Optional arguments that ``request`` takes.\u001b[39;00m\n\u001b[1;32m    596\u001b[0m \u001b[38;5;124;03m:rtype: requests.Response\u001b[39;00m\n\u001b[1;32m    597\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    599\u001b[0m kwargs\u001b[38;5;241m.\u001b[39msetdefault(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m--> 600\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mGET\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/requests/sessions.py:587\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    582\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m    583\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[1;32m    584\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[1;32m    585\u001b[0m }\n\u001b[1;32m    586\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 587\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    589\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/chromadb/auth/providers.py:123\u001b[0m, in \u001b[0;36mRequestsClientAuthProtocolAdapter._Session.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    118\u001b[0m \u001b[38;5;129m@override\u001b[39m\n\u001b[1;32m    119\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21msend\u001b[39m(\n\u001b[1;32m    120\u001b[0m     \u001b[38;5;28mself\u001b[39m, request: requests\u001b[38;5;241m.\u001b[39mPreparedRequest, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs: Any\n\u001b[1;32m    121\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m requests\u001b[38;5;241m.\u001b[39mResponse:\n\u001b[1;32m    122\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_protocol_adapter\u001b[38;5;241m.\u001b[39minject_credentials(request)\n\u001b[0;32m--> 123\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/requests/sessions.py:701\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    698\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[1;32m    700\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 701\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43madapter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    703\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[1;32m    704\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[0;32m~/PycharmProjects/chroma-core/venv/lib/python3.10/site-packages/requests/adapters.py:565\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    561\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e\u001b[38;5;241m.\u001b[39mreason, _SSLError):\n\u001b[1;32m    562\u001b[0m         \u001b[38;5;66;03m# This branch is for urllib3 v1.22 and later.\u001b[39;00m\n\u001b[1;32m    563\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m SSLError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[0;32m--> 565\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[1;32m    567\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ClosedPoolError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    568\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request\u001b[38;5;241m=\u001b[39mrequest)\n", "\u001b[0;31mConnectionError\u001b[0m: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1 (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x112d27250>: Failed to establish a new connection: [Errno 61] Connection refused'))"]}], "source": ["import chromadb\n", "from chromadb import Settings\n", "\n", "# Adds a header {\"Authorization\": \"Bearer test-token\"} to all requests\n", "client = chromadb.HttpClient(\n", "    settings=Settings(chroma_client_auth_provider=\"token\", chroma_client_auth_credentials=\"test-token\"))\n", "client.heartbeat()  # this should work with or without authentication - it is a public endpoint\n", "\n", "client.get_version()  # this should work with or without authentication - it is a public endpoint\n", "\n", "client.list_collections()  # this is a protected endpoint and requires authentication\n"]}, {"cell_type": "code", "execution_count": null, "id": "29d28a25e85f95af", "metadata": {"collapsed": false}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}