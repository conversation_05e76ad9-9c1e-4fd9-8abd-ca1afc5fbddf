{"name": "@chroma-core/google-gemini", "version": "0.1.7", "private": false, "description": "Google Gemini embedding provider for Chroma", "main": "dist/cjs/google-gemini.cjs", "types": "dist/google-gemini.d.ts", "module": "dist/google-gemini.legacy-esm.js", "type": "module", "exports": {".": {"import": {"types": "./dist/google-gemini.d.ts", "default": "./dist/google-gemini.mjs"}, "require": {"types": "./dist/cjs/google-gemini.d.cts", "default": "./dist/cjs/google-gemini.cjs"}}}, "files": ["src", "dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup", "watch": "tsup --watch", "test": "jest"}, "devDependencies": {"@jest/globals": "^29.7.0", "dotenv": "^16.3.1", "jest": "^29.7.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsup": "^8.3.5"}, "peerDependencies": {"chromadb": "workspace:^"}, "dependencies": {"@chroma-core/ai-embeddings-common": "workspace:^0.1.0", "@google/genai": "^0.14.1"}, "engines": {"node": ">=20"}, "publishConfig": {"access": "public"}}