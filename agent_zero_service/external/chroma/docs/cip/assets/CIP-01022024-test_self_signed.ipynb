{"cells": [{"cell_type": "markdown", "source": ["# Generate a Certificate\n", "\n", "```bash\n", "openssl req -new -newkey rsa:2048 -sha256 -days 365 -nodes -x509 \\\n", "  -keyout ./serverkey.pem \\\n", "  -out ./servercert.pem \\\n", "  -subj \"/O=Chroma/C=US\" \\\n", "  -config chromadb/test/openssl.cnf\n", "```\n", "\n", "> Note: The above command should be executed at the root of the repo (openssl.cnf uses relative path)\n"], "metadata": {"collapsed": false}, "id": "faa8cefb6825fe83"}, {"cell_type": "markdown", "source": ["# Start the server\n", "\n", "```bash\n", "uvicorn chromadb.app:app --workers 1 --host 0.0.0.0 --port 8443 \\\n", "  --proxy-headers --log-config chromadb/log_config.yml --ssl-keyfile ./serverkey.pem --ssl-certfile ./servercert.pem\n", "```"], "metadata": {"collapsed": false}, "id": "e084285e11c3747d"}, {"cell_type": "markdown", "source": ["# Test with cert as SSL verify string"], "metadata": {"collapsed": false}, "id": "130df9c0a6d67b52"}, {"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["from chromadb import Settings\n", "import chromadb\n", "client = chromadb.HttpClient(host=\"localhost\",port=\"8443\",ssl=True, settings=Settings(chroma_server_ssl_verify='./servercert.pem'))\n", "print(client.heartbeat())"]}, {"cell_type": "markdown", "source": ["# Test with cert as SSL verify boolean"], "metadata": {"collapsed": false}, "id": "8223d0100df06ec4"}, {"cell_type": "code", "outputs": [], "source": ["from chromadb import Settings\n", "import chromadb\n", "client = chromadb.HttpClient(host=\"localhost\",port=\"8443\",ssl=True, settings=Settings(chroma_server_ssl_verify=False))\n", "print(client.heartbeat())"], "metadata": {"collapsed": false}, "id": "f7cf299721741c1", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": [], "metadata": {"collapsed": false}, "id": "6231ac2ac38383c2"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}