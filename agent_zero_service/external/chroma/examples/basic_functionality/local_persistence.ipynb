{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Local Peristence Demo\n", "This notebook demonstrates how to configure Chroma to persist to disk, then load it back in. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import chromadb"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Create a new Chroma client with persistence enabled. \n", "persist_directory = \"db\"\n", "\n", "client = chromadb.PersistentClient(path=persist_directory)\n", "\n", "# Create a new chroma collection\n", "collection_name = \"peristed_collection\"\n", "collection = client.get_or_create_collection(name=collection_name)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Add some data to the collection\n", "collection.add(\n", "    embeddings=[\n", "        [1.1, 2.3, 3.2],\n", "        [4.5, 6.9, 4.4],\n", "        [1.1, 2.3, 3.2],\n", "        [4.5, 6.9, 4.4],\n", "        [1.1, 2.3, 3.2],\n", "        [4.5, 6.9, 4.4],\n", "        [1.1, 2.3, 3.2],\n", "        [4.5, 6.9, 4.4],\n", "    ],\n", "    metadatas=[\n", "        {\"uri\": \"img1.png\", \"style\": \"style1\"},\n", "        {\"uri\": \"img2.png\", \"style\": \"style2\"},\n", "        {\"uri\": \"img3.png\", \"style\": \"style1\"},\n", "        {\"uri\": \"img4.png\", \"style\": \"style1\"},\n", "        {\"uri\": \"img5.png\", \"style\": \"style1\"},\n", "        {\"uri\": \"img6.png\", \"style\": \"style1\"},\n", "        {\"uri\": \"img7.png\", \"style\": \"style1\"},\n", "        {\"uri\": \"img8.png\", \"style\": \"style1\"},\n", "    ],\n", "    documents=[\"doc1\", \"doc2\", \"doc3\", \"doc4\", \"doc5\", \"doc6\", \"doc7\", \"doc8\"],\n", "    ids=[\"id1\", \"id2\", \"id3\", \"id4\", \"id5\", \"id6\", \"id7\", \"id8\"],\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Create a new client with the same settings\n", "client = chromadb.PersistentClient(path=persist_directory)\n", "\n", "# Load the collection\n", "collection = client.get_collection(collection_name)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'ids': [['id1']], 'distances': [[5.1159076593562386e-15]], 'metadatas': [[{'style': 'style1', 'uri': 'img1.png'}]], 'embeddings': None, 'documents': [['doc1']]}\n"]}], "source": ["# Query the collection\n", "results = collection.query(\n", "    query_embeddings=[[1.1, 2.3, 3.2]],\n", "    n_results=1\n", ")\n", "\n", "print(results)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ids': ['id1', 'id2', 'id3', 'id4', 'id5', 'id6', 'id7', 'id8'],\n", " 'embeddings': [[1.100000023841858, 2.299999952316284, 3.200000047683716],\n", "  [4.5, 6.900000095367432, 4.400000095367432],\n", "  [1.100000023841858, 2.299999952316284, 3.200000047683716],\n", "  [4.5, 6.900000095367432, 4.400000095367432],\n", "  [1.100000023841858, 2.299999952316284, 3.200000047683716],\n", "  [4.5, 6.900000095367432, 4.400000095367432],\n", "  [1.100000023841858, 2.299999952316284, 3.200000047683716],\n", "  [4.5, 6.900000095367432, 4.400000095367432]],\n", " 'metadatas': [{'style': 'style1', 'uri': 'img1.png'},\n", "  {'style': 'style2', 'uri': 'img2.png'},\n", "  {'style': 'style1', 'uri': 'img3.png'},\n", "  {'style': 'style1', 'uri': 'img4.png'},\n", "  {'style': 'style1', 'uri': 'img5.png'},\n", "  {'style': 'style1', 'uri': 'img6.png'},\n", "  {'style': 'style1', 'uri': 'img7.png'},\n", "  {'style': 'style1', 'uri': 'img8.png'}],\n", " 'documents': ['doc1', 'doc2', 'doc3', 'doc4', 'doc5', 'doc6', 'doc7', 'doc8']}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["collection.get(include=[\"embeddings\", \"metadatas\", \"documents\"])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Clean up\n", "! rm -rf db"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "chroma", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "88f09714c9334832bac29166716f9f6a879ee2a4ed4822c1d4120cb2393b58dd"}}}, "nbformat": 4, "nbformat_minor": 2}