<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Zero - Orchestrator Dashboard</title>
    <link rel="icon" type="image/svg+xml" href="public/favicon.svg">
    <link rel="stylesheet" href="index.css">
    <link rel="stylesheet" href="css/settings.css">
    <style>
        .orchestrator-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: var(--color-panel);
            border-radius: var(--border-radius);
            margin-top: 20px;
        }
        
        .orchestrator-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #ff6600, #0066cc);
            color: white;
            border-radius: var(--border-radius);
        }
        
        .orchestrator-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .orchestrator-card {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            padding: 20px;
            transition: all 0.3s ease;
        }
        
        .orchestrator-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--color-text);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: auto;
        }
        
        .status-active { background-color: #00c340; }
        .status-inactive { background-color: #ff4444; }
        .status-pending { background-color: #ffaa00; }
        
        .orchestrator-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .control-button {
            flex: 1;
            padding: 8px 16px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--leon-primary);
            color: white;
        }
        
        .btn-secondary {
            background: var(--color-border);
            color: var(--color-text);
        }
        
        .btn-danger {
            background: #ff4444;
            color: white;
        }
        
        .control-button:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        .orchestrator-logs {
            background: var(--color-background);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
        }
        
        .log-entry {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-info { background: rgba(0, 123, 255, 0.1); color: #007bff; }
        .log-success { background: rgba(40, 167, 69, 0.1); color: #28a745; }
        .log-warning { background: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .log-error { background: rgba(220, 53, 69, 0.1); color: #dc3545; }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--leon-primary);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-button:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body class="dark-mode dutch-interface leon-dev-theme">
    <button class="back-button" onclick="window.location.href='/'">←</button>
    
    <div class="orchestrator-container">
        <div class="orchestrator-header">
            <h1>🎼 Agent Zero Orchestrator Dashboard</h1>
            <p>Leon's AI Multi-Agent Orchestration Control Center</p>
        </div>
        
        <div class="orchestrator-grid">
            <!-- AI Agents Card -->
            <div class="orchestrator-card">
                <div class="card-header">
                    <img src="public/agent.svg" alt="AI Agents" class="card-icon">
                    <span class="card-title">AI Agents</span>
                    <span class="status-indicator status-active"></span>
                </div>
                <p>Manage and monitor active AI agents</p>
                <div class="orchestrator-controls">
                    <button class="control-button btn-primary">Start Agent</button>
                    <button class="control-button btn-secondary">Monitor</button>
                    <button class="control-button btn-danger">Stop All</button>
                </div>
            </div>
            
            <!-- Task Scheduler Card -->
            <div class="orchestrator-card">
                <div class="card-header">
                    <img src="public/task_scheduler.svg" alt="Task Scheduler" class="card-icon">
                    <span class="card-title">Task Scheduler</span>
                    <span class="status-indicator status-active"></span>
                </div>
                <p>Schedule and manage automated tasks</p>
                <div class="orchestrator-controls">
                    <button class="control-button btn-primary">New Task</button>
                    <button class="control-button btn-secondary">View Queue</button>
                    <button class="control-button btn-secondary">History</button>
                </div>
            </div>
            
            <!-- Memory Management Card -->
            <div class="orchestrator-card">
                <div class="card-header">
                    <img src="public/memory.svg" alt="Memory" class="card-icon">
                    <span class="card-title">Memory Management</span>
                    <span class="status-indicator status-active"></span>
                </div>
                <p>Monitor agent memory and knowledge base</p>
                <div class="orchestrator-controls">
                    <button class="control-button btn-primary">Optimize</button>
                    <button class="control-button btn-secondary">Backup</button>
                    <button class="control-button btn-danger">Clear</button>
                </div>
            </div>
            
            <!-- API Keys Card -->
            <div class="orchestrator-card">
                <div class="card-header">
                    <img src="public/api_keys.svg" alt="API Keys" class="card-icon">
                    <span class="card-title">API Configuration</span>
                    <span class="status-indicator status-active"></span>
                </div>
                <p>Manage API keys and external services</p>
                <div class="orchestrator-controls">
                    <button class="control-button btn-primary">Configure</button>
                    <button class="control-button btn-secondary">Test</button>
                    <button class="control-button btn-secondary">Rotate</button>
                </div>
            </div>
            
            <!-- MCP Servers Card -->
            <div class="orchestrator-card">
                <div class="card-header">
                    <img src="public/mcp_server.svg" alt="MCP Servers" class="card-icon">
                    <span class="card-title">MCP Servers</span>
                    <span class="status-indicator status-pending"></span>
                </div>
                <p>Model Context Protocol server management</p>
                <div class="orchestrator-controls">
                    <button class="control-button btn-primary">Start</button>
                    <button class="control-button btn-secondary">Configure</button>
                    <button class="control-button btn-danger">Restart</button>
                </div>
            </div>
            
            <!-- Tunnel Services Card -->
            <div class="orchestrator-card">
                <div class="card-header">
                    <img src="public/tunnel.svg" alt="Tunnel" class="card-icon">
                    <span class="card-title">Tunnel Services</span>
                    <span class="status-indicator status-inactive"></span>
                </div>
                <p>External access and tunnel management</p>
                <div class="orchestrator-controls">
                    <button class="control-button btn-primary">Enable</button>
                    <button class="control-button btn-secondary">Configure</button>
                    <button class="control-button btn-secondary">Status</button>
                </div>
            </div>
        </div>
        
        <!-- System Logs -->
        <div class="orchestrator-logs">
            <h3>🔍 System Logs</h3>
            <div id="log-container">
                <div class="log-entry log-success">[2025-06-27 14:30:15] ✅ Agent Zero orchestrator started successfully</div>
                <div class="log-entry log-info">[2025-06-27 14:30:16] 🔧 Loading configuration from settings.json</div>
                <div class="log-entry log-info">[2025-06-27 14:30:17] 🤖 OpenAI API key configured and validated</div>
                <div class="log-entry log-success">[2025-06-27 14:30:18] 🚀 Web UI server started on port 5000</div>
                <div class="log-entry log-warning">[2025-06-27 14:30:19] ⚠️ MCP servers not configured - some features may be limited</div>
                <div class="log-entry log-info">[2025-06-27 14:30:20] 📊 Memory usage: 45% | CPU: 12% | Active agents: 1</div>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-refresh logs every 5 seconds
        setInterval(() => {
            const now = new Date().toLocaleString('nl-NL');
            const logContainer = document.getElementById('log-container');
            const newLog = document.createElement('div');
            newLog.className = 'log-entry log-info';
            newLog.textContent = `[${now}] 💓 System heartbeat - All services operational`;
            logContainer.appendChild(newLog);
            
            // Keep only last 10 logs
            while (logContainer.children.length > 10) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }, 5000);
        
        // Add click handlers for control buttons
        document.querySelectorAll('.control-button').forEach(button => {
            button.addEventListener('click', function() {
                const action = this.textContent;
                const card = this.closest('.orchestrator-card');
                const service = card.querySelector('.card-title').textContent;
                
                // Show feedback
                this.style.background = '#28a745';
                this.textContent = '✓ Done';
                
                setTimeout(() => {
                    this.style.background = '';
                    this.textContent = action;
                }, 2000);
                
                // Add log entry
                const now = new Date().toLocaleString('nl-NL');
                const logContainer = document.getElementById('log-container');
                const newLog = document.createElement('div');
                newLog.className = 'log-entry log-success';
                newLog.textContent = `[${now}] 🎯 ${action} executed for ${service}`;
                logContainer.appendChild(newLog);
            });
        });
    </script>
</body>
</html>
