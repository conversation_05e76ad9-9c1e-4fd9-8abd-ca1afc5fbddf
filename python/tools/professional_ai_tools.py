"""
Professional AI Tools Integration for Agent Zero
Combines system prompts, CAG, and augmented capabilities
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add lib to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "lib"))

from python.helpers.tool import Tool, Response
from python.helpers.print_style import PrintStyle


class ProfessionalAITools(Tool):
    """
    Professional AI Tools for Agent Zero
    Integrates v0, Cursor, Devin, and other professional AI system prompts
    with Cache-Augmented Generation for enhanced performance
    """
    
    def execute(self, **kwargs) -> Response:
        """Execute professional AI tools"""
        
        action = kwargs.get("action", "help")
        
        if action == "load_system_prompt":
            return self._load_system_prompt(**kwargs)
        elif action == "setup_cag":
            return self._setup_cag(**kwargs)
        elif action == "enhanced_query":
            return self._enhanced_query(**kwargs)
        elif action == "compare_tools":
            return self._compare_tools(**kwargs)
        elif action == "list_available":
            return self._list_available(**kwargs)
        else:
            return self._show_help()
    
    def _load_system_prompt(self, **kwargs) -> Response:
        """Load a professional system prompt"""
        try:
            from ai.system_prompt_loader import SystemPromptLoader
            
            tool_name = kwargs.get("tool_name", "Cursor Prompts")
            task_type = kwargs.get("task_type", "coding")
            
            loader = SystemPromptLoader()
            prompt = loader.create_agent_zero_prompt(tool_name, task_type)
            
            if prompt:
                return Response(
                    message=f"✅ Loaded {tool_name} system prompt for {task_type}",
                    data={
                        "tool": tool_name,
                        "task_type": task_type,
                        "prompt_length": len(prompt),
                        "prompt_preview": prompt[:300] + "..." if len(prompt) > 300 else prompt
                    }
                )
            else:
                return Response(
                    message=f"❌ Failed to load system prompt for {tool_name}",
                    data={"error": "Prompt not found or not accessible"}
                )
                
        except Exception as e:
            return Response(
                message=f"❌ Error loading system prompt: {str(e)}",
                data={"error": str(e)}
            )
    
    def _setup_cag(self, **kwargs) -> Response:
        """Setup Cache-Augmented Generation"""
        try:
            from ai.cag_integration import CAGManager, setup_cag_for_agent_zero
            
            knowledge_sources = kwargs.get("knowledge_sources", [
                "prompts/default/",
                "knowledge/default/", 
                "docs/",
                "lib/"
            ])
            
            result = setup_cag_for_agent_zero(knowledge_sources)
            
            if result.get("success"):
                return Response(
                    message="✅ CAG setup successful",
                    data=result
                )
            else:
                return Response(
                    message="❌ CAG setup failed",
                    data=result
                )
                
        except Exception as e:
            return Response(
                message=f"❌ CAG setup error: {str(e)}",
                data={"error": str(e)}
            )
    
    def _enhanced_query(self, **kwargs) -> Response:
        """Perform enhanced query using CAG and professional prompts"""
        try:
            from ai.cag_integration import cag_enhanced_query
            
            query = kwargs.get("query", "")
            use_professional_prompts = kwargs.get("use_professional_prompts", True)
            
            if not query:
                return Response(
                    message="❌ Query is required",
                    data={"error": "No query provided"}
                )
            
            enhanced_result = cag_enhanced_query(query, use_professional_prompts)
            
            return Response(
                message="✅ Enhanced query completed",
                data={
                    "original_query": query,
                    "enhanced_prompt": enhanced_result,
                    "used_professional_prompts": use_professional_prompts,
                    "prompt_length": len(enhanced_result)
                }
            )
            
        except Exception as e:
            return Response(
                message=f"❌ Enhanced query error: {str(e)}",
                data={"error": str(e)}
            )
    
    def _compare_tools(self, **kwargs) -> Response:
        """Compare different professional AI tools"""
        try:
            from ai.system_prompt_loader import SystemPromptLoader
            
            tools_to_compare = kwargs.get("tools", [
                "Cursor Prompts",
                "v0 Prompts and Tools", 
                "Devin AI",
                "Windsurf"
            ])
            
            loader = SystemPromptLoader()
            comparison = {}
            
            for tool in tools_to_compare:
                prompts = loader.list_available_prompts(tool)
                comparison[tool] = {
                    "available_prompts": len(prompts),
                    "prompts": prompts[:5],  # Show first 5
                    "specialization": self._get_tool_specialization(tool)
                }
            
            return Response(
                message="✅ Tool comparison completed",
                data={
                    "compared_tools": len(tools_to_compare),
                    "comparison": comparison,
                    "recommendation": self._get_tool_recommendation(kwargs.get("task_type", "general"))
                }
            )
            
        except Exception as e:
            return Response(
                message=f"❌ Tool comparison error: {str(e)}",
                data={"error": str(e)}
            )
    
    def _list_available(self, **kwargs) -> Response:
        """List all available professional tools and capabilities"""
        try:
            from ai.system_prompt_loader import SystemPromptLoader
            from ai.cag_integration import CAGManager
            
            # System prompts
            loader = SystemPromptLoader()
            summary = loader.export_prompts_summary()
            
            # CAG status
            cag = CAGManager()
            cag_status = cag.is_cag_available()
            
            # Augmented status
            augmented_path = Path("augmented/intellij-augment")
            augmented_status = augmented_path.exists()
            
            return Response(
                message="✅ Available professional tools",
                data={
                    "system_prompts": {
                        "total_tools": summary["total_tools"],
                        "tools": list(summary["tools"].keys()),
                        "cache_size": summary["cache_size"]
                    },
                    "cag": {
                        "available": cag_status,
                        "description": "Cache-Augmented Generation for faster retrieval"
                    },
                    "augmented": {
                        "available": augmented_status,
                        "description": "IntelliJ Augment integration"
                    },
                    "capabilities": [
                        "Professional system prompts from 10+ AI tools",
                        "Cache-Augmented Generation (CAG)",
                        "Enhanced query processing",
                        "Tool comparison and recommendation",
                        "IDE integration via Augmented"
                    ]
                }
            )
            
        except Exception as e:
            return Response(
                message=f"❌ Error listing tools: {str(e)}",
                data={"error": str(e)}
            )
    
    def _get_tool_specialization(self, tool_name: str) -> str:
        """Get specialization description for a tool"""
        specializations = {
            "v0 Prompts and Tools": "UI/UX generation and web interfaces",
            "Cursor Prompts": "Code editing and development assistance", 
            "Devin AI": "Full-stack software engineering",
            "Lovable": "App building and rapid prototyping",
            "Windsurf": "Development environment and workflow",
            "VSCode Agent": "Code assistance and GitHub integration",
            "Replit": "Online coding and collaboration",
            "Same.dev": "Code generation and automation",
            "Trae": "AI-powered development tools"
        }
        return specializations.get(tool_name, "General AI assistance")
    
    def _get_tool_recommendation(self, task_type: str) -> str:
        """Get tool recommendation based on task type"""
        recommendations = {
            "ui_design": "v0 Prompts and Tools",
            "code_editing": "Cursor Prompts",
            "software_engineering": "Devin AI",
            "app_development": "Lovable",
            "development_workflow": "Windsurf",
            "code_assistance": "VSCode Agent",
            "general": "Cursor Prompts"
        }
        
        recommended = recommendations.get(task_type.lower(), "Cursor Prompts")
        return f"Recommended: {recommended} for {task_type}"
    
    def _show_help(self) -> Response:
        """Show help information"""
        help_text = """
🚀 Professional AI Tools for Agent Zero

Available Actions:
• load_system_prompt - Load professional system prompts
• setup_cag - Setup Cache-Augmented Generation  
• enhanced_query - Perform enhanced queries with CAG
• compare_tools - Compare different AI tools
• list_available - List all available tools

Examples:
• action: "load_system_prompt", tool_name: "Cursor Prompts", task_type: "coding"
• action: "setup_cag", knowledge_sources: ["docs/", "prompts/"]
• action: "enhanced_query", query: "How to build a React app?"
• action: "compare_tools", tools: ["Cursor Prompts", "v0 Prompts and Tools"]
• action: "list_available"

Features:
✅ 7000+ lines of professional system prompts
✅ Cache-Augmented Generation (CAG)
✅ Enhanced query processing
✅ Tool comparison and recommendations
✅ IntelliJ Augment integration
"""
        
        return Response(
            message="Professional AI Tools Help",
            data={"help": help_text}
        )


# Register the tool
def get_tool() -> Tool:
    return ProfessionalAITools()
