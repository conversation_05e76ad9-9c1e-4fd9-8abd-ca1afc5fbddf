version: '3.9'
networks:
  net:

services:
  zipkin:
    image: openzipkin/zipkin
    ports:
      - "9411:9411" # you can access Zipkin UI at http://localhost:9411
    depends_on: [otel-collector]
    networks:
      - net
  otel-collector:
    image: otel/opentelemetry-collector-contrib
    command: ["--config=/etc/otel-collector-config.yaml"]
    volumes:
      - ${PWD}/examples/observability/otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - "4317:4317"  # OTLP
      - "55681:55681" # Legacy
    networks:
      - net
  server:
    image: server
    build:
      context: ${PWD}
      dockerfile: Dockerfile
    volumes:
      - ${PWD}/:/chroma
      # Be aware that indexed data are located in "/chroma/chroma/"
      # Default configuration for persist_directory in chromadb/config.py
    command: uvicorn chromadb.app:app --reload --workers 1 --host 0.0.0.0 --port 8000 --log-config chromadb/log_config.yml
    environment:
      - IS_PERSISTENT=TRUE
      - CHROMA_SERVER_AUTHN_PROVIDER=${CHROMA_SERVER_AUTHN_PROVIDER}
      - CHROMA_SERVER_AUTHN_CREDENTIALS_FILE=${CHROMA_SERVER_AUTHN_CREDENTIALS_FILE}
      - CHROMA_SERVER_AUTHN_CREDENTIALS=${CHROMA_SERVER_AUTHN_CREDENTIALS}
      - PERSIST_DIRECTORY=${PERSIST_DIRECTORY:-/chroma/chroma}
      - CHROMA_OTEL_COLLECTION_ENDPOINT=http://otel-collector:4317/
      - CHROMA_OTEL_EXPORTER_HEADERS=${CHROMA_OTEL_EXPORTER_HEADERS}
      - CHROMA_OTEL_SERVICE_NAME=${CHROMA_OTEL_SERVICE_NAME:-chroma}
      - CHROMA_OTEL_GRANULARITY=${CHROMA_OTEL_GRANULARITY:-all}
    ports:
      - 8000:8000
    depends_on: [otel-collector]
    networks:
      - net

volumes:
  backups:
    driver: local
