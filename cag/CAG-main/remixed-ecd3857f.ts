// lib/seo-analyzer.ts
import puppeteer from 'puppeteer'
import { prisma } from './prisma'
import { z } from 'zod'

interface SEOAnalysisResult {
  technicalSEO: TechnicalSEOResult
  performance: PerformanceResult
  content: ContentResult
  issues: TechnicalIssue[]
  recommendations: Recommendation[]
}

interface TechnicalSEOResult {
  metaTitle: string | null
  metaDescription: string | null
  h1Tags: string[]
  h2Tags: string[]
  canonicalUrl: string | null
  metaRobots: string | null
  structuredData: boolean
  xmlSitemap: boolean
  robotsTxt: boolean
  sslEnabled: boolean
  internalLinks: number
  externalLinks: number
  images: ImageAnalysis
}

interface PerformanceResult {
  loadTime: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  performanceScore: number
}

interface ContentResult {
  wordCount: number
  readabilityScore: number
  headingStructure: HeadingStructure
  keywordDensity: KeywordDensity[]
}

interface ImageAnalysis {
  total: number
  withoutAlt: string[]
  oversized: string[]
  formats: Record<string, number>
}

interface TechnicalIssue {
  type: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  title: string
  description: string
  url?: string
  element?: string
}

interface Recommendation {
  category: string
  priority: 'LOW' | 'MEDIUM' | 'HIGH'
  title: string
  description: string
  impact: string
}

export class SEOAnalyzer {
  private browser: puppeteer.Browser | null = null

  async initialize() {
    this.browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    })
  }

  async analyzeSite(url: string): Promise<SEOAnalysisResult> {
    if (!this.browser) await this.initialize()
    
    const page = await this.browser!.newPage()
    
    try {
      // Performance monitoring
      await page.setCacheEnabled(false)
      const startTime = Date.now()
      
      // Navigate to page
      const response = await page.goto(url, { 
        waitUntil: 'networkidle2',
        timeout: 30000 
      })
      
      const loadTime = Date.now() - startTime
      
      if (!response || !response.ok()) {
        throw new Error(`Failed to load ${url}: ${response?.status()}`)
      }

      // Get performance metrics
      const performanceMetrics = await this.getPerformanceMetrics(page)
      
      // Analyze technical SEO
      const technicalSEO = await this.analyzeTechnicalSEO(page, url)
      
      // Analyze content
      const content = await this.analyzeContent(page)
      
      // Detect issues
      const issues = await this.detectTechnicalIssues(page, url, technicalSEO, content)
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(technicalSEO, content, issues)

      return {
        technicalSEO,
        performance: { ...performanceMetrics, loadTime },
        content,
        issues,
        recommendations
      }
      
    } finally {
      await page.close()
    }
  }

  private async getPerformanceMetrics(page: puppeteer.Page): Promise<PerformanceResult> {
    const metrics = await page.evaluate(() => {
      return new Promise<PerformanceResult>((resolve) => {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const paintEntries = performance.getEntriesByType('paint')
          const layoutShiftEntries = performance.getEntriesByType('layout-shift')
          
          const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0
          const lcp = entries.find(entry => entry.entryType === 'largest-contentful-paint')?.startTime || 0
          const cls = layoutShiftEntries.reduce((sum, entry) => sum + (entry as any).value, 0)
          
          resolve({
            loadTime: 0, // Will be set by caller
            firstContentfulPaint: fcp,
            largestContentfulPaint: lcp,
            cumulativeLayoutShift: cls,
            performanceScore: this.calculatePerformanceScore(fcp, lcp, cls)
          })
        })
        
        observer.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'layout-shift'] })
        
        // Fallback after 5 seconds
        setTimeout(() => {
          resolve({
            loadTime: 0,
            firstContentfulPaint: 0,
            largestContentfulPaint: 0,
            cumulativeLayoutShift: 0,
            performanceScore: 0
          })
        }, 5000)
      })
    })
    
    return metrics
  }

  private async analyzeTechnicalSEO(page: puppeteer.Page, url: string): Promise<TechnicalSEOResult> {
    return await page.evaluate((pageUrl) => {
      const metaTitle = document.querySelector('title')?.textContent || null
      const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content') || null
      const canonical = document.querySelector('link[rel="canonical"]')?.getAttribute('href') || null
      const metaRobots = document.querySelector('meta[name="robots"]')?.getAttribute('content') || null
      
      // Get heading tags
      const h1Tags = Array.from(document.querySelectorAll('h1')).map(h => h.textContent || '')
      const h2Tags = Array.from(document.querySelectorAll('h2')).map(h => h.textContent || '')
      
      // Check for structured data
      const structuredData = document.querySelector('script[type="application/ld+json"]') !== null
      
      // Count links
      const links = Array.from(document.querySelectorAll('a[href]'))
      const internalLinks = links.filter(link => {
        const href = link.getAttribute('href')
        return href && (href.startsWith('/') || href.includes(new URL(pageUrl).hostname))
      }).length
      
      const externalLinks = links.length - internalLinks
      
      // Analyze images
      const images = Array.from(document.querySelectorAll('img'))
      const imagesWithoutAlt = images.filter(img => !img.getAttribute('alt') || img.getAttribute('alt')?.trim() === '')
      
      const imageFormats: Record<string, number> = {}
      images.forEach(img => {
        const src = img.getAttribute('src')
        if (src) {
          const extension = src.split('.').pop()?.toLowerCase()
          if (extension) {
            imageFormats[extension] = (imageFormats[extension] || 0) + 1
          }
        }
      })

      return {
        metaTitle,
        metaDescription,
        h1Tags,
        h2Tags,
        canonicalUrl: canonical,
        metaRobots,
        structuredData,
        xmlSitemap: false, // Will be checked separately
        robotsTxt: false,  // Will be checked separately
        sslEnabled: pageUrl.startsWith('https://'),
        internalLinks,
        externalLinks,
        images: {
          total: images.length,
          withoutAlt: imagesWithoutAlt.map(img => img.getAttribute('src') || ''),
          oversized: [], // Will be analyzed separately
          formats: imageFormats
        }
      }
    }, url)
  }

  private async analyzeContent(page: puppeteer.Page): Promise<ContentResult> {
    return await page.evaluate(() => {
      // Get main content (try to exclude navigation, footer, etc.)
      const mainContent = document.querySelector('main') || 
                         document.querySelector('[role="main"]') || 
                         document.querySelector('.content') ||
                         document.body
      
      const textContent = mainContent?.textContent || ''
      const wordCount = textContent.trim().split(/\s+/).filter(word => word.length > 0).length
      
      // Simple readability calculation (Flesch Reading Ease approximation)
      const sentences = textContent.split(/[.!?]+/).filter(s => s.trim().length > 0).length
      const avgWordsPerSentence = wordCount / (sentences || 1)
      const avgSyllablesPerWord = 1.5 // Simplified assumption
      const readabilityScore = 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord)
      
      // Analyze heading structure
      const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
      const headingStructure = {
        h1: headings.filter(h => h.tagName === 'H1').length,
        h2: headings.filter(h => h.tagName === 'H2').length,
        h3: headings.filter(h => h.tagName === 'H3').length,
        h4: headings.filter(h => h.tagName === 'H4').length,
        h5: headings.filter(h => h.tagName === 'H5').length,
        h6: headings.filter(h => h.tagName === 'H6').length,
      }
      
      return {
        wordCount,
        readabilityScore: Math.max(0, Math.min(100, readabilityScore)),
        headingStructure,
        keywordDensity: [] // Will be calculated separately if keywords provided
      }
    })
  }

  private async detectTechnicalIssues(
    page: puppeteer.Page, 
    url: string, 
    technical: TechnicalSEOResult, 
    content: ContentResult
  ): Promise<TechnicalIssue[]> {
    const issues: TechnicalIssue[] = []
    
    // Check meta title
    if (!technical.metaTitle) {
      issues.push({
        type: 'META_MISSING',
        severity: 'HIGH',
        title: 'Missing Meta Title',
        description: 'The page is missing a meta title tag, which is crucial for SEO.',
        element: '<title>'
      })
    } else if (technical.metaTitle.length > 60) {
      issues.push({
        type: 'TITLE_TOO_LONG',
        severity: 'MEDIUM',
        title: 'Meta Title Too Long',
        description: `Meta title is ${technical.metaTitle.length} characters. Keep it under 60 characters.`,
        element: '<title>'
      })
    }
    
    // Check meta description
    if (!technical.metaDescription) {
      issues.push({
        type: 'META_MISSING',
        severity: 'HIGH',
        title: 'Missing Meta Description',
        description: 'The page is missing a meta description, which affects click-through rates.',
        element: '<meta name="description">'
      })
    } else if (technical.metaDescription.length > 160) {
      issues.push({
        type: 'DESCRIPTION_TOO_LONG',
        severity: 'MEDIUM',
        title: 'Meta Description Too Long',
        description: `Meta description is ${technical.metaDescription.length} characters. Keep it under 160 characters.`,
        element: '<meta name="description">'
      })
    }
    
    // Check H1 tags
    if (technical.h1Tags.length === 0) {
      issues.push({
        type: 'H1_MISSING',
        severity: 'HIGH',
        title: 'Missing H1 Tag',
        description: 'The page is missing an H1 tag, which is important for content structure.',
        element: '<h1>'
      })
    } else if (technical.h1Tags.length > 1) {
      issues.push({
        type: 'H1_MISSING',
        severity: 'MEDIUM',
        title: 'Multiple H1 Tags',
        description: `Found ${technical.h1Tags.length} H1 tags. Use only one H1 per page.`,
        element: '<h1>'
      })
    }
    
    // Check images without alt text
    if (technical.images.withoutAlt.length > 0) {
      issues.push({
        type: 'MISSING_ALT_TEXT',
        severity: 'MEDIUM',
        title: 'Images Missing Alt Text',
        description: `${technical.images.withoutAlt.length} images are missing alt text, affecting accessibility and SEO.`,
        element: '<img>'
      })
    }
    
    // Check SSL
    if (!technical.sslEnabled) {
      issues.push({
        type: 'SSL_ISSUE',
        severity: 'CRITICAL',
        title: 'No SSL Certificate',
        description: 'The website is not using HTTPS, which is required for modern SEO.',
        url
      })
    }
    
    return issues
  }

  private generateRecommendations(
    technical: TechnicalSEOResult,
    content: ContentResult,
    issues: TechnicalIssue[]
  ): Recommendation[] {
    const recommendations: Recommendation[] = []
    
    // Content recommendations
    if (content.wordCount < 300) {
      recommendations.push({
        category: 'Content',
        priority: 'HIGH',
        title: 'Increase Content Length',
        description: `Page has only ${content.wordCount} words. Aim for at least 300 words for better SEO.`,
        impact: 'Improved search rankings and user engagement'
      })
    }
    
    // Technical recommendations based on issues
    const criticalIssues = issues.filter(issue => issue.severity === 'CRITICAL')
    if (criticalIssues.length > 0) {
      recommendations.push({
        category: 'Technical',
        priority: 'HIGH',
        title: 'Fix Critical Technical Issues',
        description: `Address ${criticalIssues.length} critical technical issues immediately.`,
        impact: 'Prevent negative SEO impact and improve site security'
      })
    }
    
    // Performance recommendations
    recommendations.push({
      category: 'Performance',
      priority: 'MEDIUM',
      title: 'Optimize Images',
      description: 'Convert images to modern formats like WebP and implement lazy loading.',
      impact: 'Faster page load times and better Core Web Vitals'
    })
    
    return recommendations
  }

  private calculatePerformanceScore(fcp: number, lcp: number, cls: number): number {
    // Simplified performance score calculation
    let score = 100
    
    // FCP scoring (target: < 1.8s)
    if (fcp > 1800) score -= 20
    else if (fcp > 1000) score -= 10
    
    // LCP scoring (target: < 2.5s)
    if (lcp > 2500) score -= 25
    else if (lcp > 1500) score -= 15
    
    // CLS scoring (target: < 0.1)
    if (cls > 0.25) score -= 20
    else if (cls > 0.1) score -= 10
    
    return Math.max(0, score)
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close()
      this.browser = null
    }
  }
}

// Main function to analyze SEO and save to database
export async function analyzeSEO(reportId: string) {
  try {
    const report = await prisma.seoReport.findUnique({
      where: { id: reportId },
      include: { website: true }
    })
    
    if (!report) {
      throw new Error(`Report ${reportId} not found`)
    }
    
    await prisma.seoReport.update({
      where: { id: reportId },
      data: { status: 'RUNNING' }
    })
    
    const analyzer = new SEOAnalyzer()
    const result = await analyzer.analyzeSite(report.websiteUrl)
    await analyzer.cleanup()
    
    // Save results to database
    await prisma.seoReport.update({
      where: { id: reportId },
      data: {
        status: 'COMPLETED',
        completedAt: new Date(),
        overallScore: calculateOverallScore(result),
        performanceScore: result.performance.performanceScore,
        seoScore: calculateSEOScore(result.technicalSEO, result.content),
        metaTitle: result.technicalSEO.metaTitle,
        metaDescription: result.technicalSEO.metaDescription,
        h1Count: result.technicalSEO.h1Tags.length,
        h2Count: result.technicalSEO.h2Tags.length,
        internalLinks: result.technicalSEO.internalLinks,
        externalLinks: result.technicalSEO.externalLinks,
        images: result.technicalSEO.images.total,
        imagesWithoutAlt: result.technicalSEO.images.withoutAlt.length,
        loadTime: result.performance.loadTime,
        firstContentfulPaint: result.performance.firstContentfulPaint,
        largestContentfulPaint: result.performance.largestContentfulPaint,
        cumulativeLayoutShift: result.performance.cumulativeLayoutShift,
        wordCount: result.content.wordCount,
        readabilityScore: result.content.readabilityScore,
        rawData: result,
        recommendations: result.recommendations
      }
    })
    
    // Save technical issues
    if (result.issues.length > 0 && report.website) {
      await prisma.technicalIssue.createMany({
        data: result.issues.map(issue => ({
          websiteId: report.website!.id,
          type: issue.type as any,
          severity: issue.severity as any,
          title: issue.title,
          description: issue.description,
          url: issue.url
        }))
      })
    }
    
    console.log(`SEO analysis completed for ${report.websiteUrl}`)
    
  } catch (error) {
    console.error(`SEO analysis failed for report ${reportId}:`, error)
    
    await prisma.seoReport.update({
      where: { id: reportId },
      data: { 
        status: 'FAILED',
        completedAt: new Date()
      }
    })
  }
}

function calculateOverallScore(result: SEOAnalysisResult): number {
  const seoScore = calculateSEOScore(result.technicalSEO, result.content)
  const performanceScore = result.performance.performanceScore
  const issuesPenalty = result.issues.reduce((penalty, issue) => {
    switch (issue.severity) {
      case 'CRITICAL': return penalty + 15
      case 'HIGH': return penalty + 10
      case 'MEDIUM': return penalty + 5
      case 'LOW': return penalty + 2
      default: return penalty
    }
  }, 0)
  
  return Math.max(0, Math.round((seoScore + performanceScore) / 2 - issuesPenalty))
}

function calculateSEOScore(technical: TechnicalSEOResult, content: ContentResult): number {
  let score = 100
  
  // Technical SEO factors
  if (!technical.metaTitle) score -= 15
  if (!technical.metaDescription) score -= 10
  if (technical.h1Tags.length === 0) score -= 10
  if (technical.h1Tags.length > 1) score -= 5
  if (!technical.sslEnabled) score -= 20
  if (technical.images.withoutAlt.length > 0) {
    score -= Math.min(15, technical.images.withoutAlt.length * 2)
  }
  
  // Content factors
  if (content.wordCount < 300) score -= 15
  if (content.readabilityScore < 30) score -= 10
  
  return Math.max(0, score)
}