{"name": "@chroma-core/cloudflare-worker-ai", "version": "0.1.7", "private": false, "description": "Cloudflare Workers AI embedding provider for Chroma", "main": "dist/index.cjs", "types": "dist/index.d.ts", "module": "dist/index.js", "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "files": ["src", "dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup", "watch": "tsup --watch", "test": "jest"}, "devDependencies": {"@jest/globals": "^29.7.0", "dotenv": "^16.3.1", "jest": "^29.7.0", "rimraf": "^5.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsup": "^8.3.5"}, "peerDependencies": {"chromadb": "workspace:^"}, "dependencies": {"@chroma-core/ai-embeddings-common": "workspace:^"}, "engines": {"node": ">=20"}, "publishConfig": {"access": "public"}}