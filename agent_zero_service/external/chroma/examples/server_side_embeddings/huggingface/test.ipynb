{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Prior to running the below make sure that you have an HF server running:\n", "\n", "You can run:\n", "\n", "```bash\n", "docker compose -f examples/server_side_embeddings/huggingface/docker-compose.yml up -d\n", "```"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/Users/<USER>/experiments/chroma-experiments/1367_hugging_face_embedding_server\n"]}], "source": ["%cd ../../../"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ids': [['test']],\n", " 'distances': [[0.0]],\n", " 'embeddings': None,\n", " 'metadatas': [[None]],\n", " 'documents': [['test']],\n", " 'uris': None,\n", " 'data': None}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import chromadb\n", "\n", "from chromadb.utils.embedding_functions import HuggingFaceEmbeddingServer\n", "\n", "\n", "ef = HuggingFaceEmbeddingServer(url=\"http://localhost:8001/embed\")\n", "\n", "client = chromadb.HttpClient(\"http://localhost:8000/\")\n", "\n", "col=client.get_or_create_collection(\"test\",embedding_function=ef)\n", "\n", "col.add(documents=[\"test\"],ids=[\"test\"])\n", "\n", "col.query(query_texts=[\"test\"])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}