## Examples

> Searching for community contributions! Join the [#contributing](https://discord.com/channels/1073293645303795742/1074711539724058635) Discord Channel to discuss.

This folder will contain an ever-growing set of examples.

The key with examples is that they should *always* work. The failure mode of examples folders is that they get quickly deprecated.

Examples are:
- Easy to maintain
- Easy to maintain examples are __simple__
- Use case examples are fine, technology is better

```
folder structure
- basic_functionality - notebooks with simple walkthroughs
- advanced_functionality - notebooks with advanced walkthroughs
- deployments - how to deploy places
- use_with - chroma + ___, where ___ can be langchain, nextjs, etc
- data - common data for examples
```

> 💡 Feel free to open a PR with an example you would like to see

### Basic Functionality
- [x] Examples of using different embedding models
- [x] Local persistance demo
- [x] Where filtering demo

### Advanced Functionality
- [ ] Clustering
- [ ] Projections
- [ ] Fine tuning

### Use With

#### LLM Application Code
- [ ] Langchain
- [ ] LlamaIndex
- [ ] Semantic Kernal

#### App Frameworks
- [ ] Streamlit
- [ ] Gradio
- [ ] Nextjs
- [ ] Rails
- [ ] FastAPI

#### Inference Services
- [ ] Brev.dev
- [ ] Banana.dev
- [ ] Modal

### LLM providers/services
- [ ] OpenAI
- [ ] Anthropic
- [ ] Cohere
- [ ] Google PaLM
- [ ] Hugging Face

***

### Inspiration
- The [OpenAI Cookbook](https://github.com/openai/openai-cookbook) gets a lot of things right
