"""
AI Prompt Templates for Agent Zero
Collection of reusable prompt templates for different tasks
"""

from typing import Dict, List, Any, Optional
from datetime import datetime


class PromptTemplates:
    """Collection of AI prompt templates for Agent Zero"""
    
    @staticmethod
    def code_review_prompt(code: str, language: str = "python") -> str:
        """Generate a code review prompt"""
        return f"""
Please review the following {language} code and provide feedback on:

1. **Code Quality**: Readability, maintainability, and best practices
2. **Performance**: Potential optimizations and efficiency improvements  
3. **Security**: Potential vulnerabilities or security concerns
4. **Bugs**: Any logical errors or potential issues
5. **Suggestions**: Specific improvements with examples

**Code to Review:**
```{language}
{code}
```

**Please provide:**
- Overall rating (1-10)
- Specific issues with line numbers
- Suggested improvements
- Positive aspects of the code

Format your response in markdown with clear sections.
"""

    @staticmethod
    def documentation_prompt(code: str, language: str = "python") -> str:
        """Generate documentation for code"""
        return f"""
Please create comprehensive documentation for the following {language} code:

**Code:**
```{language}
{code}
```

**Please provide:**
1. **Overview**: Brief description of what the code does
2. **Parameters**: Description of all parameters/arguments
3. **Returns**: What the function/class returns
4. **Examples**: Usage examples with sample inputs/outputs
5. **Notes**: Any important implementation details or limitations

Format the documentation in a clear, professional style suitable for developers.
"""

    @staticmethod
    def bug_fix_prompt(code: str, error_message: str, language: str = "python") -> str:
        """Generate a bug fixing prompt"""
        return f"""
I'm encountering an error in my {language} code. Please help me fix it.

**Error Message:**
```
{error_message}
```

**Code with Issue:**
```{language}
{code}
```

**Please provide:**
1. **Root Cause**: Explanation of what's causing the error
2. **Fixed Code**: The corrected version of the code
3. **Explanation**: Step-by-step explanation of the fix
4. **Prevention**: How to avoid similar issues in the future

Please highlight the specific changes made to fix the issue.
"""

    @staticmethod
    def optimization_prompt(code: str, language: str = "python") -> str:
        """Generate a code optimization prompt"""
        return f"""
Please optimize the following {language} code for better performance:

**Current Code:**
```{language}
{code}
```

**Please provide:**
1. **Performance Analysis**: Current bottlenecks and inefficiencies
2. **Optimized Code**: Improved version with better performance
3. **Benchmarks**: Expected performance improvements
4. **Trade-offs**: Any trade-offs between performance and readability
5. **Alternative Approaches**: Other optimization strategies

Focus on practical improvements that maintain code readability.
"""

    @staticmethod
    def test_generation_prompt(code: str, language: str = "python") -> str:
        """Generate unit tests for code"""
        return f"""
Please create comprehensive unit tests for the following {language} code:

**Code to Test:**
```{language}
{code}
```

**Please provide:**
1. **Test Cases**: Cover normal cases, edge cases, and error conditions
2. **Test Framework**: Use appropriate testing framework ({language} unittest, pytest, etc.)
3. **Mocking**: Mock external dependencies if needed
4. **Coverage**: Aim for high code coverage
5. **Documentation**: Brief explanation of what each test validates

Include both positive and negative test cases.
"""

    @staticmethod
    def refactoring_prompt(code: str, language: str = "python") -> str:
        """Generate a refactoring prompt"""
        return f"""
Please refactor the following {language} code to improve its structure and maintainability:

**Code to Refactor:**
```{language}
{code}
```

**Please focus on:**
1. **Clean Code Principles**: Single responsibility, DRY, SOLID principles
2. **Code Structure**: Better organization and modularity
3. **Naming**: Improve variable and function names
4. **Complexity**: Reduce cyclomatic complexity
5. **Reusability**: Make code more reusable and extensible

**Provide:**
- Refactored code with clear improvements
- Explanation of changes made
- Benefits of the refactoring
"""

    @staticmethod
    def api_design_prompt(requirements: str) -> str:
        """Generate an API design prompt"""
        return f"""
Please design a RESTful API based on the following requirements:

**Requirements:**
{requirements}

**Please provide:**
1. **API Endpoints**: Complete list with HTTP methods and paths
2. **Request/Response Schemas**: JSON schemas for all endpoints
3. **Authentication**: Security and authentication strategy
4. **Error Handling**: Standard error responses and codes
5. **Documentation**: OpenAPI/Swagger specification
6. **Best Practices**: RESTful design principles applied

Include example requests and responses for each endpoint.
"""

    @staticmethod
    def database_design_prompt(requirements: str) -> str:
        """Generate a database design prompt"""
        return f"""
Please design a database schema based on the following requirements:

**Requirements:**
{requirements}

**Please provide:**
1. **Entity Relationship Diagram**: Tables and relationships
2. **Table Schemas**: Detailed column definitions with types and constraints
3. **Indexes**: Recommended indexes for performance
4. **Normalization**: Proper normalization level and justification
5. **Sample Queries**: Common queries with optimization
6. **Migration Scripts**: SQL scripts to create the schema

Consider scalability and performance in your design.
"""

    @staticmethod
    def custom_prompt(template: str, **kwargs) -> str:
        """Create a custom prompt from template with variables"""
        try:
            return template.format(**kwargs)
        except KeyError as e:
            return f"Error: Missing variable {e} in template"

    @staticmethod
    def get_available_templates() -> List[str]:
        """Get list of available prompt templates"""
        return [
            "code_review_prompt",
            "documentation_prompt", 
            "bug_fix_prompt",
            "optimization_prompt",
            "test_generation_prompt",
            "refactoring_prompt",
            "api_design_prompt",
            "database_design_prompt",
            "custom_prompt"
        ]


# Helper function for Agent Zero integration
def generate_prompt(template_name: str, **kwargs) -> str:
    """Generate a prompt using the specified template"""
    templates = PromptTemplates()
    
    if hasattr(templates, template_name):
        method = getattr(templates, template_name)
        return method(**kwargs)
    else:
        return f"Error: Template '{template_name}' not found. Available templates: {templates.get_available_templates()}"


# Example usage
if __name__ == "__main__":
    # Test code review prompt
    sample_code = """
def calculate_total(items):
    total = 0
    for item in items:
        total += item['price'] * item['quantity']
    return total
"""
    
    prompt = PromptTemplates.code_review_prompt(sample_code, "python")
    print("Generated prompt:")
    print(prompt)
