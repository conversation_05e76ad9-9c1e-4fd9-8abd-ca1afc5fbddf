{"name": "chromadb", "version": "3.0.5", "description": "A JavaScript interface for chroma", "keywords": ["chroma", "embedding", "ai", "vector"], "author": "Chroma", "license": "Apache-2.0", "type": "module", "main": "dist/cjs/chromadb.cjs", "types": "dist/chromadb.d.ts", "module": "dist/chromadb.legacy-esm.js", "exports": {".": {"import": {"types": "./dist/chromadb.d.ts", "default": "./dist/chromadb.mjs"}, "require": {"types": "./dist/cjs/chromadb.d.cts", "default": "./dist/cjs/chromadb.cjs"}}}, "files": ["src", "dist"], "scripts": {"test": "jest --runInBand", "test:functional": "jest --runInBand --testPathIgnorePatterns=test/auth.*.test.ts", "test:update": "jest --runInBand --updateSnapshot", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsup", "watch": "tsup --watch", "genapi": "node --loader ts-node/esm scripts/gen-api.ts", "prettier": "prettier --write .", "release": "run-s build test && npm publish", "release_alpha": "run-s build test && npm publish --tag alpha", "release_dev": "run-s build test && npm version ${NEW_VERSION} --no-git-tag-version && npm publish"}, "dependencies": {"semver": "^7.7.1"}, "devDependencies": {"@hey-api/client-fetch": "^0.10.0", "@hey-api/openapi-ts": "^0.67.3", "@jest/globals": "^29.7.0", "@jest/types": "^29.6.3", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.0", "@types/node": "^20.8.10", "@types/semver": "^7.7.0", "@types/wait-on": "^5.3.4", "bcrypt": "^5.1.1", "chalk": "^4.1.2", "jest": "^29.5.0", "npm-run-all": "^4.1.5", "prettier": "2.8.7", "rimraf": "^5.0.0", "testcontainers": "^10.9.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "tsd": "^0.28.1", "tsup": "^8.3.5", "typescript": "^5.0.4", "wait-on": "^8.0.3", "@chroma-core/default-embed": "workspace:^"}, "optionalDependencies": {"chromadb-js-bindings-darwin-arm64": "^1.0.0", "chromadb-js-bindings-darwin-x64": "^1.0.0", "chromadb-js-bindings-linux-arm64-gnu": "^1.0.0", "chromadb-js-bindings-linux-x64-gnu": "^1.0.0", "chromadb-js-bindings-win32-x64-msvc": "^1.0.0"}, "engines": {"node": ">=20"}, "bin": {"chroma": "dist/cli.mjs"}}