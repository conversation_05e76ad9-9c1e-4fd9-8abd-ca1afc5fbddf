{"chat_model": {"provider": "anthropic", "name": "claude-3-5-sonnet-20241022", "ctx_length": 200000, "max_tokens": 8192, "temperature": 0.7, "ctx_window_ratio": 0.8}, "utility_model": {"provider": "anthropic", "name": "claude-3-haiku-20240307", "ctx_length": 200000, "max_tokens": 4096, "temperature": 0.3}, "embedding_model": {"provider": "huggingface", "name": "sentence-transformers/all-MiniLM-L6-v2", "ctx_length": 8191}, "speech_to_text": {"provider": "whisper", "model_size": "large", "language": "nl", "silence_threshold": 0.2, "silence_duration": 1200, "waiting_timeout": 3000, "dutch_optimization": true}, "text_to_speech": {"enabled": true, "provider": "macos", "voice": "<PERSON>", "speed": 180, "volume": 0.8, "language": "nl", "auto_speak_responses": true, "filter_markdown": true, "max_length": 500}, "agent_prompts_subdir": "default", "agent_memory_subdir": "leon_main", "agent_knowledge_subdir": "leon_dev", "rfc_auto_docker": false, "rfc_url": "localhost", "rfc_password": "", "rfc_port_http": 55080, "rfc_port_ssh": 55022, "env_api_keys": {"anthropic": "API_KEY_ANTHROPIC", "openrouter": "API_KEY_OPENROUTER", "deepseek": "API_KEY_DEEPSEEK"}, "web_ui": {"port": 50001, "host": "0.0.0.0", "title": "Leon's Agent Zero - AI Development Hub", "branding": {"developer": "<PERSON>", "theme": "dutch_dev", "colors": {"primary": "#ff6600", "secondary": "#0066cc", "accent": "#00a86b"}}}, "leon_dev_settings": {"language": "nl", "development_mode": true, "bot_integration": {"telegram_enabled": true, "whatsapp_enabled": true, "trading_bots": true, "crm_integration": true}, "workflow_automation": {"figma_to_code": true, "pwa_focus": true, "mobile_app_dev": true, "ai_agents_preferred": true}, "code_preferences": {"minimal_comments": true, "direct_usable_code": true, "local_ai_priority": true, "json_strategies": true}, "speech_preferences": {"dutch_pronunciation": true, "technical_term_translation": true, "auto_speech_responses": true, "voice_commands_enabled": true}}, "claude_specific": {"system_prompt_optimization": true, "context_window_usage": "aggressive", "streaming": true, "safety_settings": "balanced", "dutch_language_support": true, "developer_mode": true, "code_execution_priority": "high", "speech_integration": true}}