name: 📦 Release chromadb to PyPI and DockerHub

on:
  push:
    tags:
      - "*"
    branches:
      - main

jobs:
  check-tag:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    outputs:
      tag_matches: ${{ steps.check-tag.outputs.tag_matches }}
    steps:
      - name: Check Tag
        id: check-tag
        run: |
          if [[ ${{ github.event.ref }} =~ ^refs/tags/[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
              echo "tag_matches=true" >> $GITHUB_OUTPUT
          else
            echo "Tag does not match the release tag pattern ([0-9]+\.[0-9]+\.[0-9]+), exiting workflow"
            echo "tag_matches=false" >> $GITHUB_OUTPUT
          fi

  get-version:
    runs-on: blacksmith-4vcpu-ubuntu-2204
    outputs:
      version: ${{ steps.version.outputs.version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.9'
      - name: Install setuptools_scm
        run: python -m pip install setuptools_scm
      - name: Get Release Version
        id: version
        run: echo "version=$(python -m setuptools_scm)" >> $GITHUB_OUTPUT

  python-tests-linux:
    uses: ./.github/workflows/_python-tests.yml
    secrets: inherit
    with:
      python_versions: '["3.9", "3.10", "3.11", "3.12"]'
      property_testing_preset: 'normal'

  python-tests-windows:
    uses: ./.github/workflows/_python-tests.yml
    secrets: inherit
    with:
      python_versions: '["3.9", "3.10", "3.11", "3.12"]'
      property_testing_preset: 'normal'
      runner: '16core-64gb-windows-latest'

  javascript-client-tests:
    name: JavaScript client tests
    uses: ./.github/workflows/_javascript-client-tests.yml

  rust-tests:
    name: Rust tests
    uses: ./.github/workflows/_rust-tests.yml
    secrets: inherit

  go-tests:
    name: Go tests
    uses: ./.github/workflows/_go-tests.yml
    secrets: inherit

  release-docker:
    name: Publish to DockerHub and GHCR
    needs:
      - check-tag
      - get-version
      - python-tests-linux
      - python-tests-windows
      - javascript-client-tests
      - rust-tests
      - go-tests
    uses: ./.github/workflows/_build_release_container.yml
    secrets: inherit
    with:
      tag: ${{ needs.get-version.outputs.version }}
      tag_as_latest: ${{ needs.check-tag.outputs.tag_matches == 'true' }}
      push: true

  release-pypi:
    name: Publish to PyPI
    needs:
      - check-tag
      - get-version
      - python-tests-linux
      - python-tests-windows
      - javascript-client-tests
      - rust-tests
      - go-tests
    uses: ./.github/workflows/_build_release_pypi.yml
    secrets: inherit
    with:
      publish_to_test_pypi: true
      publish_to_pypi: ${{ needs.check-tag.outputs.tag_matches == 'true' }}
      version: ${{ needs.get-version.outputs.version }}

  release-thin-pypi:
    name: Publish thin client to PyPI
    runs-on: blacksmith-4vcpu-ubuntu-2204
    needs:
      - check-tag
      - python-tests-linux
      - python-tests-windows
      - javascript-client-tests
      - rust-tests
      - go-tests
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Python
        uses: ./.github/actions/python
        with:
          python-version: '3.12'
      - name: Build Client
        run: ./clients/python/build_python_thin_client.sh
      - name: Test Client Package
        run: bin/test-package/test-thin-client-package.sh dist/*.tar.gz
      - name: Install setuptools_scm
        run: python -m pip install setuptools_scm
      - name: Publish to Test PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.TEST_PYPI_PYTHON_CLIENT_PUBLISH_KEY }}
          repository-url: https://test.pypi.org/legacy/
          verbose: 'true'
      - name: Publish to PyPI
        if:  ${{ needs.check-tag.outputs.tag_matches == 'true' }}
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_PYTHON_CLIENT_PUBLISH_KEY }}
          verbose: 'true'

  release-github:
    name: Make GitHub release
    runs-on: blacksmith-4vcpu-ubuntu-2204
    needs:
      - check-tag
      - get-version
      - release-docker
      - release-pypi
      - release-thin-pypi
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Download artifact
        uses: actions/download-artifact@v4
        with:
          pattern: wheels-*
          path: dist
      - name: Get current date
        id: builddate
        run: echo "builddate=$(date +'%Y-%m-%dT%H:%M')" >> $GITHUB_OUTPUT
      - name: Release Tagged Version
        uses: ncipollo/release-action@v1.14.0
        if: ${{ needs.check-tag.outputs.tag_matches == 'true' }}
        with:
          body: |
            Version: `${{needs.get-version.outputs.version}}`
            Git ref: `${{github.ref}}`
            Build Date: `${{steps.builddate.outputs.builddate}}`
            PIP Package: `chroma-${{needs.get-version.outputs.version}}.tar.gz`
            Github Container Registry Image: `${{ env.GHCR_IMAGE_NAME }}:${{ needs.get-version.outputs.version }}`
            DockerHub Image: `${{ env.DOCKERHUB_IMAGE_NAME }}:${{ needs.get-version.outputs.version }}`
          artifacts: "dist/*"
          prerelease: false
          makeLatest: true
          generateReleaseNotes: true
      - name: Update Tag
        uses: richardsimko/update-tag@v1.0.5
        if: ${{ needs.check-tag.outputs.tag_matches != 'true' }}
        with:
          tag_name: latest
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Release Latest
        uses: ncipollo/release-action@v1.14.0
        if: ${{ needs.check-tag.outputs.tag_matches != 'true' }}
        with:
          tag: "latest"
          name: "Latest"
          body: |
            Version: `${{needs.get-version.outputs.version}}`
            Git ref: `${{github.ref}}`
            Build Date: `${{steps.builddate.outputs.builddate}}`
            PIP Package: `chroma-${{needs.get-version.outputs.version}}.tar.gz`
            Github Container Registry Image: `${{ env.GHCR_IMAGE_NAME }}:${{ needs.get-version.outputs.version }}`
            DockerHub Image: `${{ env.DOCKERHUB_IMAGE_NAME }}:${{ needs.get-version.outputs.version }}`
          artifacts: "dist/*"
          allowUpdates: true
          removeArtifacts: true
          prerelease: true

  release-hosted-control-plane:
    name: Release to Chroma Cloud control plane
    # depends on release-github because it updates the tag to latest, which is what will get deployed
    needs:
      - release-github
    uses: ./.github/workflows/_deploy.yml
    with:
      plane: control
    secrets: inherit

  release-hosted-data-plane:
    name: Release to Chroma Cloud data plane
    # depends on release-github because it updates the tag to latest, which is what will get deployed
    needs:
      - release-github
    uses: ./.github/workflows/_deploy.yml
    with:
      plane: data
    secrets: inherit

  release-docs:
    name: Deploy docs to Vercel
    runs-on: blacksmith-4vcpu-ubuntu-2204
    needs:
      - check-tag
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v4
        with:
          node-version: "18.x"
          registry-url: "https://registry.npmjs.org"
      - name: Install vercel
        run: npm install -g vercel
      - name: Deploy
        run: vercel deploy --token ${{ secrets.VERCEL_TOKEN }} ${{ needs.check-tag.outputs.tag_matches == 'true' && '--prod' || '' }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_DOCS_PROJECT_ID }}
