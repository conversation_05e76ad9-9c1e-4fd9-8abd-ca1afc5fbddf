{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "current_dir = os.getcwd()\n", "os.chdir(\"../../\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["name='test_collection' id=UUID('97c86f7b-6197-4ebe-bf97-9b73fceccdef') metadata=None tenant='default_tenant' database='default_database'\n", "default_tenant\n", "default_database\n"]}], "source": ["\n", "import chromadb\n", "\n", "client = chromadb.PersistentClient()\n", "\n", "col = client.get_or_create_collection('test_collection')\n", "print(col)\n", "\n", "\n", "col1=client.get_collection(id=col.id)\n", "\n", "print(col1.tenant)\n", "assert col1.id == col.id\n", "\n", "assert col1.tenant == col.tenant\n", "assert col1.name == col.name\n", "assert col1.database == col.database\n", "print(col1.database)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.2"}}, "nbformat": 4, "nbformat_minor": 2}