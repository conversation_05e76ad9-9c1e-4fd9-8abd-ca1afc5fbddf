{"include": ["src"], "compilerOptions": {"declaration": true, "module": "ESNext", "lib": ["ES2020", "DOM"], "outDir": "dist/main", "sourceMap": true, "target": "ES2020", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "Node", "forceConsistentCasingInFileNames": true, "stripInternal": true, "paths": {"@internal/chromadb-core": ["../chromadb-core/src"]}, "resolveJsonModule": true}}