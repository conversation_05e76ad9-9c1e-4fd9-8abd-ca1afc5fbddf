name: 📦 Development Release JavaScript client

on:
  push:
    branches:
      - main
jobs:
  test:
    name: JavaScript client tests
    uses: ./.github/workflows/_javascript-client-tests.yml
  release-dev:
    needs: test
    strategy:
      matrix:
        registry: [ "https://npm.pkg.github.com" ]
    runs-on: blacksmith-4vcpu-ubuntu-2204
    if: ${{ github.ref == 'refs/heads/main' }}
    permissions: write-all
    steps:
      - name: Check if tag matches the pattern
        id: check-tag
        run: |
          # we don't necessarily need this
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "Push to main branch, releasing dev version to GH packages"
            echo "NPM_SCRIPT=release_dev" >> "$GITHUB_ENV"
          else
            echo "The ref does not point to main, exiting workflow" # we alredy make the check above but this is a good practice
            exit 1
          fi
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18.x"
          registry-url: ${{ matrix.registry }}
          check-latest: false
          token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'pnpm'
          cache-dependency-path: 'clients/js/pnpm-lock.yaml'

      - name: Install dependencies
        run: pnpm install --no-frozen-lockfile
        working-directory: ./clients/js/

      - name: Build packages
        run: pnpm build
        working-directory: ./clients/js/

      - name: Generate Dev Version
        id: dev-version
        run: |
          set -e
          # Generate a dev tag using commit short sha and run id
          COMMIT_SHA=$(git rev-parse --short HEAD)
          DEV_TAG="dev.${COMMIT_SHA}-${GITHUB_RUN_ID}"
          echo "DEV_TAG=${DEV_TAG}" >> "$GITHUB_ENV"

          # Update each package's version with dev tag
          for PKG_DIR in packages/chromadb packages/chromadb-client; do
            PKG_PATH="./${PKG_DIR}/package.json"
            # Get current version
            CURRENT_VERSION=$(node -p "require('${PKG_PATH}').version")
            # Create full version with dev tag
            BASE_VERSION=$(echo $CURRENT_VERSION | cut -f1,2 -d.)
            PATCH_VERSION=$(echo $CURRENT_VERSION | cut -f3 -d.)
            # bump patch version
            NEW_PATCH_VERSION=$((PATCH_VERSION + 1))
            NEW_VERSION="${BASE_VERSION}.${NEW_PATCH_VERSION}-${DEV_TAG}"

            # Update package.json with new version
            jq --arg version "$NEW_VERSION" '.version = $version' $PKG_PATH > tmp.$$.json && mv tmp.$$.json $PKG_PATH
            echo "Updated ${PKG_DIR} to version ${NEW_VERSION}"
          done
        working-directory: ./clients/js/

      - name: Update package.json with organization scope
        run: |
          ORG_NAME="@chroma-core"
          # Update chromadb package
          CHROMADB_PKG="./packages/chromadb/package.json"
          PACKAGE_NAME=$(jq -r '.name' $CHROMADB_PKG)
          jq --arg org "$ORG_NAME" --arg name "$PACKAGE_NAME" '.name = "\($org)/\($name)"' $CHROMADB_PKG > tmp.$$.json && mv tmp.$$.json $CHROMADB_PKG

          # Update chromadb-client package
          CLIENT_PKG="./packages/chromadb-client/package.json"
          PACKAGE_NAME=$(jq -r '.name' $CLIENT_PKG)
          jq --arg org "$ORG_NAME" --arg name "$PACKAGE_NAME" '.name = "\($org)/\($name)"' $CLIENT_PKG > tmp.$$.json && mv tmp.$$.json $CLIENT_PKG
        working-directory: ./clients/js/

      - name: Publish dev packages
        run: pnpm publish -r --access public --no-git-checks --tag dev
        working-directory: ./clients/js/
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
