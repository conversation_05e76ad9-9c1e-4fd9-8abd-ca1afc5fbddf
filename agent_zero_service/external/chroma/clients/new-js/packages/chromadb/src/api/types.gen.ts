// This file is auto-generated by @hey-api/openapi-ts

export type AddCollectionRecordsPayload = {
  documents?: Array<string | null> | null;
  embeddings?: null | EmbeddingsPayload;
  ids: Array<string>;
  metadatas?: Array<null | HashMap> | null;
  uris?: Array<string | null> | null;
};

export type AddCollectionRecordsResponse = {
  [key: string]: unknown;
};

export type ChecklistResponse = {
  max_batch_size: number;
  supports_base64_encoding: boolean;
};

export type Collection = {
  configuration_json: CollectionConfiguration;
  database: string;
  dimension?: number | null;
  id: CollectionUuid;
  log_position: number;
  metadata?: null | HashMap;
  name: string;
  tenant: string;
  version: number;
};

export type CollectionConfiguration = {
  embedding_function?: null | EmbeddingFunctionConfiguration;
  hnsw?: null | HnswConfiguration;
  spann?: null | SpannConfiguration;
};

/**
 * CollectionUuid is a wrapper around Uuid to provide a type for the collection id.
 */
export type CollectionUuid = string;

export type CreateCollectionPayload = {
  configuration?: null | CollectionConfiguration;
  get_or_create?: boolean;
  metadata?: null | HashMap;
  name: string;
};

export type CreateDatabasePayload = {
  name: string;
};

export type CreateDatabaseResponse = {
  [key: string]: unknown;
};

export type CreateTenantPayload = {
  name: string;
};

export type CreateTenantResponse = {
  [key: string]: unknown;
};

export type Database = {
  id: string;
  name: string;
  tenant: string;
};

export type DeleteCollectionRecordsPayload = RawWhereFields & {
  ids?: Array<string> | null;
};

export type DeleteCollectionRecordsResponse = {
  [key: string]: unknown;
};

export type DeleteDatabaseResponse = {
  [key: string]: unknown;
};

export type EmbeddingFunctionConfiguration =
  | {
      type: "legacy";
    }
  | (EmbeddingFunctionNewConfiguration & {
      type: "known";
    });

export type EmbeddingFunctionNewConfiguration = {
  config: unknown;
  name: string;
};

export type EmbeddingsPayload = Array<Array<number>> | Array<string>;

export type ErrorResponse = {
  error: string;
  message: string;
};

export type ForkCollectionPayload = {
  new_name: string;
};

export type GetRequestPayload = RawWhereFields & {
  ids?: Array<string> | null;
  include?: IncludeList;
  limit?: number | null;
  offset?: number | null;
};

export type GetResponse = {
  documents?: Array<string | null> | null;
  embeddings?: Array<Array<number>> | null;
  ids: Array<string>;
  include: Array<Include>;
  metadatas?: Array<null | HashMap> | null;
  uris?: Array<string | null> | null;
};

export type GetTenantResponse = {
  name: string;
};

export type GetUserIdentityResponse = {
  databases: Array<string>;
  tenant: string;
  user_id: string;
};

export type HashMap = {
  [key: string]: boolean | number | string | null;
};

export type HeartbeatResponse = {
  "nanosecond heartbeat": number;
};

export type HnswConfiguration = {
  ef_construction?: number | null;
  ef_search?: number | null;
  max_neighbors?: number | null;
  resize_factor?: number | null;
  space?: HnswSpace;
  sync_threshold?: number | null;
};

export type HnswSpace = "l2" | "cosine" | "ip";

export type Include =
  | "distances"
  | "documents"
  | "embeddings"
  | "metadatas"
  | "uris";

export type IncludeList = Array<Include>;

export type QueryRequestPayload = RawWhereFields & {
  ids?: Array<string> | null;
  include?: IncludeList;
  n_results?: number | null;
  query_embeddings: Array<Array<number>>;
};

export type QueryResponse = {
  distances?: Array<Array<number | null>> | null;
  documents?: Array<Array<string | null>> | null;
  embeddings?: Array<Array<Array<number> | null>> | null;
  ids: Array<Array<string>>;
  include: Array<Include>;
  metadatas?: Array<Array<null | HashMap>> | null;
  uris?: Array<Array<string | null>> | null;
};

export type RawWhereFields = {
  where?: unknown;
  where_document?: unknown;
};

export type SpannConfiguration = {
  ef_construction?: number | null;
  ef_search?: number | null;
  max_neighbors?: number | null;
  merge_threshold?: number | null;
  reassign_neighbor_count?: number | null;
  search_nprobe?: number | null;
  space?: HnswSpace;
  split_threshold?: number | null;
  write_nprobe?: number | null;
};

export type UpdateCollectionConfiguration = {
  embedding_function?: null | EmbeddingFunctionConfiguration;
  hnsw?: null | UpdateHnswConfiguration;
  spann?: null | SpannConfiguration;
};

export type UpdateCollectionPayload = {
  new_configuration?: null | UpdateCollectionConfiguration;
  new_metadata?: null | HashMap;
  new_name?: string | null;
};

export type UpdateCollectionRecordsPayload = {
  documents?: Array<string | null> | null;
  embeddings?: null | UpdateEmbeddingsPayload;
  ids: Array<string>;
  metadatas?: Array<null | HashMap> | null;
  uris?: Array<string | null> | null;
};

export type UpdateCollectionRecordsResponse = {
  [key: string]: unknown;
};

export type UpdateCollectionResponse = {
  [key: string]: unknown;
};

export type UpdateEmbeddingsPayload =
  | Array<Array<number> | null>
  | Array<string | null>;

export type UpdateHnswConfiguration = {
  batch_size?: number | null;
  ef_search?: number | null;
  max_neighbors?: number | null;
  num_threads?: number | null;
  resize_factor?: number | null;
  sync_threshold?: number | null;
};

export type UpsertCollectionRecordsPayload = {
  documents?: Array<string | null> | null;
  embeddings?: null | EmbeddingsPayload;
  ids: Array<string>;
  metadatas?: Array<null | HashMap> | null;
  uris?: Array<string | null> | null;
};

export type UpsertCollectionRecordsResponse = {
  [key: string]: unknown;
};

export type Vec = Array<{
  configuration_json: CollectionConfiguration;
  database: string;
  dimension?: number | null;
  id: CollectionUuid;
  log_position: number;
  metadata?: null | HashMap;
  name: string;
  tenant: string;
  version: number;
}>;

export type U32 = number;

export type GetUserIdentityData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v2/auth/identity";
};

export type GetUserIdentityErrors = {
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type GetUserIdentityError =
  GetUserIdentityErrors[keyof GetUserIdentityErrors];

export type GetUserIdentityResponses = {
  /**
   * Get user identity
   */
  200: GetUserIdentityResponse;
};

export type GetUserIdentityResponse2 =
  GetUserIdentityResponses[keyof GetUserIdentityResponses];

export type HealthcheckData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v2/healthcheck";
};

export type HealthcheckErrors = {
  /**
   * Service Unavailable
   */
  503: ErrorResponse;
};

export type HealthcheckError = HealthcheckErrors[keyof HealthcheckErrors];

export type HealthcheckResponses = {
  /**
   * Success
   */
  200: string;
};

export type HealthcheckResponse =
  HealthcheckResponses[keyof HealthcheckResponses];

export type HeartbeatData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v2/heartbeat";
};

export type HeartbeatErrors = {
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type HeartbeatError = HeartbeatErrors[keyof HeartbeatErrors];

export type HeartbeatResponses = {
  /**
   * Success
   */
  200: HeartbeatResponse;
};

export type HeartbeatResponse2 = HeartbeatResponses[keyof HeartbeatResponses];

export type PreFlightChecksData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v2/pre-flight-checks";
};

export type PreFlightChecksErrors = {
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type PreFlightChecksError =
  PreFlightChecksErrors[keyof PreFlightChecksErrors];

export type PreFlightChecksResponses = {
  /**
   * Pre flight checks
   */
  200: ChecklistResponse;
};

export type PreFlightChecksResponse =
  PreFlightChecksResponses[keyof PreFlightChecksResponses];

export type ResetData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v2/reset";
};

export type ResetErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type ResetError = ResetErrors[keyof ResetErrors];

export type ResetResponses = {
  /**
   * Reset successful
   */
  200: boolean;
};

export type ResetResponse = ResetResponses[keyof ResetResponses];

export type CreateTenantData = {
  body: CreateTenantPayload;
  path?: never;
  query?: never;
  url: "/api/v2/tenants";
};

export type CreateTenantErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CreateTenantError = CreateTenantErrors[keyof CreateTenantErrors];

export type CreateTenantResponses = {
  /**
   * Tenant created successfully
   */
  200: CreateTenantResponse;
};

export type CreateTenantResponse2 =
  CreateTenantResponses[keyof CreateTenantResponses];

export type GetTenantData = {
  body?: never;
  path: {
    /**
     * Tenant name or ID to retrieve
     */
    tenant_name: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant_name}";
};

export type GetTenantErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Tenant not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type GetTenantError = GetTenantErrors[keyof GetTenantErrors];

export type GetTenantResponses = {
  /**
   * Tenant found
   */
  200: GetTenantResponse;
};

export type GetTenantResponse2 = GetTenantResponses[keyof GetTenantResponses];

export type ListDatabasesData = {
  body?: never;
  path: {
    /**
     * Tenant ID to list databases for
     */
    tenant: string;
  };
  query?: {
    /**
     * Limit for pagination
     */
    limit?: number;
    /**
     * Offset for pagination
     */
    offset?: number;
  };
  url: "/api/v2/tenants/{tenant}/databases";
};

export type ListDatabasesErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type ListDatabasesError = ListDatabasesErrors[keyof ListDatabasesErrors];

export type ListDatabasesResponses = {
  /**
   * List of databases
   */
  200: Vec;
};

export type ListDatabasesResponse =
  ListDatabasesResponses[keyof ListDatabasesResponses];

export type CreateDatabaseData = {
  body: CreateDatabasePayload;
  path: {
    /**
     * Tenant ID to associate with the new database
     */
    tenant: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases";
};

export type CreateDatabaseErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CreateDatabaseError =
  CreateDatabaseErrors[keyof CreateDatabaseErrors];

export type CreateDatabaseResponses = {
  /**
   * Database created successfully
   */
  200: CreateDatabaseResponse;
};

export type CreateDatabaseResponse2 =
  CreateDatabaseResponses[keyof CreateDatabaseResponses];

export type DeleteDatabaseData = {
  body?: never;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Name of the database to delete
     */
    database: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}";
};

export type DeleteDatabaseErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Database not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type DeleteDatabaseError =
  DeleteDatabaseErrors[keyof DeleteDatabaseErrors];

export type DeleteDatabaseResponses = {
  /**
   * Database deleted successfully
   */
  200: DeleteDatabaseResponse;
};

export type DeleteDatabaseResponse2 =
  DeleteDatabaseResponses[keyof DeleteDatabaseResponses];

export type GetDatabaseData = {
  body?: never;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Name of the database to retrieve
     */
    database: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}";
};

export type GetDatabaseErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Database not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type GetDatabaseError = GetDatabaseErrors[keyof GetDatabaseErrors];

export type GetDatabaseResponses = {
  /**
   * Database retrieved successfully
   */
  200: Database;
};

export type GetDatabaseResponse =
  GetDatabaseResponses[keyof GetDatabaseResponses];

export type ListCollectionsData = {
  body?: never;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name to list collections from
     */
    database: string;
  };
  query?: {
    /**
     * Limit for pagination
     */
    limit?: number;
    /**
     * Offset for pagination
     */
    offset?: number;
  };
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections";
};

export type ListCollectionsErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type ListCollectionsError =
  ListCollectionsErrors[keyof ListCollectionsErrors];

export type ListCollectionsResponses = {
  /**
   * List of collections
   */
  200: Vec;
};

export type ListCollectionsResponse =
  ListCollectionsResponses[keyof ListCollectionsResponses];

export type CreateCollectionData = {
  body: CreateCollectionPayload;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name containing the new collection
     */
    database: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections";
};

export type CreateCollectionErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CreateCollectionError =
  CreateCollectionErrors[keyof CreateCollectionErrors];

export type CreateCollectionResponses = {
  /**
   * Collection created successfully
   */
  200: Collection;
};

export type CreateCollectionResponse =
  CreateCollectionResponses[keyof CreateCollectionResponses];

export type DeleteCollectionData = {
  body?: never;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name
     */
    database: string;
    /**
     * UUID of the collection to delete
     */
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}";
};

export type DeleteCollectionErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type DeleteCollectionError =
  DeleteCollectionErrors[keyof DeleteCollectionErrors];

export type DeleteCollectionResponses = {
  /**
   * Collection deleted successfully
   */
  200: UpdateCollectionResponse;
};

export type DeleteCollectionResponse =
  DeleteCollectionResponses[keyof DeleteCollectionResponses];

export type GetCollectionData = {
  body?: never;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name
     */
    database: string;
    /**
     * UUID of the collection
     */
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}";
};

export type GetCollectionErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type GetCollectionError = GetCollectionErrors[keyof GetCollectionErrors];

export type GetCollectionResponses = {
  /**
   * Collection found
   */
  200: Collection;
};

export type GetCollectionResponse =
  GetCollectionResponses[keyof GetCollectionResponses];

export type UpdateCollectionData = {
  body: UpdateCollectionPayload;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name
     */
    database: string;
    /**
     * UUID of the collection to update
     */
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}";
};

export type UpdateCollectionErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type UpdateCollectionError =
  UpdateCollectionErrors[keyof UpdateCollectionErrors];

export type UpdateCollectionResponses = {
  /**
   * Collection updated successfully
   */
  200: UpdateCollectionResponse;
};

export type UpdateCollectionResponse2 =
  UpdateCollectionResponses[keyof UpdateCollectionResponses];

export type CollectionAddData = {
  body: AddCollectionRecordsPayload;
  path: {
    tenant: string;
    database: string;
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/add";
};

export type CollectionAddErrors = {
  /**
   * Invalid data for collection addition
   */
  400: unknown;
};

export type CollectionAddResponses = {
  /**
   * Collection added successfully
   */
  201: AddCollectionRecordsResponse;
};

export type CollectionAddResponse =
  CollectionAddResponses[keyof CollectionAddResponses];

export type CollectionCountData = {
  body?: never;
  path: {
    /**
     * Tenant ID for the collection
     */
    tenant: string;
    /**
     * Database containing this collection
     */
    database: string;
    /**
     * Collection ID whose records are counted
     */
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/count";
};

export type CollectionCountErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CollectionCountError =
  CollectionCountErrors[keyof CollectionCountErrors];

export type CollectionCountResponses = {
  /**
   * Number of records in the collection
   */
  200: U32;
};

export type CollectionCountResponse =
  CollectionCountResponses[keyof CollectionCountResponses];

export type CollectionDeleteData = {
  body: DeleteCollectionRecordsPayload;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name
     */
    database: string;
    /**
     * Collection ID
     */
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/delete";
};

export type CollectionDeleteErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CollectionDeleteError =
  CollectionDeleteErrors[keyof CollectionDeleteErrors];

export type CollectionDeleteResponses = {
  /**
   * Records deleted successfully
   */
  200: DeleteCollectionRecordsResponse;
};

export type CollectionDeleteResponse =
  CollectionDeleteResponses[keyof CollectionDeleteResponses];

export type ForkCollectionData = {
  body: ForkCollectionPayload;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name
     */
    database: string;
    /**
     * UUID of the collection to update
     */
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/fork";
};

export type ForkCollectionErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type ForkCollectionError =
  ForkCollectionErrors[keyof ForkCollectionErrors];

export type ForkCollectionResponses = {
  /**
   * Collection forked successfully
   */
  200: Collection;
};

export type ForkCollectionResponse =
  ForkCollectionResponses[keyof ForkCollectionResponses];

export type CollectionGetData = {
  body: GetRequestPayload;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name for the collection
     */
    database: string;
    /**
     * Collection ID to fetch records from
     */
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/get";
};

export type CollectionGetErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CollectionGetError = CollectionGetErrors[keyof CollectionGetErrors];

export type CollectionGetResponses = {
  /**
   * Records retrieved from the collection
   */
  200: GetResponse;
};

export type CollectionGetResponse =
  CollectionGetResponses[keyof CollectionGetResponses];

export type CollectionQueryData = {
  body: QueryRequestPayload;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name containing the collection
     */
    database: string;
    /**
     * Collection ID to query
     */
    collection_id: string;
  };
  query?: {
    /**
     * Limit for pagination
     */
    limit?: number;
    /**
     * Offset for pagination
     */
    offset?: number;
  };
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/query";
};

export type CollectionQueryErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CollectionQueryError =
  CollectionQueryErrors[keyof CollectionQueryErrors];

export type CollectionQueryResponses = {
  /**
   * Records matching the query
   */
  200: QueryResponse;
};

export type CollectionQueryResponse =
  CollectionQueryResponses[keyof CollectionQueryResponses];

export type CollectionUpdateData = {
  body: UpdateCollectionRecordsPayload;
  path: {
    tenant: string;
    database: string;
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/update";
};

export type CollectionUpdateErrors = {
  /**
   * Collection not found
   */
  404: unknown;
};

export type CollectionUpdateResponses = {
  /**
   * Collection updated successfully
   */
  200: UpdateCollectionRecordsResponse;
};

export type CollectionUpdateResponse =
  CollectionUpdateResponses[keyof CollectionUpdateResponses];

export type CollectionUpsertData = {
  body: UpsertCollectionRecordsPayload;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name
     */
    database: string;
    /**
     * Collection ID
     */
    collection_id: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections/{collection_id}/upsert";
};

export type CollectionUpsertErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Collection not found
   */
  404: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CollectionUpsertError =
  CollectionUpsertErrors[keyof CollectionUpsertErrors];

export type CollectionUpsertResponses = {
  /**
   * Records upserted successfully
   */
  200: UpsertCollectionRecordsResponse;
};

export type CollectionUpsertResponse =
  CollectionUpsertResponses[keyof CollectionUpsertResponses];

export type CountCollectionsData = {
  body?: never;
  path: {
    /**
     * Tenant ID
     */
    tenant: string;
    /**
     * Database name to count collections from
     */
    database: string;
  };
  query?: never;
  url: "/api/v2/tenants/{tenant}/databases/{database}/collections_count";
};

export type CountCollectionsErrors = {
  /**
   * Unauthorized
   */
  401: ErrorResponse;
  /**
   * Server error
   */
  500: ErrorResponse;
};

export type CountCollectionsError =
  CountCollectionsErrors[keyof CountCollectionsErrors];

export type CountCollectionsResponses = {
  /**
   * Count of collections
   */
  200: U32;
};

export type CountCollectionsResponse =
  CountCollectionsResponses[keyof CountCollectionsResponses];

export type VersionData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v2/version";
};

export type VersionResponses = {
  /**
   * Get server version
   */
  200: string;
};

export type VersionResponse = VersionResponses[keyof VersionResponses];

export type ClientOptions = {
  baseUrl: "http://127.0.0.1:8000" | (string & {});
};
