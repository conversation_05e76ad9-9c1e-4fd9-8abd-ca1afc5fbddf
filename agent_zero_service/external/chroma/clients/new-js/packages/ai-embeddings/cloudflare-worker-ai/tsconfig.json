{"compilerOptions": {"target": "es2020", "module": "esnext", "moduleResolution": "bundler", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "declaration": true, "outDir": "dist", "allowSyntheticDefaultImports": true, "composite": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "**/*.test.ts"], "references": [{"path": "../common"}]}