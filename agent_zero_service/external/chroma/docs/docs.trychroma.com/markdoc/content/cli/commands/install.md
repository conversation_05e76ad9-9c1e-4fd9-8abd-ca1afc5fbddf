# Sample Apps

{% Banner type="tip" %}
This CLI command is available on Chroma 1.0.4 and later. 
{% /Banner %}

The Chroma team regularly releases sample AI applications powered by Chroma, which you can use to learn about retrieval, building with AI, and as a jumping-off board for your own projects.

The CLI makes it easy to install and set up the Chroma sample apps on your local machine with the `chroma install` command.

To install a sample app simply run

```terminal
chroma install [app_name]
```

The CLI will walk you through any particular customization you can make, and setting up your environment.

To see a full list of available sample app, use the `list` argument:

```terminal
chroma install --list
```
