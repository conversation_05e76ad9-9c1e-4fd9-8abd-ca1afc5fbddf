// Form Filler Utility for Agent Zero
// Automatically fills forms with provided data

function fillForm(formData) {
    const results = [];
    
    for (const [selector, value] of Object.entries(formData)) {
        try {
            const element = document.querySelector(selector);
            
            if (!element) {
                results.push({
                    selector: selector,
                    success: false,
                    error: 'Element not found'
                });
                continue;
            }
            
            // Handle different input types
            switch (element.type?.toLowerCase()) {
                case 'checkbox':
                case 'radio':
                    element.checked = Boolean(value);
                    break;
                    
                case 'select-one':
                case 'select-multiple':
                    if (element.tagName === 'SELECT') {
                        element.value = value;
                        // Trigger change event
                        element.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                    break;
                    
                case 'file':
                    // File inputs need special handling
                    results.push({
                        selector: selector,
                        success: false,
                        error: 'File inputs require special handling'
                    });
                    continue;
                    
                default:
                    // Text inputs, textareas, etc.
                    element.value = value;
                    // Trigger input and change events
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    break;
            }
            
            results.push({
                selector: selector,
                success: true,
                value: value,
                elementType: element.type || element.tagName
            });
            
        } catch (error) {
            results.push({
                selector: selector,
                success: false,
                error: error.message
            });
        }
    }
    
    return {
        totalFields: Object.keys(formData).length,
        successCount: results.filter(r => r.success).length,
        results: results
    };
}

// Submit form after filling
function submitForm(formSelector = 'form') {
    try {
        const form = document.querySelector(formSelector);
        if (form) {
            form.submit();
            return { success: true, message: 'Form submitted' };
        } else {
            return { success: false, error: 'Form not found' };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}

// Get all form fields and their current values
function getFormData(formSelector = 'form') {
    try {
        const form = document.querySelector(formSelector);
        if (!form) {
            return { success: false, error: 'Form not found' };
        }
        
        const formData = {};
        const inputs = form.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            if (input.name || input.id) {
                const key = input.name || input.id;
                
                switch (input.type?.toLowerCase()) {
                    case 'checkbox':
                    case 'radio':
                        formData[key] = input.checked;
                        break;
                    case 'file':
                        formData[key] = input.files ? Array.from(input.files).map(f => f.name) : [];
                        break;
                    default:
                        formData[key] = input.value;
                        break;
                }
            }
        });
        
        return {
            success: true,
            formData: formData,
            fieldCount: Object.keys(formData).length
        };
        
    } catch (error) {
        return { success: false, error: error.message };
    }
}
