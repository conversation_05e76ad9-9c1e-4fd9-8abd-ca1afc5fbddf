/* eslint-disable */
// tslint:disable
/**
 * chroma-frontend
 *
 *
 * OpenAPI spec version: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator+.
 * https://github.com/karlvr/openapi-generator-plus
 * Do not edit the class manually.
 */

export namespace Api {
  export interface AddCollectionRecordsPayload {
    documents?: (string | null)[] | null;
    embeddings?: Api.EmbeddingsPayload | null;
    ids: string[];
    metadatas?:
      | ({ [name: string]: boolean | number | number | string } | null)[]
      | null;
    uris?: (string | null)[] | null;
  }

  export interface AddCollectionRecordsResponse {}

  export interface ChecklistResponse {
    /**
     * @type {number}
     * @memberof ChecklistResponse
     * minimum: 0
     */
    max_batch_size: number;
    supports_base64_encoding: boolean;
  }

  export interface Collection {
    configuration_json: Api.CollectionConfiguration;
    database: string;
    /**
     * @type {number | null}
     * @memberof Collection
     */
    dimension?: number | null;
    /**
     * @description <p>CollectionUuid is a wrapper around Uuid to provide a type for the collection id.</p>
     * @type {string}
     * @memberof Collection
     */
    id: string;
    /**
     * @type {number}
     * @memberof Collection
     */
    log_position: number;
    metadata?: { [name: string]: boolean | number | number | string } | null;
    name: string;
    tenant: string;
    /**
     * @type {number}
     * @memberof Collection
     */
    version: number;
  }

  export interface CollectionConfiguration {
    embedding_function?: Api.EmbeddingFunctionConfiguration | null;
    hnsw?: Api.HnswConfiguration | null;
    spann?: Api.SpannConfiguration | null;
  }

  export interface CreateCollectionPayload {
    configuration?: Api.CollectionConfiguration | null;
    get_or_create?: boolean;
    metadata?: { [name: string]: boolean | number | number | string } | null;
    name: string;
  }

  export interface CreateDatabasePayload {
    name: string;
  }

  export interface CreateDatabaseResponse {}

  export interface CreateTenantPayload {
    name: string;
  }

  export interface CreateTenantResponse {}

  export interface Database {
    id: string;
    name: string;
    tenant: string;
  }

  export interface DeleteCollectionRecordsPayload extends Api.RawWhereFields {
    ids?: string[] | null;
  }

  export interface DeleteCollectionRecordsResponse {}

  export interface DeleteDatabaseResponse {}

  export type EmbeddingFunctionConfiguration =
    | Api.EmbeddingFunctionConfiguration.ObjectValue
    | Api.EmbeddingFunctionConfiguration.AllofValue;

  /**
   * @export
   * @namespace EmbeddingFunctionConfiguration
   */
  export namespace EmbeddingFunctionConfiguration {
    export interface ObjectValue {
      type: Api.EmbeddingFunctionConfiguration.ObjectValue.TypeEnum;
    }

    /**
     * @export
     * @namespace ObjectValue
     */
    export namespace ObjectValue {
      export enum TypeEnum {
        Legacy = "legacy",
      }
    }

    export interface AllofValue extends Api.EmbeddingFunctionNewConfiguration {
      type: Api.EmbeddingFunctionConfiguration.AllofValue.TypeEnum;
    }

    /**
     * @export
     * @namespace AllofValue
     */
    export namespace AllofValue {
      export enum TypeEnum {
        Known = "known",
      }
    }
  }

  export interface EmbeddingFunctionNewConfiguration {
    config: unknown;
    name: string;
  }

  export type EmbeddingsPayload = number[][] | string[];

  export interface ErrorResponse {
    error: string;
    message: string;
  }

  export interface ForkCollectionPayload {
    new_name: string;
  }

  export interface GetRequestPayload extends Api.RawWhereFields {
    ids?: string[] | null;
    include?: Api.Include[];
    /**
     * @type {number | null}
     * @memberof GetRequestPayload
     * minimum: 0
     */
    limit?: number | null;
    /**
     * @type {number | null}
     * @memberof GetRequestPayload
     * minimum: 0
     */
    offset?: number | null;
  }

  export interface GetResponse {
    documents?: (string | null)[] | null;
    embeddings?: number[][] | null;
    ids: string[];
    include: Api.Include[];
    metadatas?:
      | ({ [name: string]: boolean | number | number | string } | null)[]
      | null;
    uris?: (string | null)[] | null;
  }

  export interface GetTenantResponse {
    name: string;
  }

  export interface GetUserIdentityResponse {
    databases: string[];
    tenant: string;
    user_id: string;
  }

  export interface HeartbeatResponse {
    /**
     * @type {number}
     * @memberof HeartbeatResponse
     * minimum: 0
     */
    "nanosecond heartbeat": number;
  }

  export interface HnswConfiguration {
    /**
     * @type {number | null}
     * @memberof HnswConfiguration
     * minimum: 0
     */
    ef_construction?: number | null;
    /**
     * @type {number | null}
     * @memberof HnswConfiguration
     * minimum: 0
     */
    ef_search?: number | null;
    /**
     * @type {number | null}
     * @memberof HnswConfiguration
     * minimum: 0
     */
    max_neighbors?: number | null;
    /**
     * @type {number | null}
     * @memberof HnswConfiguration
     */
    resize_factor?: number | null;
    space?: Api.HnswSpace;
    /**
     * @type {number | null}
     * @memberof HnswConfiguration
     * minimum: 0
     */
    sync_threshold?: number | null;
  }

  export enum HnswSpace {
    L2 = "l2",
    Cosine = "cosine",
    Ip = "ip",
  }

  export enum Include {
    Distances = "distances",
    Documents = "documents",
    Embeddings = "embeddings",
    Metadatas = "metadatas",
    Uris = "uris",
  }

  export interface QueryRequestPayload extends Api.RawWhereFields {
    ids?: string[] | null;
    include?: Api.Include[];
    /**
     * @type {number | null}
     * @memberof QueryRequestPayload
     * minimum: 0
     */
    n_results?: number | null;
    query_embeddings: number[][];
  }

  export interface QueryResponse {
    distances?: (number | null)[][] | null;
    documents?: (string | null)[][] | null;
    embeddings?: (number[] | null)[][] | null;
    ids: string[][];
    include: Api.Include[];
    metadatas?:
      | ({ [name: string]: boolean | number | number | string } | null)[][]
      | null;
    uris?: (string | null)[][] | null;
  }

  export interface RawWhereFields {
    where?: unknown;
    where_document?: unknown;
  }

  export interface SpannConfiguration {
    /**
     * @type {number | null}
     * @memberof SpannConfiguration
     * minimum: 0
     */
    ef_construction?: number | null;
    /**
     * @type {number | null}
     * @memberof SpannConfiguration
     * minimum: 0
     */
    ef_search?: number | null;
    /**
     * @type {number | null}
     * @memberof SpannConfiguration
     * minimum: 0
     */
    max_neighbors?: number | null;
    /**
     * @type {number | null}
     * @memberof SpannConfiguration
     * minimum: 0
     */
    merge_threshold?: number | null;
    /**
     * @type {number | null}
     * @memberof SpannConfiguration
     * minimum: 0
     */
    reassign_neighbor_count?: number | null;
    /**
     * @type {number | null}
     * @memberof SpannConfiguration
     * minimum: 0
     */
    search_nprobe?: number | null;
    space?: Api.HnswSpace;
    /**
     * @type {number | null}
     * @memberof SpannConfiguration
     * minimum: 0
     */
    split_threshold?: number | null;
    /**
     * @type {number | null}
     * @memberof SpannConfiguration
     * minimum: 0
     */
    write_nprobe?: number | null;
  }

  export interface UpdateCollectionConfiguration {
    embedding_function?: Api.EmbeddingFunctionConfiguration | null;
    hnsw?: Api.UpdateHnswConfiguration | null;
    spann?: Api.SpannConfiguration | null;
  }

  export interface UpdateCollectionPayload {
    new_configuration?: Api.UpdateCollectionConfiguration | null;
    new_metadata?: {
      [name: string]: boolean | number | number | string;
    } | null;
    new_name?: string | null;
  }

  export interface UpdateCollectionRecordsPayload {
    documents?: (string | null)[] | null;
    embeddings?: Api.UpdateEmbeddingsPayload | null;
    ids: string[];
    metadatas?:
      | ({ [name: string]: boolean | number | number | string } | null)[]
      | null;
    uris?: (string | null)[] | null;
  }

  export interface UpdateCollectionRecordsResponse {}

  export interface UpdateCollectionResponse {}

  export type UpdateEmbeddingsPayload = (number[] | null)[] | (string | null)[];

  export interface UpdateHnswConfiguration {
    /**
     * @type {number | null}
     * @memberof UpdateHnswConfiguration
     * minimum: 0
     */
    batch_size?: number | null;
    /**
     * @type {number | null}
     * @memberof UpdateHnswConfiguration
     * minimum: 0
     */
    ef_search?: number | null;
    /**
     * @type {number | null}
     * @memberof UpdateHnswConfiguration
     * minimum: 0
     */
    max_neighbors?: number | null;
    /**
     * @type {number | null}
     * @memberof UpdateHnswConfiguration
     * minimum: 0
     */
    num_threads?: number | null;
    /**
     * @type {number | null}
     * @memberof UpdateHnswConfiguration
     */
    resize_factor?: number | null;
    /**
     * @type {number | null}
     * @memberof UpdateHnswConfiguration
     * minimum: 0
     */
    sync_threshold?: number | null;
  }

  export interface UpsertCollectionRecordsPayload {
    documents?: (string | null)[] | null;
    embeddings?: Api.EmbeddingsPayload | null;
    ids: string[];
    metadatas?:
      | ({ [name: string]: boolean | number | number | string } | null)[]
      | null;
    uris?: (string | null)[] | null;
  }

  export interface UpsertCollectionRecordsResponse {}

  export interface Vec2 {
    configuration_json: Api.CollectionConfiguration;
    database: string;
    /**
     * @type {number | null}
     * @memberof Vec2
     */
    dimension?: number | null;
    /**
     * @description <p>CollectionUuid is a wrapper around Uuid to provide a type for the collection id.</p>
     * @type {string}
     * @memberof Vec2
     */
    id: string;
    /**
     * @type {number}
     * @memberof Vec2
     */
    log_position: number;
    metadata?: { [name: string]: boolean | number | number | string } | null;
    name: string;
    tenant: string;
    /**
     * @type {number}
     * @memberof Vec2
     */
    version: number;
  }
}
