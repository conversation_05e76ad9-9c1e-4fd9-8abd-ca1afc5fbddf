"""
Cache-Augmented Generation (CAG) Integration for Agent Zero
Provides faster, more reliable knowledge retrieval without real-time search
"""

import os
import sys
import json
from typing import Dict, List, Optional, Any
from pathlib import Path

# Add CAG to Python path
cag_path = Path("cag/CAG-main")
if cag_path.exists():
    sys.path.append(str(cag_path))


class CAGManager:
    """Manages Cache-Augmented Generation for Agent Zero"""
    
    def __init__(self, cag_base_dir: str = "cag/CAG-main"):
        self.cag_dir = Path(cag_base_dir)
        self.cache_dir = self.cag_dir / "data_cache"
        self.knowledge_cache = {}
        self.context_limit = 32000  # Adjust based on model context window
        
    def is_cag_available(self) -> bool:
        """Check if CAG is properly installed and available"""
        return (self.cag_dir.exists() and 
                (self.cag_dir / "kvcache.py").exists() and
                (self.cag_dir / "rag.py").exists())
    
    def preload_knowledge_base(self, knowledge_sources: List[str]) -> Dict[str, Any]:
        """Preload knowledge sources into context cache"""
        if not self.is_cag_available():
            return {"error": "CAG not available"}
        
        try:
            # Import CAG modules
            from kvcache import CAGProcessor
            from config import CAGConfig
            
            # Initialize CAG
            config = CAGConfig()
            processor = CAGProcessor(config)
            
            # Process knowledge sources
            cached_knowledge = {}
            total_tokens = 0
            
            for source in knowledge_sources:
                if total_tokens >= self.context_limit:
                    break
                    
                # Process each knowledge source
                processed = processor.process_knowledge(source)
                if processed:
                    cached_knowledge[source] = processed
                    total_tokens += len(processed.split())
            
            self.knowledge_cache = cached_knowledge
            
            return {
                "success": True,
                "sources_loaded": len(cached_knowledge),
                "total_tokens": total_tokens,
                "cache_efficiency": f"{(total_tokens/self.context_limit)*100:.1f}%"
            }
            
        except ImportError:
            return {"error": "CAG modules not found. Run: pip install -r cag/CAG-main/requirements.txt"}
        except Exception as e:
            return {"error": f"CAG preload failed: {str(e)}"}
    
    def query_cached_knowledge(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """Query the cached knowledge without real-time retrieval"""
        if not self.knowledge_cache:
            return {"error": "No knowledge cached. Run preload_knowledge_base() first"}
        
        try:
            # Simple relevance scoring (can be enhanced with embeddings)
            results = []
            query_lower = query.lower()
            
            for source, content in self.knowledge_cache.items():
                # Calculate relevance score
                content_lower = content.lower()
                score = 0
                
                # Count query word matches
                query_words = query_lower.split()
                for word in query_words:
                    score += content_lower.count(word)
                
                if score > 0:
                    results.append({
                        "source": source,
                        "content": content,
                        "relevance_score": score,
                        "preview": content[:200] + "..." if len(content) > 200 else content
                    })
            
            # Sort by relevance and limit results
            results.sort(key=lambda x: x["relevance_score"], reverse=True)
            results = results[:max_results]
            
            return {
                "success": True,
                "query": query,
                "results_count": len(results),
                "results": results
            }
            
        except Exception as e:
            return {"error": f"Query failed: {str(e)}"}
    
    def generate_with_cache(self, prompt: str, use_cached_context: bool = True) -> str:
        """Generate response using cached knowledge context"""
        if not use_cached_context or not self.knowledge_cache:
            return prompt
        
        # Build context from cached knowledge
        context_parts = []
        for source, content in self.knowledge_cache.items():
            context_parts.append(f"## Knowledge from {source}:\n{content}\n")
        
        cached_context = "\n".join(context_parts)
        
        # Combine with prompt
        enhanced_prompt = f"""
# Context (Cached Knowledge):
{cached_context}

# User Query:
{prompt}

# Instructions:
Use the cached knowledge above to provide a comprehensive and accurate response. 
Reference specific sources when applicable.
"""
        
        return enhanced_prompt
    
    def compare_rag_vs_cag(self, query: str) -> Dict[str, Any]:
        """Compare RAG vs CAG performance for a query"""
        try:
            import time
            
            # Simulate RAG (with retrieval latency)
            rag_start = time.time()
            time.sleep(0.1)  # Simulate retrieval delay
            rag_result = f"RAG result for: {query}"
            rag_time = time.time() - rag_start
            
            # CAG (cached retrieval)
            cag_start = time.time()
            cag_result = self.query_cached_knowledge(query)
            cag_time = time.time() - cag_start
            
            return {
                "query": query,
                "rag": {
                    "time": rag_time,
                    "result": rag_result,
                    "latency": "High (real-time retrieval)"
                },
                "cag": {
                    "time": cag_time,
                    "result": cag_result,
                    "latency": "Low (cached retrieval)"
                },
                "speedup": f"{rag_time/cag_time:.2f}x faster" if cag_time > 0 else "N/A"
            }
            
        except Exception as e:
            return {"error": f"Comparison failed: {str(e)}"}
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get statistics about the current cache"""
        if not self.knowledge_cache:
            return {"error": "No cache loaded"}
        
        total_content = sum(len(content) for content in self.knowledge_cache.values())
        avg_content_length = total_content / len(self.knowledge_cache) if self.knowledge_cache else 0
        
        return {
            "sources_cached": len(self.knowledge_cache),
            "total_characters": total_content,
            "average_content_length": int(avg_content_length),
            "context_utilization": f"{(total_content/self.context_limit)*100:.1f}%",
            "sources": list(self.knowledge_cache.keys())
        }
    
    def clear_cache(self) -> Dict[str, str]:
        """Clear the knowledge cache"""
        self.knowledge_cache.clear()
        return {"message": "Cache cleared successfully"}


# Helper functions for Agent Zero integration
def setup_cag_for_agent_zero(knowledge_sources: List[str]) -> Dict[str, Any]:
    """Setup CAG with Agent Zero's knowledge sources"""
    cag = CAGManager()
    
    # Default knowledge sources for Agent Zero
    default_sources = [
        "prompts/default/",
        "knowledge/default/",
        "knowledge/custom/",
        "docs/",
        "lib/"
    ]
    
    all_sources = list(set(knowledge_sources + default_sources))
    return cag.preload_knowledge_base(all_sources)


def cag_enhanced_query(query: str, use_professional_prompts: bool = True) -> str:
    """Enhanced query using CAG and professional prompts"""
    cag = CAGManager()
    
    # Get cached knowledge
    cached_result = cag.query_cached_knowledge(query)
    
    # Optionally enhance with professional prompts
    if use_professional_prompts:
        from lib.ai.system_prompt_loader import SystemPromptLoader
        prompt_loader = SystemPromptLoader()
        
        # Determine best tool for the query
        if "code" in query.lower() or "programming" in query.lower():
            enhanced_prompt = prompt_loader.get_cursor_system_prompt()
        elif "ui" in query.lower() or "interface" in query.lower():
            enhanced_prompt = prompt_loader.get_v0_system_prompt()
        else:
            enhanced_prompt = prompt_loader.get_devin_system_prompt()
        
        # Combine CAG results with professional prompts
        if cached_result.get("success"):
            context = "\n".join([r["content"] for r in cached_result["results"]])
            return f"{enhanced_prompt}\n\nContext:\n{context}\n\nQuery: {query}"
    
    return cag.generate_with_cache(query)


# Example usage
if __name__ == "__main__":
    cag = CAGManager()
    
    if cag.is_cag_available():
        print("✅ CAG is available")
        
        # Test knowledge loading
        result = cag.preload_knowledge_base(["docs/", "prompts/"])
        print("Preload result:", result)
        
        # Test query
        query_result = cag.query_cached_knowledge("How to use Agent Zero?")
        print("Query result:", query_result)
        
    else:
        print("❌ CAG not available. Please install CAG dependencies.")
