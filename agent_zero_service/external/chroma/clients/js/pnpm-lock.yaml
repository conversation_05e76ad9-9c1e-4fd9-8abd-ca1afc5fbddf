lockfileVersion: "9.0"

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

overrides:
  whatwg-url: 13.0.0

importers:
  .:
    dependencies:
      "@google/generative-ai":
        specifier: ^0.1.1
        version: 0.1.3
      cliui:
        specifier: ^8.0.1
        version: 8.0.1
      cohere-ai:
        specifier: ^5.0.0 || ^6.0.0 || ^7.0.0
        version: 7.10.5(@aws-sdk/client-sso-oidc@3.596.0)
      isomorphic-fetch:
        specifier: ^3.0.0
        version: 3.0.0
      ollama:
        specifier: ^0.5.0
        version: 0.5.12
      openai:
        specifier: ^3.0.0 || ^4.0.0
        version: 4.51.0
      voyageai:
        specifier: ^0.0.3-1
        version: 0.0.3-1
    devDependencies:
      "@jest/globals":
        specifier: ^29.7.0
        version: 29.7.0
      "@jest/types":
        specifier: ^29.6.3
        version: 29.6.3
      "@openapi-generator-plus/typescript-fetch-client-generator":
        specifier: ^1.11.0
        version: 1.11.0(openapi-types@12.1.3)
      "@types/bcrypt":
        specifier: ^5.0.2
        version: 5.0.2
      "@types/jest":
        specifier: ^29.5.0
        version: 29.5.12
      "@types/node":
        specifier: ^20.8.10
        version: 20.14.2
      bcrypt:
        specifier: ^5.1.1
        version: 5.1.1
      chalk:
        specifier: ^4.1.2
        version: 4.1.2
      jest:
        specifier: ^29.5.0
        version: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      npm-run-all:
        specifier: ^4.1.5
        version: 4.1.5
      openapi-generator-plus:
        specifier: ^2.20.0
        version: 2.20.0(@openapi-generator-plus/core@2.23.0(openapi-types@12.1.3))
      openapi-types:
        specifier: ^12.1.3
        version: 12.1.3
      prettier:
        specifier: 2.8.7
        version: 2.8.7
      rimraf:
        specifier: ^5.0.0
        version: 5.0.7
      testcontainers:
        specifier: ^10.9.0
        version: 10.10.3
      ts-jest:
        specifier: ^29.1.0
        version: 29.1.4(@babel/core@7.24.7)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.24.7))(esbuild@0.19.12)(jest@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)))(typescript@5.4.5)
      ts-node:
        specifier: ^10.9.1
        version: 10.9.2(@types/node@20.14.2)(typescript@5.4.5)
      tsd:
        specifier: ^0.28.1
        version: 0.28.1
      tsup:
        specifier: ^7.2.0
        version: 7.3.0(postcss@8.5.3)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))(typescript@5.4.5)
      typescript:
        specifier: ^5.0.4
        version: 5.4.5

  packages/chromadb:
    dependencies:
      "@google/generative-ai":
        specifier: ^0.1.1
        version: 0.1.3
      "@xenova/transformers":
        specifier: ^2.17.2
        version: 2.17.2
      chromadb-default-embed:
        specifier: ^2.14.0
        version: 2.14.0
      cliui:
        specifier: ^8.0.1
        version: 8.0.1
      cohere-ai:
        specifier: ^7.0.0
        version: 7.10.5(@aws-sdk/client-sso-oidc@3.596.0)
      isomorphic-fetch:
        specifier: ^3.0.0
        version: 3.0.0
      ollama:
        specifier: ^0.5.0
        version: 0.5.12
      openai:
        specifier: ^4.0.0
        version: 4.51.0
      semver:
        specifier: ^7.7.1
        version: 7.7.1
      voyageai:
        specifier: ^0.0.3-1
        version: 0.0.3-1
    devDependencies:
      "@internal/chromadb-core":
        specifier: workspace:*
        version: link:../chromadb-core
      "@types/semver":
        specifier: ^7.7.0
        version: 7.7.0
      jest:
        specifier: ^29.5.0
        version: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      rimraf:
        specifier: ^5.0.0
        version: 5.0.7
      ts-jest:
        specifier: ^29.1.0
        version: 29.1.4(@babel/core@7.24.7)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.24.7))(esbuild@0.19.12)(jest@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)))(typescript@5.4.5)
      tsup:
        specifier: ^7.2.0
        version: 7.3.0(postcss@8.5.3)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))(typescript@5.4.5)
      typescript:
        specifier: ^5.0.4
        version: 5.4.5

  packages/chromadb-client:
    dependencies:
      "@google/generative-ai":
        specifier: ^0.1.1
        version: 0.1.3
      "@xenova/transformers":
        specifier: ^2.17.2
        version: 2.17.2
      chromadb-default-embed:
        specifier: ^2.14.0
        version: 2.14.0
      cliui:
        specifier: ^8.0.1
        version: 8.0.1
      cohere-ai:
        specifier: ^5.0.0 || ^6.0.0 || ^7.0.0
        version: 7.10.5(@aws-sdk/client-sso-oidc@3.596.0)
      isomorphic-fetch:
        specifier: ^3.0.0
        version: 3.0.0
      ollama:
        specifier: ^0.5.0
        version: 0.5.12
      openai:
        specifier: ^3.0.0 || ^4.0.0
        version: 4.51.0
      voyageai:
        specifier: ^0.0.3-1
        version: 0.0.3-1
    devDependencies:
      "@internal/chromadb-core":
        specifier: workspace:*
        version: link:../chromadb-core
      jest:
        specifier: ^29.5.0
        version: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      rimraf:
        specifier: ^5.0.0
        version: 5.0.7
      ts-jest:
        specifier: ^29.1.0
        version: 29.1.4(@babel/core@7.24.7)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.24.7))(esbuild@0.19.12)(jest@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)))(typescript@5.4.5)
      tsup:
        specifier: ^7.2.0
        version: 7.3.0(postcss@8.5.3)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))(typescript@5.4.5)
      typescript:
        specifier: ^5.0.4
        version: 5.4.5

  packages/chromadb-core:
    dependencies:
      ajv:
        specifier: ^8.12.0
        version: 8.16.0
      cliui:
        specifier: ^8.0.1
        version: 8.0.1
      isomorphic-fetch:
        specifier: ^3.0.0
        version: 3.0.0
    devDependencies:
      "@jest/globals":
        specifier: ^29.7.0
        version: 29.7.0
      "@jest/types":
        specifier: ^29.6.3
        version: 29.6.3
      "@types/jest":
        specifier: ^29.5.0
        version: 29.5.12
      "@types/node":
        specifier: ^20.8.10
        version: 20.14.2
      bcrypt:
        specifier: ^5.1.1
        version: 5.1.1
      jest:
        specifier: ^29.5.0
        version: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      prettier:
        specifier: 2.8.7
        version: 2.8.7
      rimraf:
        specifier: ^5.0.0
        version: 5.0.7
      ts-jest:
        specifier: ^29.1.0
        version: 29.1.4(@babel/core@7.24.7)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.24.7))(esbuild@0.19.12)(jest@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)))(typescript@5.4.5)
      ts-node:
        specifier: ^10.9.1
        version: 10.9.2(@types/node@20.14.2)(typescript@5.4.5)
      tsup:
        specifier: ^7.2.0
        version: 7.3.0(postcss@8.5.3)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))(typescript@5.4.5)
      typescript:
        specifier: ^5.0.4
        version: 5.4.5

packages:
  "@ampproject/remapping@2.3.0":
    resolution:
      {
        integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==,
      }
    engines: { node: ">=6.0.0" }

  "@apidevtools/openapi-schemas@2.1.0":
    resolution:
      {
        integrity: sha512-Zc1AlqrJlX3SlpupFGpiLi2EbteyP7fXmUOGup6/DnkRgjP9bgMM/ag+n91rsv0U1Gpz0H3VILA/o3bW7Ua6BQ==,
      }
    engines: { node: ">=10" }

  "@apidevtools/swagger-methods@3.0.2":
    resolution:
      {
        integrity: sha512-QAkD5kK2b1WfjDS/UQn/qQkbwF31uqRjPTrsCs5ZG9BQGAkjwvqGFjjPqAuzac/IYzpPtRzjCP1WrTuAIjMrXg==,
      }

  "@aws-crypto/crc32@3.0.0":
    resolution:
      {
        integrity: sha512-IzSgsrxUcsrejQbPVilIKy16kAT52EwB6zSaI+M3xxIhKh5+aldEyvI+z6erM7TCLB2BJsFrtHjp6/4/sr+3dA==,
      }

  "@aws-crypto/ie11-detection@3.0.0":
    resolution:
      {
        integrity: sha512-341lBBkiY1DfDNKai/wXM3aujNBkXR7tq1URPQDL9wi3AUbI80NR74uF1TXHMm7po1AcnFk8iu2S2IeU/+/A+Q==,
      }

  "@aws-crypto/sha256-browser@3.0.0":
    resolution:
      {
        integrity: sha512-8VLmW2B+gjFbU5uMeqtQM6Nj0/F1bro80xQXCW6CQBWgosFWXTx77aeOF5CAIAmbOK64SdMBJdNr6J41yP5mvQ==,
      }

  "@aws-crypto/sha256-js@3.0.0":
    resolution:
      {
        integrity: sha512-PnNN7os0+yd1XvXAy23CFOmTbMaDxgxXtTKHybrJ39Y8kGzBATgBFibWJKH6BhytLI/Zyszs87xCOBNyBig6vQ==,
      }

  "@aws-crypto/supports-web-crypto@3.0.0":
    resolution:
      {
        integrity: sha512-06hBdMwUAb2WFTuGG73LSC0wfPu93xWwo5vL2et9eymgmu3Id5vFAHBbajVWiGhPO37qcsdCap/FqXvJGJWPIg==,
      }

  "@aws-crypto/util@3.0.0":
    resolution:
      {
        integrity: sha512-2OJlpeJpCR48CC8r+uKVChzs9Iungj9wkZrl8Z041DWEWvyIHILYKCPNzJghKsivj+S3mLo6BVc7mBNzdxA46w==,
      }

  "@aws-sdk/client-cognito-identity@3.596.0":
    resolution:
      {
        integrity: sha512-EnMebSL120H1V3CvxlSDu7xVg/c/U19J2pw5t3TbgWdP0bWR4gmaf2m7wczyi5XtPel0NIklnpPhlDJqr6T4Eg==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/client-sagemaker@3.596.0":
    resolution:
      {
        integrity: sha512-VDEo+pVw4fl9X18mEz0uj2rULke8QQ2QvpFWJ5oXGUqYCQRBqYOHaYtUvC+4kvj4vLmrmhTxYA6mO91tGbNadg==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/client-sso-oidc@3.596.0":
    resolution:
      {
        integrity: sha512-KnTWtKzO0N+rMdIrVwbewFp4FAvVWBV/ekCAh5w7EN+uAvBHxMoFElE2RwlcRF/gH1/F715OspPMvOxPom6bMA==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/client-sso@3.592.0":
    resolution:
      {
        integrity: sha512-w+SuW47jQqvOC7fonyjFjsOh3yjqJ+VpWdVrmrl0E/KryBE7ho/Wn991Buf/EiHHeJikoWgHsAIPkBH29+ntdA==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/client-sts@3.596.0":
    resolution:
      {
        integrity: sha512-37+WQDjgmqS/YXj3vPzIVIrbXaFcZ1WXk715AMGIPBZn9Y2/wr2bmSTpX7bsMyn0G8+LxmoIxFcG7n1Gu0nvLg==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/core@3.592.0":
    resolution:
      {
        integrity: sha512-gLPMXR/HXDP+9gXAt58t7gaMTvRts9i6Q7NMISpkGF54wehskl5WGrbdtHJFylrlJ5BQo3XVY6i661o+EuR1wg==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/credential-provider-cognito-identity@3.596.0":
    resolution:
      {
        integrity: sha512-ps/1P+wwEbzOryIdnPGkfo83AH5+kFPe0UKTc6Lhsc4l4zhfvyU3WV/JzrCINEKqo3bEZdUt6tl/IpsyC+nggQ==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/credential-provider-env@3.587.0":
    resolution:
      {
        integrity: sha512-Hyg/5KFECIk2k5o8wnVEiniV86yVkhn5kzITUydmNGCkXdBFHMHRx6hleQ1bqwJHbBskyu8nbYamzcwymmGwmw==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/credential-provider-http@3.596.0":
    resolution:
      {
        integrity: sha512-nnmvEsz1KJgRmfSZJPWuzbxPRXu8Y+/78Ifa1jY3fQKSKdEJfXMDsjPljJvMDBl4dZ8pf5Hwx+S/ONnMEDwYEA==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/credential-provider-ini@3.596.0":
    resolution:
      {
        integrity: sha512-c7PLtd7GbnOVAc5sk3sVlHxLvEsM8RF96rsBGlRo4AVpil/lXLKyNv9VarS4w/ZZZoRbJRyZ+m92PjNcLvpTDQ==,
      }
    engines: { node: ">=16.0.0" }
    peerDependencies:
      "@aws-sdk/client-sts": ^3.596.0

  "@aws-sdk/credential-provider-node@3.596.0":
    resolution:
      {
        integrity: sha512-F4MLyXpQyie1AnJS9n7TIRL0aF7YH8tKMIJXDsM5OXpSZi2en+yR6SzsxvHf5dwS2Ga8LUdEJyiyS2NoebaJGA==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/credential-provider-process@3.587.0":
    resolution:
      {
        integrity: sha512-V4xT3iCqkF8uL6QC4gqBJg/2asd/damswP1h9HCfqTllmPWzImS+8WD3VjgTLw5b0KbTy+ZdUhKc0wDnyzkzxg==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/credential-provider-sso@3.592.0":
    resolution:
      {
        integrity: sha512-fYFzAdDHKHvhtufPPtrLdSv8lO6GuW3em6n3erM5uFdpGytNpjXvr3XGokIsuXcNkETAY/Xihg+G9ksNE8WJxQ==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/credential-provider-web-identity@3.587.0":
    resolution:
      {
        integrity: sha512-XqIx/I2PG7kyuw3WjAP9wKlxy8IvFJwB8asOFT1xPFoVfZYKIogjG9oLP5YiRtfvDkWIztHmg5MlVv3HdJDGRw==,
      }
    engines: { node: ">=16.0.0" }
    peerDependencies:
      "@aws-sdk/client-sts": ^3.587.0

  "@aws-sdk/credential-providers@3.596.0":
    resolution:
      {
        integrity: sha512-EsbkylyO08P3alxXTpanKT1rPTh5/vXu7r/GoKbPl+7Laqheme41CYg0jtwAou/w7/6RFxqMn5ey5vg/qopNVA==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/middleware-host-header@3.577.0":
    resolution:
      {
        integrity: sha512-9ca5MJz455CODIVXs0/sWmJm7t3QO4EUa1zf8pE8grLpzf0J94bz/skDWm37Pli13T3WaAQBHCTiH2gUVfCsWg==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/middleware-logger@3.577.0":
    resolution:
      {
        integrity: sha512-aPFGpGjTZcJYk+24bg7jT4XdIp42mFXSuPt49lw5KygefLyJM/sB0bKKqPYYivW0rcuZ9brQ58eZUNthrzYAvg==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/middleware-recursion-detection@3.577.0":
    resolution:
      {
        integrity: sha512-pn3ZVEd2iobKJlR3H+bDilHjgRnNrQ6HMmK9ZzZw89Ckn3Dcbv48xOv4RJvu0aU8SDLl/SNCxppKjeLDTPGBNA==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/middleware-user-agent@3.587.0":
    resolution:
      {
        integrity: sha512-SyDomN+IOrygLucziG7/nOHkjUXES5oH5T7p8AboO8oakMQJdnudNXiYWTicQWO52R51U6CR27rcMPTGeMedYA==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/protocol-http@3.374.0":
    resolution:
      {
        integrity: sha512-9WpRUbINdGroV3HiZZIBoJvL2ndoWk39OfwxWs2otxByppJZNN14bg/lvCx5e8ggHUti7IBk5rb0nqQZ4m05pg==,
      }
    engines: { node: ">=14.0.0" }
    deprecated: This package has moved to @smithy/protocol-http

  "@aws-sdk/region-config-resolver@3.587.0":
    resolution:
      {
        integrity: sha512-93I7IPZtulZQoRK+O20IJ4a1syWwYPzoO2gc3v+/GNZflZPV3QJXuVbIm0pxBsu0n/mzKGUKqSOLPIaN098HcQ==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/signature-v4@3.374.0":
    resolution:
      {
        integrity: sha512-2xLJvSdzcZZAg0lsDLUAuSQuihzK0dcxIK7WmfuJeF7DGKJFmp9czQmz5f3qiDz6IDQzvgK1M9vtJSVCslJbyQ==,
      }
    engines: { node: ">=14.0.0" }
    deprecated: This package has moved to @smithy/signature-v4

  "@aws-sdk/token-providers@3.587.0":
    resolution:
      {
        integrity: sha512-ULqhbnLy1hmJNRcukANBWJmum3BbjXnurLPSFXoGdV0llXYlG55SzIla2VYqdveQEEjmsBuTZdFvXAtNpmS5Zg==,
      }
    engines: { node: ">=16.0.0" }
    peerDependencies:
      "@aws-sdk/client-sso-oidc": ^3.587.0

  "@aws-sdk/types@3.577.0":
    resolution:
      {
        integrity: sha512-FT2JZES3wBKN/alfmhlo+3ZOq/XJ0C7QOZcDNrpKjB0kqYoKjhVKZ/Hx6ArR0czkKfHzBBEs6y40ebIHx2nSmA==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/util-endpoints@3.587.0":
    resolution:
      {
        integrity: sha512-8I1HG6Em8wQWqKcRW6m358mqebRVNpL8XrrEoT4In7xqkKkmYtHRNVYP6lcmiQh5pZ/c/FXu8dSchuFIWyEtqQ==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/util-locate-window@3.568.0":
    resolution:
      {
        integrity: sha512-3nh4TINkXYr+H41QaPelCceEB2FXP3fxp93YZXB/kqJvX0U9j0N0Uk45gvsjmEPzG8XxkPEeLIfT2I1M7A6Lig==,
      }
    engines: { node: ">=16.0.0" }

  "@aws-sdk/util-user-agent-browser@3.577.0":
    resolution:
      {
        integrity: sha512-zEAzHgR6HWpZOH7xFgeJLc6/CzMcx4nxeQolZxVZoB5pPaJd3CjyRhZN0xXeZB0XIRCWmb4yJBgyiugXLNMkLA==,
      }

  "@aws-sdk/util-user-agent-node@3.587.0":
    resolution:
      {
        integrity: sha512-Pnl+DUe/bvnbEEDHP3iVJrOtE3HbFJBPgsD6vJ+ml/+IYk1Eq49jEG+EHZdNTPz3SDG0kbp2+7u41MKYJHR/iQ==,
      }
    engines: { node: ">=16.0.0" }
    peerDependencies:
      aws-crt: ">=1.0.0"
    peerDependenciesMeta:
      aws-crt:
        optional: true

  "@aws-sdk/util-utf8-browser@3.259.0":
    resolution:
      {
        integrity: sha512-UvFa/vR+e19XookZF8RzFZBrw2EUkQWxiBW0yYQAhvk3C+QVGl0H3ouca8LDBlBfQKXwmW3huo/59H8rwb1wJw==,
      }

  "@babel/code-frame@7.24.7":
    resolution:
      {
        integrity: sha512-BcYH1CVJBO9tvyIZ2jVeXgSIMvGZ2FDRvDdOIVQyuklNKSsx+eppDEBq/g47Ayw+RqNFE+URvOShmf+f/qwAlA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/compat-data@7.24.7":
    resolution:
      {
        integrity: sha512-qJzAIcv03PyaWqxRgO4mSU3lihncDT296vnyuE2O8uA4w3UHWI4S3hgeZd1L8W1Bft40w9JxJ2b412iDUFFRhw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/core@7.24.7":
    resolution:
      {
        integrity: sha512-nykK+LEK86ahTkX/3TgauT0ikKoNCfKHEaZYTUVupJdTLzGNvrblu4u6fa7DhZONAltdf8e662t/abY8idrd/g==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/generator@7.24.7":
    resolution:
      {
        integrity: sha512-oipXieGC3i45Y1A41t4tAqpnEZWgB/lC6Ehh6+rOviR5XWpTtMmLN+fGjz9vOiNRt0p6RtO6DtD0pdU3vpqdSA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-compilation-targets@7.24.7":
    resolution:
      {
        integrity: sha512-ctSdRHBi20qWOfy27RUb4Fhp07KSJ3sXcuSvTrXrc4aG8NSYDo1ici3Vhg9bg69y5bj0Mr1lh0aeEgTvc12rMg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-environment-visitor@7.24.7":
    resolution:
      {
        integrity: sha512-DoiN84+4Gnd0ncbBOM9AZENV4a5ZiL39HYMyZJGZ/AZEykHYdJw0wW3kdcsh9/Kn+BRXHLkkklZ51ecPKmI1CQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-function-name@7.24.7":
    resolution:
      {
        integrity: sha512-FyoJTsj/PEUWu1/TYRiXTIHc8lbw+TDYkZuoE43opPS5TrI7MyONBE1oNvfguEXAD9yhQRrVBnXdXzSLQl9XnA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-hoist-variables@7.24.7":
    resolution:
      {
        integrity: sha512-MJJwhkoGy5c4ehfoRyrJ/owKeMl19U54h27YYftT0o2teQ3FJ3nQUf/I3LlJsX4l3qlw7WRXUmiyajvHXoTubQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-module-imports@7.24.7":
    resolution:
      {
        integrity: sha512-8AyH3C+74cgCVVXow/myrynrAGv+nTVg5vKu2nZph9x7RcRwzmh0VFallJuFTZ9mx6u4eSdXZfcOzSqTUm0HCA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-module-transforms@7.24.7":
    resolution:
      {
        integrity: sha512-1fuJEwIrp+97rM4RWdO+qrRsZlAeL1lQJoPqtCYWv0NL115XM93hIH4CSRln2w52SqvmY5hqdtauB6QFCDiZNQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/helper-plugin-utils@7.24.7":
    resolution:
      {
        integrity: sha512-Rq76wjt7yz9AAc1KnlRKNAi/dMSVWgDRx43FHoJEbcYU6xOWaE2dVPwcdTukJrjxS65GITyfbvEYHvkirZ6uEg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-simple-access@7.24.7":
    resolution:
      {
        integrity: sha512-zBAIvbCMh5Ts+b86r/CjU+4XGYIs+R1j951gxI3KmmxBMhCg4oQMsv6ZXQ64XOm/cvzfU1FmoCyt6+owc5QMYg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-split-export-declaration@7.24.7":
    resolution:
      {
        integrity: sha512-oy5V7pD+UvfkEATUKvIjvIAH/xCzfsFVw7ygW2SI6NClZzquT+mwdTfgfdbUiceh6iQO0CHtCPsyze/MZ2YbAA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-string-parser@7.24.7":
    resolution:
      {
        integrity: sha512-7MbVt6xrwFQbunH2DNQsAP5sTGxfqQtErvBIvIMi6EQnbgUOuVYanvREcmFrOPhoXBrTtjhhP+lW+o5UfK+tDg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-identifier@7.24.7":
    resolution:
      {
        integrity: sha512-rR+PBcQ1SMQDDyF6X0wxtG8QyLCgUB0eRAGguqRLfkCA87l7yAP7ehq8SNj96OOGTO8OBV70KhuFYcIkHXOg0w==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-option@7.24.7":
    resolution:
      {
        integrity: sha512-yy1/KvjhV/ZCL+SM7hBrvnZJ3ZuT9OuZgIJAGpPEToANvc3iM6iDvBnRjtElWibHU6n8/LPR/EjX9EtIEYO3pw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helpers@7.24.7":
    resolution:
      {
        integrity: sha512-NlmJJtvcw72yRJRcnCmGvSi+3jDEg8qFu3z0AFoymmzLx5ERVWyzd9kVXr7Th9/8yIJi2Zc6av4Tqz3wFs8QWg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/highlight@7.24.7":
    resolution:
      {
        integrity: sha512-EStJpq4OuY8xYfhGVXngigBJRWxftKX9ksiGDnmlY3o7B/V7KIAc9X4oiK87uPJSc/vs5L869bem5fhZa8caZw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/parser@7.24.7":
    resolution:
      {
        integrity: sha512-9uUYRm6OqQrCqQdG1iCBwBPZgN8ciDBro2nIOFaiRz1/BCxaI7CNvQbDHvsArAC7Tw9Hda/B3U+6ui9u4HWXPw==,
      }
    engines: { node: ">=6.0.0" }
    hasBin: true

  "@babel/plugin-syntax-async-generators@7.8.4":
    resolution:
      {
        integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-bigint@7.8.3":
    resolution:
      {
        integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-class-properties@7.12.13":
    resolution:
      {
        integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-import-meta@7.10.4":
    resolution:
      {
        integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-json-strings@7.8.3":
    resolution:
      {
        integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-jsx@7.24.7":
    resolution:
      {
        integrity: sha512-6ddciUPe/mpMnOKv/U+RSd2vvVy+Yw/JfBB0ZHYjEZt9NLHmCUylNYlsbqCCS1Bffjlb0fCwC9Vqz+sBz6PsiQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-logical-assignment-operators@7.10.4":
    resolution:
      {
        integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3":
    resolution:
      {
        integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-numeric-separator@7.10.4":
    resolution:
      {
        integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-object-rest-spread@7.8.3":
    resolution:
      {
        integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-optional-catch-binding@7.8.3":
    resolution:
      {
        integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-optional-chaining@7.8.3":
    resolution:
      {
        integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-top-level-await@7.14.5":
    resolution:
      {
        integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-typescript@7.24.7":
    resolution:
      {
        integrity: sha512-c/+fVeJBB0FeKsFvwytYiUD+LBvhHjGSI0g446PRGdSVGZLRNArBUno2PETbAly3tpiNAQR5XaZ+JslxkotsbA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/template@7.24.7":
    resolution:
      {
        integrity: sha512-jYqfPrU9JTF0PmPy1tLYHW4Mp4KlgxJD9l2nP9fD6yT/ICi554DmrWBAEYpIelzjHf1msDP3PxJIRt/nFNfBig==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/traverse@7.24.7":
    resolution:
      {
        integrity: sha512-yb65Ed5S/QAcewNPh0nZczy9JdYXkkAbIsEo+P7BE7yO3txAY30Y/oPa3QkQ5It3xVG2kpKMg9MsdxZaO31uKA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/types@7.24.7":
    resolution:
      {
        integrity: sha512-XEFXSlxiG5td2EJRe8vOmRbaXVgfcBlszKujvVmWIK/UpywWljQCfzAv3RQCGujWQ1RD4YYWEAqDXfuJiy8f5Q==,
      }
    engines: { node: ">=6.9.0" }

  "@balena/dockerignore@1.0.2":
    resolution:
      {
        integrity: sha512-wMue2Sy4GAVTk6Ic4tJVcnfdau+gx2EnG7S+uAEe+TWJFqE4YoWN4/H8MSLj4eYJKxGg26lZwboEniNiNwZQ6Q==,
      }

  "@bcoe/v8-coverage@0.2.3":
    resolution:
      {
        integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==,
      }

  "@cspotcode/source-map-support@0.8.1":
    resolution:
      {
        integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==,
      }
    engines: { node: ">=12" }

  "@esbuild/aix-ppc64@0.19.12":
    resolution:
      {
        integrity: sha512-bmoCYyWdEL3wDQIVbcyzRyeKLgk2WtWLTWz1ZIAZF/EGbNOwSA6ew3PftJ1PqMiOOGu0OyFMzG53L0zqIpPeNA==,
      }
    engines: { node: ">=12" }
    cpu: [ppc64]
    os: [aix]

  "@esbuild/android-arm64@0.19.12":
    resolution:
      {
        integrity: sha512-P0UVNGIienjZv3f5zq0DP3Nt2IE/3plFzuaS96vihvD0Hd6H/q4WXUGpCxD/E8YrSXfNyRPbpTq+T8ZQioSuPA==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [android]

  "@esbuild/android-arm@0.19.12":
    resolution:
      {
        integrity: sha512-qg/Lj1mu3CdQlDEEiWrlC4eaPZ1KztwGJ9B6J+/6G+/4ewxJg7gqj8eVYWvao1bXrqGiW2rsBZFSX3q2lcW05w==,
      }
    engines: { node: ">=12" }
    cpu: [arm]
    os: [android]

  "@esbuild/android-x64@0.19.12":
    resolution:
      {
        integrity: sha512-3k7ZoUW6Q6YqhdhIaq/WZ7HwBpnFBlW905Fa4s4qWJyiNOgT1dOqDiVAQFwBH7gBRZr17gLrlFCRzF6jFh7Kew==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [android]

  "@esbuild/darwin-arm64@0.19.12":
    resolution:
      {
        integrity: sha512-B6IeSgZgtEzGC42jsI+YYu9Z3HKRxp8ZT3cqhvliEHovq8HSX2YX8lNocDn79gCKJXOSaEot9MVYky7AKjCs8g==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [darwin]

  "@esbuild/darwin-x64@0.19.12":
    resolution:
      {
        integrity: sha512-hKoVkKzFiToTgn+41qGhsUJXFlIjxI/jSYeZf3ugemDYZldIXIxhvwN6erJGlX4t5h417iFuheZ7l+YVn05N3A==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [darwin]

  "@esbuild/freebsd-arm64@0.19.12":
    resolution:
      {
        integrity: sha512-4aRvFIXmwAcDBw9AueDQ2YnGmz5L6obe5kmPT8Vd+/+x/JMVKCgdcRwH6APrbpNXsPz+K653Qg8HB/oXvXVukA==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [freebsd]

  "@esbuild/freebsd-x64@0.19.12":
    resolution:
      {
        integrity: sha512-EYoXZ4d8xtBoVN7CEwWY2IN4ho76xjYXqSXMNccFSx2lgqOG/1TBPW0yPx1bJZk94qu3tX0fycJeeQsKovA8gg==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [freebsd]

  "@esbuild/linux-arm64@0.19.12":
    resolution:
      {
        integrity: sha512-EoTjyYyLuVPfdPLsGVVVC8a0p1BFFvtpQDB/YLEhaXyf/5bczaGeN15QkR+O4S5LeJ92Tqotve7i1jn35qwvdA==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [linux]

  "@esbuild/linux-arm@0.19.12":
    resolution:
      {
        integrity: sha512-J5jPms//KhSNv+LO1S1TX1UWp1ucM6N6XuL6ITdKWElCu8wXP72l9MM0zDTzzeikVyqFE6U8YAV9/tFyj0ti+w==,
      }
    engines: { node: ">=12" }
    cpu: [arm]
    os: [linux]

  "@esbuild/linux-ia32@0.19.12":
    resolution:
      {
        integrity: sha512-Thsa42rrP1+UIGaWz47uydHSBOgTUnwBwNq59khgIwktK6x60Hivfbux9iNR0eHCHzOLjLMLfUMLCypBkZXMHA==,
      }
    engines: { node: ">=12" }
    cpu: [ia32]
    os: [linux]

  "@esbuild/linux-loong64@0.19.12":
    resolution:
      {
        integrity: sha512-LiXdXA0s3IqRRjm6rV6XaWATScKAXjI4R4LoDlvO7+yQqFdlr1Bax62sRwkVvRIrwXxvtYEHHI4dm50jAXkuAA==,
      }
    engines: { node: ">=12" }
    cpu: [loong64]
    os: [linux]

  "@esbuild/linux-mips64el@0.19.12":
    resolution:
      {
        integrity: sha512-fEnAuj5VGTanfJ07ff0gOA6IPsvrVHLVb6Lyd1g2/ed67oU1eFzL0r9WL7ZzscD+/N6i3dWumGE1Un4f7Amf+w==,
      }
    engines: { node: ">=12" }
    cpu: [mips64el]
    os: [linux]

  "@esbuild/linux-ppc64@0.19.12":
    resolution:
      {
        integrity: sha512-nYJA2/QPimDQOh1rKWedNOe3Gfc8PabU7HT3iXWtNUbRzXS9+vgB0Fjaqr//XNbd82mCxHzik2qotuI89cfixg==,
      }
    engines: { node: ">=12" }
    cpu: [ppc64]
    os: [linux]

  "@esbuild/linux-riscv64@0.19.12":
    resolution:
      {
        integrity: sha512-2MueBrlPQCw5dVJJpQdUYgeqIzDQgw3QtiAHUC4RBz9FXPrskyyU3VI1hw7C0BSKB9OduwSJ79FTCqtGMWqJHg==,
      }
    engines: { node: ">=12" }
    cpu: [riscv64]
    os: [linux]

  "@esbuild/linux-s390x@0.19.12":
    resolution:
      {
        integrity: sha512-+Pil1Nv3Umes4m3AZKqA2anfhJiVmNCYkPchwFJNEJN5QxmTs1uzyy4TvmDrCRNT2ApwSari7ZIgrPeUx4UZDg==,
      }
    engines: { node: ">=12" }
    cpu: [s390x]
    os: [linux]

  "@esbuild/linux-x64@0.19.12":
    resolution:
      {
        integrity: sha512-B71g1QpxfwBvNrfyJdVDexenDIt1CiDN1TIXLbhOw0KhJzE78KIFGX6OJ9MrtC0oOqMWf+0xop4qEU8JrJTwCg==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [linux]

  "@esbuild/netbsd-x64@0.19.12":
    resolution:
      {
        integrity: sha512-3ltjQ7n1owJgFbuC61Oj++XhtzmymoCihNFgT84UAmJnxJfm4sYCiSLTXZtE00VWYpPMYc+ZQmB6xbSdVh0JWA==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [netbsd]

  "@esbuild/openbsd-x64@0.19.12":
    resolution:
      {
        integrity: sha512-RbrfTB9SWsr0kWmb9srfF+L933uMDdu9BIzdA7os2t0TXhCRjrQyCeOt6wVxr79CKD4c+p+YhCj31HBkYcXebw==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [openbsd]

  "@esbuild/sunos-x64@0.19.12":
    resolution:
      {
        integrity: sha512-HKjJwRrW8uWtCQnQOz9qcU3mUZhTUQvi56Q8DPTLLB+DawoiQdjsYq+j+D3s9I8VFtDr+F9CjgXKKC4ss89IeA==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [sunos]

  "@esbuild/win32-arm64@0.19.12":
    resolution:
      {
        integrity: sha512-URgtR1dJnmGvX864pn1B2YUYNzjmXkuJOIqG2HdU62MVS4EHpU2946OZoTMnRUHklGtJdJZ33QfzdjGACXhn1A==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [win32]

  "@esbuild/win32-ia32@0.19.12":
    resolution:
      {
        integrity: sha512-+ZOE6pUkMOJfmxmBZElNOx72NKpIa/HFOMGzu8fqzQJ5kgf6aTGrcJaFsNiVMH4JKpMipyK+7k0n2UXN7a8YKQ==,
      }
    engines: { node: ">=12" }
    cpu: [ia32]
    os: [win32]

  "@esbuild/win32-x64@0.19.12":
    resolution:
      {
        integrity: sha512-T1QyPSDCyMXaO3pzBkF96E8xMkiRYbUEZADd29SyPGabqxMViNoii+NcK7eWJAEoU6RZyEm5lVSIjTmcdoB9HA==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [win32]

  "@google/generative-ai@0.1.3":
    resolution:
      {
        integrity: sha512-Cm4uJX1sKarpm1mje/MiOIinM7zdUUrQp/5/qGPAgznbdd/B9zup5ehT6c1qGqycFcSopTA1J1HpqHS5kJR8hQ==,
      }
    engines: { node: ">=18.0.0" }

  "@huggingface/jinja@0.1.3":
    resolution:
      {
        integrity: sha512-9KsiorsdIK8+7VmlamAT7Uh90zxAhC/SeKaKc80v58JhtPYuwaJpmR/ST7XAUxrHAFqHTCoTH5aJnJDwSL6xIQ==,
      }
    engines: { node: ">=18" }

  "@huggingface/jinja@0.2.2":
    resolution:
      {
        integrity: sha512-/KPde26khDUIPkTGU82jdtTW9UAuvUTumCAbFs/7giR0SxsvZC4hru51PBvpijH6BVkHcROcvZM/lpy5h1jRRA==,
      }
    engines: { node: ">=18" }

  "@isaacs/cliui@8.0.2":
    resolution:
      {
        integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==,
      }
    engines: { node: ">=12" }

  "@istanbuljs/load-nyc-config@1.1.0":
    resolution:
      {
        integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==,
      }
    engines: { node: ">=8" }

  "@istanbuljs/schema@0.1.3":
    resolution:
      {
        integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==,
      }
    engines: { node: ">=8" }

  "@jest/console@29.7.0":
    resolution:
      {
        integrity: sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/core@29.7.0":
    resolution:
      {
        integrity: sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  "@jest/environment@29.7.0":
    resolution:
      {
        integrity: sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/expect-utils@29.7.0":
    resolution:
      {
        integrity: sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/expect@29.7.0":
    resolution:
      {
        integrity: sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/fake-timers@29.7.0":
    resolution:
      {
        integrity: sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/globals@29.7.0":
    resolution:
      {
        integrity: sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/reporters@29.7.0":
    resolution:
      {
        integrity: sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  "@jest/schemas@29.6.3":
    resolution:
      {
        integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/source-map@29.6.3":
    resolution:
      {
        integrity: sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/test-result@29.7.0":
    resolution:
      {
        integrity: sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/test-sequencer@29.7.0":
    resolution:
      {
        integrity: sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/transform@29.7.0":
    resolution:
      {
        integrity: sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jest/types@29.6.3":
    resolution:
      {
        integrity: sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  "@jridgewell/gen-mapping@0.3.5":
    resolution:
      {
        integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/resolve-uri@3.1.2":
    resolution:
      {
        integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/set-array@1.2.1":
    resolution:
      {
        integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/sourcemap-codec@1.4.15":
    resolution:
      {
        integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==,
      }

  "@jridgewell/trace-mapping@0.3.25":
    resolution:
      {
        integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==,
      }

  "@jridgewell/trace-mapping@0.3.9":
    resolution:
      {
        integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==,
      }

  "@jsdevtools/ono@7.1.3":
    resolution:
      {
        integrity: sha512-4JQNk+************************************+c456qOrbbM/5xcR8huNCCcbVt7+UmizG6GuUvPvKUYg==,
      }

  "@mapbox/node-pre-gyp@1.0.11":
    resolution:
      {
        integrity: sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==,
      }
    hasBin: true

  "@nodelib/fs.scandir@2.1.5":
    resolution:
      {
        integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==,
      }
    engines: { node: ">= 8" }

  "@nodelib/fs.stat@2.0.5":
    resolution:
      {
        integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==,
      }
    engines: { node: ">= 8" }

  "@nodelib/fs.walk@1.2.8":
    resolution:
      {
        integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==,
      }
    engines: { node: ">= 8" }

  "@openapi-generator-plus/core@2.23.0":
    resolution:
      {
        integrity: sha512-S8btMFVuuOxp4AU+lIi9lPc2DCDoH1ieEBU5FZyLCO20lLaxKuVLQN1ChyLQj6eB8dn6OmV7LObgpo/GerVTMg==,
      }

  "@openapi-generator-plus/generator-common@1.7.1":
    resolution:
      {
        integrity: sha512-A0o8Pe/FzWh3OUXi8IYXIxO61F146HrdlvYgwdgD/RiiRjsYYWChxKo1OIOLnqN1FnNWvlPXoG1nC4vz8cZZ8g==,
      }

  "@openapi-generator-plus/handlebars-templates@1.11.1":
    resolution:
      {
        integrity: sha512-N1yGwqGqxuPZd9w/4rXBQVDjVq37J/0VTWauwIqzWzApBUWbCXkaytXlqk5nV7Yu0HjmrscArBgK1/3l/Cv2KQ==,
      }

  "@openapi-generator-plus/indexed-type@1.0.0":
    resolution:
      {
        integrity: sha512-RGUrlulyLoH7+V6wDalDGD9bfwTyDgIMZnfPo5GmaQs3CGOZ2aSHYAsB78gVTz2KWTyc5Ov4doi2lPENeUarZQ==,
      }

  "@openapi-generator-plus/java-like-generator-helper@2.6.0":
    resolution:
      {
        integrity: sha512-tcvl8M5EAYm4ph3aPzI6axLaFD5072hA8E6i2AXwc+IDg6CTIKR4Dgs+Fxbt2iK3H0zjnEucI++YLo+fJd09Sg==,
      }

  "@openapi-generator-plus/json-schema-ref-parser@9.0.11":
    resolution:
      {
        integrity: sha512-SJbsXJgQozq86V2ImkLuthI9d7esDIPjG/MUw2BEVa3HLIi/lHMmAVpUvBGNIpK4+yvUGmZSpgLOLmW3R9XoTA==,
      }

  "@openapi-generator-plus/swagger-parser@10.1.0":
    resolution:
      {
        integrity: sha512-Nxa6cAcJR6f2qieIa/pXTg0B9LqwzwYj6/AHBS39jE/eizJrhHQm74kqzABPjrFhvp9EcZD9E8IBuRunFfQULg==,
      }
    peerDependencies:
      openapi-types: ">=7"

  "@openapi-generator-plus/types@2.19.0":
    resolution:
      {
        integrity: sha512-nw4CboqusBIn3ybXzYDhYGMxW58YybfWIx/56VNgM1brYsgcpdbwjqtsanm5HnjA8gR7NyQEjk1Nq5eJBQ85ww==,
      }

  "@openapi-generator-plus/typescript-fetch-client-generator@1.11.0":
    resolution:
      {
        integrity: sha512-EcRLd6O+YEhEkliLoOrew24vsCCekIlxpPwZ1uEpx+kxQ2tPSxA4eamw5avjtt370GD4Neq16Y50s0bjO6otvw==,
      }

  "@openapi-generator-plus/typescript-generator-common@1.12.0":
    resolution:
      {
        integrity: sha512-jdqU3wWi0KAc2nfPgbxoGumO7gzxQTv4xg1kOaXng22xM7R2D2FKinobe5O2nqBCqVphcml4BKiQvflC+8fVYQ==,
      }

  "@openapi-generator-plus/utils@1.1.4":
    resolution:
      {
        integrity: sha512-He0atAw3HIIGZPsNhP6N+pXSkYh/ebdh/FMAduXnuhTHz8V8qPXyI/EBIACCuPNJXuIaSNhbqR5HRn2iUG5/jQ==,
      }

  "@pkgjs/parseargs@0.11.0":
    resolution:
      {
        integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==,
      }
    engines: { node: ">=14" }

  "@protobufjs/aspromise@1.1.2":
    resolution:
      {
        integrity: sha512-j+gKExEuLmKwvz3OgROXtrJ2UG2x8Ch2YZUxahh+s1F2HZ+wAceUNLkvy6zKCPVRkU++ZWQrdxsUeQXmcg4uoQ==,
      }

  "@protobufjs/base64@1.1.2":
    resolution:
      {
        integrity: sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg==,
      }

  "@protobufjs/codegen@2.0.4":
    resolution:
      {
        integrity: sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg==,
      }

  "@protobufjs/eventemitter@1.1.0":
    resolution:
      {
        integrity: sha512-j9ednRT81vYJ9OfVuXG6ERSTdEL1xVsNgqpkxMsbIabzSo3goCjDIveeGv5d03om39ML71RdmrGNjG5SReBP/Q==,
      }

  "@protobufjs/fetch@1.1.0":
    resolution:
      {
        integrity: sha512-lljVXpqXebpsijW71PZaCYeIcE5on1w5DlQy5WH6GLbFryLUrBD4932W/E2BSpfRJWseIL4v/KPgBFxDOIdKpQ==,
      }

  "@protobufjs/float@1.0.2":
    resolution:
      {
        integrity: sha512-Ddb+kVXlXst9d+R9PfTIxh1EdNkgoRe5tOX6t01f1lYWOvJnSPDBlG241QLzcyPdoNTsblLUdujGSE4RzrTZGQ==,
      }

  "@protobufjs/inquire@1.1.0":
    resolution:
      {
        integrity: sha512-kdSefcPdruJiFMVSbn801t4vFK7KB/5gd2fYvrxhuJYg8ILrmn9SKSX2tZdV6V+ksulWqS7aXjBcRXl3wHoD9Q==,
      }

  "@protobufjs/path@1.1.2":
    resolution:
      {
        integrity: sha512-6JOcJ5Tm08dOHAbdR3GrvP+yUUfkjG5ePsHYczMFLq3ZmMkAD98cDgcT2iA1lJ9NVwFd4tH/iSSoe44YWkltEA==,
      }

  "@protobufjs/pool@1.1.0":
    resolution:
      {
        integrity: sha512-0kELaGSIDBKvcgS4zkjz1PeddatrjYcmMWOlAuAPwAeccUrPHdUqo/J6LiymHHEiJT5NrF1UVwxY14f+fy4WQw==,
      }

  "@protobufjs/utf8@1.1.0":
    resolution:
      {
        integrity: sha512-Vvn3zZrhQZkkBE8LSuW3em98c0FwgO4nxzv6OdSxPKJIEKY2bGbHn+mhGIPerzI4twdxaP8/0+06HBpwf345Lw==,
      }

  "@rollup/rollup-android-arm-eabi@4.18.0":
    resolution:
      {
        integrity: sha512-Tya6xypR10giZV1XzxmH5wr25VcZSncG0pZIjfePT0OVBvqNEurzValetGNarVrGiq66EBVAFn15iYX4w6FKgQ==,
      }
    cpu: [arm]
    os: [android]

  "@rollup/rollup-android-arm64@4.18.0":
    resolution:
      {
        integrity: sha512-avCea0RAP03lTsDhEyfy+hpfr85KfyTctMADqHVhLAF3MlIkq83CP8UfAHUssgXTYd+6er6PaAhx/QGv4L1EiA==,
      }
    cpu: [arm64]
    os: [android]

  "@rollup/rollup-darwin-arm64@4.18.0":
    resolution:
      {
        integrity: sha512-IWfdwU7KDSm07Ty0PuA/W2JYoZ4iTj3TUQjkVsO/6U+4I1jN5lcR71ZEvRh52sDOERdnNhhHU57UITXz5jC1/w==,
      }
    cpu: [arm64]
    os: [darwin]

  "@rollup/rollup-darwin-x64@4.18.0":
    resolution:
      {
        integrity: sha512-n2LMsUz7Ynu7DoQrSQkBf8iNrjOGyPLrdSg802vk6XT3FtsgX6JbE8IHRvposskFm9SNxzkLYGSq9QdpLYpRNA==,
      }
    cpu: [x64]
    os: [darwin]

  "@rollup/rollup-linux-arm-gnueabihf@4.18.0":
    resolution:
      {
        integrity: sha512-C/zbRYRXFjWvz9Z4haRxcTdnkPt1BtCkz+7RtBSuNmKzMzp3ZxdM28Mpccn6pt28/UWUCTXa+b0Mx1k3g6NOMA==,
      }
    cpu: [arm]
    os: [linux]

  "@rollup/rollup-linux-arm-musleabihf@4.18.0":
    resolution:
      {
        integrity: sha512-l3m9ewPgjQSXrUMHg93vt0hYCGnrMOcUpTz6FLtbwljo2HluS4zTXFy2571YQbisTnfTKPZ01u/ukJdQTLGh9A==,
      }
    cpu: [arm]
    os: [linux]

  "@rollup/rollup-linux-arm64-gnu@4.18.0":
    resolution:
      {
        integrity: sha512-rJ5D47d8WD7J+7STKdCUAgmQk49xuFrRi9pZkWoRD1UeSMakbcepWXPF8ycChBoAqs1pb2wzvbY6Q33WmN2ftw==,
      }
    cpu: [arm64]
    os: [linux]

  "@rollup/rollup-linux-arm64-musl@4.18.0":
    resolution:
      {
        integrity: sha512-be6Yx37b24ZwxQ+wOQXXLZqpq4jTckJhtGlWGZs68TgdKXJgw54lUUoFYrg6Zs/kjzAQwEwYbp8JxZVzZLRepQ==,
      }
    cpu: [arm64]
    os: [linux]

  "@rollup/rollup-linux-powerpc64le-gnu@4.18.0":
    resolution:
      {
        integrity: sha512-hNVMQK+qrA9Todu9+wqrXOHxFiD5YmdEi3paj6vP02Kx1hjd2LLYR2eaN7DsEshg09+9uzWi2W18MJDlG0cxJA==,
      }
    cpu: [ppc64]
    os: [linux]

  "@rollup/rollup-linux-riscv64-gnu@4.18.0":
    resolution:
      {
        integrity: sha512-ROCM7i+m1NfdrsmvwSzoxp9HFtmKGHEqu5NNDiZWQtXLA8S5HBCkVvKAxJ8U+CVctHwV2Gb5VUaK7UAkzhDjlg==,
      }
    cpu: [riscv64]
    os: [linux]

  "@rollup/rollup-linux-s390x-gnu@4.18.0":
    resolution:
      {
        integrity: sha512-0UyyRHyDN42QL+NbqevXIIUnKA47A+45WyasO+y2bGJ1mhQrfrtXUpTxCOrfxCR4esV3/RLYyucGVPiUsO8xjg==,
      }
    cpu: [s390x]
    os: [linux]

  "@rollup/rollup-linux-x64-gnu@4.18.0":
    resolution:
      {
        integrity: sha512-xuglR2rBVHA5UsI8h8UbX4VJ470PtGCf5Vpswh7p2ukaqBGFTnsfzxUBetoWBWymHMxbIG0Cmx7Y9qDZzr648w==,
      }
    cpu: [x64]
    os: [linux]

  "@rollup/rollup-linux-x64-musl@4.18.0":
    resolution:
      {
        integrity: sha512-LKaqQL9osY/ir2geuLVvRRs+utWUNilzdE90TpyoX0eNqPzWjRm14oMEE+YLve4k/NAqCdPkGYDaDF5Sw+xBfg==,
      }
    cpu: [x64]
    os: [linux]

  "@rollup/rollup-win32-arm64-msvc@4.18.0":
    resolution:
      {
        integrity: sha512-7J6TkZQFGo9qBKH0pk2cEVSRhJbL6MtfWxth7Y5YmZs57Pi+4x6c2dStAUvaQkHQLnEQv1jzBUW43GvZW8OFqA==,
      }
    cpu: [arm64]
    os: [win32]

  "@rollup/rollup-win32-ia32-msvc@4.18.0":
    resolution:
      {
        integrity: sha512-Txjh+IxBPbkUB9+SXZMpv+b/vnTEtFyfWZgJ6iyCmt2tdx0OF5WhFowLmnh8ENGNpfUlUZkdI//4IEmhwPieNg==,
      }
    cpu: [ia32]
    os: [win32]

  "@rollup/rollup-win32-x64-msvc@4.18.0":
    resolution:
      {
        integrity: sha512-UOo5FdvOL0+eIVTgS4tIdbW+TtnBLWg1YBCcU2KWM7nuNwRz9bksDX1bekJJCpu25N1DVWaCwnT39dVQxzqS8g==,
      }
    cpu: [x64]
    os: [win32]

  "@sinclair/typebox@0.27.8":
    resolution:
      {
        integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==,
      }

  "@sinonjs/commons@3.0.1":
    resolution:
      {
        integrity: sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==,
      }

  "@sinonjs/fake-timers@10.3.0":
    resolution:
      {
        integrity: sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==,
      }

  "@smithy/abort-controller@3.0.1":
    resolution:
      {
        integrity: sha512-Jb7jg4E+C+uvrUQi+h9kbILY6ts6fglKZzseMCHlH9ayq+1f5QdpYf8MV/xppuiN6DAMJAmwGz53GwP3213dmA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/config-resolver@3.0.2":
    resolution:
      {
        integrity: sha512-wUyG6ezpp2sWAvfqmSYTROwFUmJqKV78GLf55WODrosBcT0BAMd9bOLO4HRhynWBgAobPml2cF9ZOdgCe00r+g==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/core@2.2.1":
    resolution:
      {
        integrity: sha512-R8Pzrr2v2oGUoj4CTZtKPr87lVtBsz7IUBGhSwS1kc6Cj0yPwNdYbkzhFsxhoDE9+BPl09VN/6rFsW9GJzWnBA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/credential-provider-imds@3.1.1":
    resolution:
      {
        integrity: sha512-htndP0LwHdE3R3Nam9ZyVWhwPYOmD4xCL79kqvNxy8u/bv0huuy574CSiRY4cvEICgimv8jlVfLeZ7zZqbnB2g==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/eventstream-codec@1.1.0":
    resolution:
      {
        integrity: sha512-3tEbUb8t8an226jKB6V/Q2XU/J53lCwCzULuBPEaF4JjSh+FlCMp7TmogE/Aij5J9DwlsZ4VAD/IRDuQ/0ZtMw==,
      }

  "@smithy/fetch-http-handler@3.0.2":
    resolution:
      {
        integrity: sha512-0nW6tLK0b7EqSsfKvnOmZCgJqnodBAnvqcrlC5dotKfklLedPTRGsQamSVbVDWyuU/QGg+YbZDJUQ0CUufJXZQ==,
      }

  "@smithy/hash-node@3.0.1":
    resolution:
      {
        integrity: sha512-w2ncjgk2EYO2+WhAsSQA8owzoOSY7IL1qVytlwpnL1pFGWTjIoIh5nROkEKXY51unB63bMGZqDiVoXaFbyKDlg==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/invalid-dependency@3.0.1":
    resolution:
      {
        integrity: sha512-RSNF/32BKygXKKMyS7koyuAq1rcdW5p5c4EFa77QenBFze9As+JiRnV9OWBh2cB/ejGZalEZjvIrMLHwJl7aGA==,
      }

  "@smithy/is-array-buffer@1.1.0":
    resolution:
      {
        integrity: sha512-twpQ/n+3OWZJ7Z+xu43MJErmhB/WO/mMTnqR6PwWQShvSJ/emx5d1N59LQZk6ZpTAeuRWrc+eHhkzTp9NFjNRQ==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/is-array-buffer@3.0.0":
    resolution:
      {
        integrity: sha512-+Fsu6Q6C4RSJiy81Y8eApjEB5gVtM+oFKTffg+jSuwtvomJJrhUJBu2zS8wjXSgH/g1MKEWrzyChTBe6clb5FQ==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/middleware-content-length@3.0.1":
    resolution:
      {
        integrity: sha512-6QdK/VbrCfXD5/QolE2W/ok6VqxD+SM28Ds8iSlEHXZwv4buLsvWyvoEEy0322K/g5uFgPzBmZjGqesTmPL+yQ==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/middleware-endpoint@3.0.2":
    resolution:
      {
        integrity: sha512-gWEaGYB3Bei17Oiy/F2IlUPpBazNXImytoOdJ1xbrUOaJKAOiUhx8/4FOnYLLJHdAwa9PlvJ2ULda2f/Dnwi9w==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/middleware-retry@3.0.4":
    resolution:
      {
        integrity: sha512-Tu+FggbLNF5G9L6Wi8o32Mg4bhlBInWlhhaFKyytGRnkfxGopxFVXJQn7sjZdFYJyTz6RZZa06tnlvavUgtoVg==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/middleware-serde@3.0.1":
    resolution:
      {
        integrity: sha512-ak6H/ZRN05r5+SR0/IUc5zOSyh2qp3HReg1KkrnaSLXmncy9lwOjNqybX4L4x55/e5mtVDn1uf/gQ6bw5neJPw==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/middleware-stack@3.0.1":
    resolution:
      {
        integrity: sha512-fS5uT//y1SlBdkzIvgmWQ9FufwMXrHSSbuR25ygMy1CRDIZkcBMoF4oTMYNfR9kBlVBcVzlv7joFdNrFuQirPA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/node-config-provider@3.1.1":
    resolution:
      {
        integrity: sha512-z5G7+ysL4yUtMghUd2zrLkecu0mTfnYlt5dR76g/HsFqf7evFazwiZP1ag2EJenGxNBDwDM5g8nm11NPogiUVA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/node-http-handler@3.0.1":
    resolution:
      {
        integrity: sha512-hlBI6MuREA4o1wBMEt+QNhUzoDtFFvwR6ecufimlx9D79jPybE/r8kNorphXOi91PgSO9S2fxRjcKCLk7Jw8zA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/property-provider@3.1.1":
    resolution:
      {
        integrity: sha512-YknOMZcQkB5on+MU0DvbToCmT2YPtTETMXW0D3+/Iln7ezT+Zm1GMHhCW1dOH/X/+LkkQD9aXEoCX/B10s4Xdw==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/protocol-http@1.2.0":
    resolution:
      {
        integrity: sha512-GfGfruksi3nXdFok5RhgtOnWe5f6BndzYfmEXISD+5gAGdayFGpjWu5pIqIweTudMtse20bGbc+7MFZXT1Tb8Q==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/protocol-http@4.0.1":
    resolution:
      {
        integrity: sha512-eBhm9zwcFPEazc654c0BEWtxYAzrw+OhoSf5pkwKzfftWKXRoqEhwOE2Pvn30v0iAdo7Mfsfb6pi1NnZlGCMpg==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/querystring-builder@3.0.1":
    resolution:
      {
        integrity: sha512-vKitpnG/2KOMVlx3x1S3FkBH075EROG3wcrcDaNerQNh8yuqnSL23btCD2UyX4i4lpPzNW6VFdxbn2Z25b/g5Q==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/querystring-parser@3.0.1":
    resolution:
      {
        integrity: sha512-Qt8DMC05lVS8NcQx94lfVbZSX+2Ym7032b/JR8AlboAa/D669kPzqb35dkjkvAG6+NWmUchef3ENtrD6F+5n8Q==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/service-error-classification@3.0.1":
    resolution:
      {
        integrity: sha512-ubFUvIePjDCyIzZ+pLETqNC6KXJ/fc6g+/baqel7Zf6kJI/kZKgjwkCI7zbUhoUuOZ/4eA/87YasVu40b/B4bA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/shared-ini-file-loader@3.1.1":
    resolution:
      {
        integrity: sha512-nD6tXIX2126/P9e3wqRY1bm9dTtPZwRDyjVOd18G28o+1UOG+kOVgUwujE795HslSuPlEgqzsH5sgNP1hDjj9g==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/signature-v4@1.1.0":
    resolution:
      {
        integrity: sha512-fDo3m7YqXBs7neciOePPd/X9LPm5QLlDMdIC4m1H6dgNLnXfLMFNIxEfPyohGA8VW9Wn4X8lygnPSGxDZSmp0Q==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/signature-v4@3.0.1":
    resolution:
      {
        integrity: sha512-ARAmD+E7j6TIEhKLjSZxdzs7wceINTMJRN2BXPM09BiUmJhkXAF1ZZtDXH6fhlk7oehBZeh37wGiPOqtdKjLeg==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/smithy-client@3.1.2":
    resolution:
      {
        integrity: sha512-f3eQpczBOFUtdT/ptw2WpUKu1qH1K7xrssrSiHYtd9TuLXkvFqb88l9mz9FHeUVNSUxSnkW1anJnw6rLwUKzQQ==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/types@1.2.0":
    resolution:
      {
        integrity: sha512-z1r00TvBqF3dh4aHhya7nz1HhvCg4TRmw51fjMrh5do3h+ngSstt/yKlNbHeb9QxJmFbmN8KEVSWgb1bRvfEoA==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/types@3.1.0":
    resolution:
      {
        integrity: sha512-qi4SeCVOUPjhSSZrxxB/mB8DrmuSFUcJnD9KXjuP+7C3LV/KFV4kpuUSH3OHDZgQB9TEH/1sO/Fq/5HyaK9MPw==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/url-parser@3.0.1":
    resolution:
      {
        integrity: sha512-G140IlNFlzYWVCedC4E2d6NycM1dCUbe5CnsGW1hmGt4hYKiGOw0v7lVru9WAn5T2w09QEjl4fOESWjGmCvVmg==,
      }

  "@smithy/util-base64@3.0.0":
    resolution:
      {
        integrity: sha512-Kxvoh5Qtt0CDsfajiZOCpJxgtPHXOKwmM+Zy4waD43UoEMA+qPxxa98aE/7ZhdnBFZFXMOiBR5xbcaMhLtznQQ==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-body-length-browser@3.0.0":
    resolution:
      {
        integrity: sha512-cbjJs2A1mLYmqmyVl80uoLTJhAcfzMOyPgjwAYusWKMdLeNtzmMz9YxNl3/jRLoxSS3wkqkf0jwNdtXWtyEBaQ==,
      }

  "@smithy/util-body-length-node@3.0.0":
    resolution:
      {
        integrity: sha512-Tj7pZ4bUloNUP6PzwhN7K386tmSmEET9QtQg0TgdNOnxhZvCssHji+oZTUIuzxECRfG8rdm2PMw2WCFs6eIYkA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-buffer-from@1.1.0":
    resolution:
      {
        integrity: sha512-9m6NXE0ww+ra5HKHCHig20T+FAwxBAm7DIdwc/767uGWbRcY720ybgPacQNB96JMOI7xVr/CDa3oMzKmW4a+kw==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/util-buffer-from@3.0.0":
    resolution:
      {
        integrity: sha512-aEOHCgq5RWFbP+UDPvPot26EJHjOC+bRgse5A8V3FSShqd5E5UN4qc7zkwsvJPPAVsf73QwYcHN1/gt/rtLwQA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-config-provider@3.0.0":
    resolution:
      {
        integrity: sha512-pbjk4s0fwq3Di/ANL+rCvJMKM5bzAQdE5S/6RL5NXgMExFAi6UgQMPOm5yPaIWPpr+EOXKXRonJ3FoxKf4mCJQ==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-defaults-mode-browser@3.0.4":
    resolution:
      {
        integrity: sha512-sXtin3Mue3A3xo4+XkozpgPptgmRwvNPOqTvb3ANGTCzzoQgAPBNjpE+aXCINaeSMXwHmv7E2oEn2vWdID+SAQ==,
      }
    engines: { node: ">= 10.0.0" }

  "@smithy/util-defaults-mode-node@3.0.4":
    resolution:
      {
        integrity: sha512-CUF6TyxLh3CgBRVYgZNOPDfzHQjeQr0vyALR6/DkQkOm7rNfGEzW1BRFi88C73pndmfvoiIT7ochuT76OPz9Dw==,
      }
    engines: { node: ">= 10.0.0" }

  "@smithy/util-endpoints@2.0.2":
    resolution:
      {
        integrity: sha512-4zFOcBFQvifd2LSD4a1dKvfIWWwh4sWNtS3oZ7mpob/qPPmJseqKB148iT+hWCDsG//TmI+8vjYPgZdvnkYlTg==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-hex-encoding@1.1.0":
    resolution:
      {
        integrity: sha512-7UtIE9eH0u41zpB60Jzr0oNCQ3hMJUabMcKRUVjmyHTXiWDE4vjSqN6qlih7rCNeKGbioS7f/y2Jgym4QZcKFg==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/util-hex-encoding@3.0.0":
    resolution:
      {
        integrity: sha512-eFndh1WEK5YMUYvy3lPlVmYY/fZcQE1D8oSf41Id2vCeIkKJXPcYDCZD+4+xViI6b1XSd7tE+s5AmXzz5ilabQ==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-middleware@1.1.0":
    resolution:
      {
        integrity: sha512-6hhckcBqVgjWAqLy2vqlPZ3rfxLDhFWEmM7oLh2POGvsi7j0tHkbN7w4DFhuBExVJAbJ/qqxqZdRY6Fu7/OezQ==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/util-middleware@3.0.1":
    resolution:
      {
        integrity: sha512-WRODCQtUsO7vIvfrdxS8RFPeLKcewYtaCglZsBsedIKSUGIIvMlZT5oh+pCe72I+1L+OjnZuqRNpN2LKhWA4KQ==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-retry@3.0.1":
    resolution:
      {
        integrity: sha512-5lRtYm+8fNFEUTdqZXg5M4ppVp40rMIJfR1TpbHAhKQgPIDpWT+iYMaqgnwEbtpi9U1smyUOPv5Sg+M1neOBgw==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-stream@3.0.2":
    resolution:
      {
        integrity: sha512-n5Obp5AnlI6qHo8sbupwrcpBe6vFp4qkl0SRNuExKPNrH3ABAMG2ZszRTIUIv2b4AsFrCO+qiy4uH1Q3z1dxTA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-uri-escape@1.1.0":
    resolution:
      {
        integrity: sha512-/jL/V1xdVRt5XppwiaEU8Etp5WHZj609n0xMTuehmCqdoOFbId1M+aEeDWZsQ+8JbEB/BJ6ynY2SlYmOaKtt8w==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/util-uri-escape@3.0.0":
    resolution:
      {
        integrity: sha512-LqR7qYLgZTD7nWLBecUi4aqolw8Mhza9ArpNEQ881MJJIU2sE5iHCK6TdyqqzcDLy0OPe10IY4T8ctVdtynubg==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-utf8@1.1.0":
    resolution:
      {
        integrity: sha512-p/MYV+JmqmPyjdgyN2UxAeYDj9cBqCjp0C/NsTWnnjoZUVqoeZ6IrW915L9CAKWVECgv9lVQGc4u/yz26/bI1A==,
      }
    engines: { node: ">=14.0.0" }

  "@smithy/util-utf8@3.0.0":
    resolution:
      {
        integrity: sha512-rUeT12bxFnplYDe815GXbq/oixEGHfRFFtcTF3YdDi/JaENIM6aSYYLJydG83UNzLXeRI5K8abYd/8Sp/QM0kA==,
      }
    engines: { node: ">=16.0.0" }

  "@smithy/util-waiter@3.0.1":
    resolution:
      {
        integrity: sha512-wwnrVQdjQxvWGOAiLmqlEhENGCcDIN+XJ/+usPOgSZObAslrCXgKlkX7rNVwIWW2RhPguTKthvF+4AoO0Z6KpA==,
      }
    engines: { node: ">=16.0.0" }

  "@tsconfig/node10@1.0.11":
    resolution:
      {
        integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==,
      }

  "@tsconfig/node12@1.0.11":
    resolution:
      {
        integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==,
      }

  "@tsconfig/node14@1.0.3":
    resolution:
      {
        integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==,
      }

  "@tsconfig/node16@1.0.4":
    resolution:
      {
        integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==,
      }

  "@tsd/typescript@5.0.4":
    resolution:
      {
        integrity: sha512-YQi2lvZSI+xidKeUjlbv6b6Zw7qB3aXHw5oGJLs5OOGAEqKIOvz5UIAkWyg0bJbkSUWPBEtaOHpVxU4EYBO1Jg==,
      }

  "@types/babel__core@7.20.5":
    resolution:
      {
        integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==,
      }

  "@types/babel__generator@7.6.8":
    resolution:
      {
        integrity: sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==,
      }

  "@types/babel__template@7.4.4":
    resolution:
      {
        integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==,
      }

  "@types/babel__traverse@7.20.6":
    resolution:
      {
        integrity: sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==,
      }

  "@types/bcrypt@5.0.2":
    resolution:
      {
        integrity: sha512-6atioO8Y75fNcbmj0G7UjI9lXN2pQ/IGJ2FWT4a/btd0Lk9lQalHLKhkgKVZ3r+spnmWUKfbMi1GEe9wyHQfNQ==,
      }

  "@types/docker-modem@3.0.6":
    resolution:
      {
        integrity: sha512-yKpAGEuKRSS8wwx0joknWxsmLha78wNMe9R2S3UNsVOkZded8UqOrV8KoeDXoXsjndxwyF3eIhyClGbO1SEhEg==,
      }

  "@types/dockerode@3.3.29":
    resolution:
      {
        integrity: sha512-5PRRq/yt5OT/Jf77ltIdz4EiR9+VLnPF+HpU4xGFwUqmV24Co2HKBNW3w+slqZ1CYchbcDeqJASHDYWzZCcMiQ==,
      }

  "@types/eslint@7.29.0":
    resolution:
      {
        integrity: sha512-VNcvioYDH8/FxaeTKkM4/TiTwt6pBV9E3OfGmvaw8tPl0rrHCJ4Ll15HRT+pMiFAf/MLQvAzC+6RzUMEL9Ceng==,
      }

  "@types/estree@1.0.5":
    resolution:
      {
        integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==,
      }

  "@types/graceful-fs@4.1.9":
    resolution:
      {
        integrity: sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==,
      }

  "@types/istanbul-lib-coverage@2.0.6":
    resolution:
      {
        integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==,
      }

  "@types/istanbul-lib-report@3.0.3":
    resolution:
      {
        integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==,
      }

  "@types/istanbul-reports@3.0.4":
    resolution:
      {
        integrity: sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==,
      }

  "@types/jest@29.5.12":
    resolution:
      {
        integrity: sha512-eDC8bTvT/QhYdxJAulQikueigY5AsdBRH2yDKW3yveW7svY3+DzN84/2NUgkw10RTiJbWqZrTtoGVdYlvFJdLw==,
      }

  "@types/json-schema@7.0.15":
    resolution:
      {
        integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==,
      }

  "@types/long@4.0.2":
    resolution:
      {
        integrity: sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA==,
      }

  "@types/minimist@1.2.5":
    resolution:
      {
        integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==,
      }

  "@types/node-fetch@2.6.11":
    resolution:
      {
        integrity: sha512-24xFj9R5+rfQJLRyM56qh+wnVSYhyXC2tkoBndtY0U+vubqNsYXGjufB2nn8Q6gt0LrARwL6UBtMCSVCwl4B1g==,
      }

  "@types/node@18.19.34":
    resolution:
      {
        integrity: sha512-eXF4pfBNV5DAMKGbI02NnDtWrQ40hAN558/2vvS4gMpMIxaf6JmD7YjnZbq0Q9TDSSkKBamime8ewRoomHdt4g==,
      }

  "@types/node@20.14.2":
    resolution:
      {
        integrity: sha512-xyu6WAMVwv6AKFLB+e/7ySZVr/0zLCzOa7rSpq6jNwpqOrUbcACDWC+53d4n2QHOnDou0fbIsg8wZu/sxrnI4Q==,
      }

  "@types/normalize-package-data@2.4.4":
    resolution:
      {
        integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==,
      }

  "@types/semver@7.7.0":
    resolution:
      {
        integrity: sha512-k107IF4+Xr7UHjwDc7Cfd6PRQfbdkiRabXGRjo07b4WyPahFBZCZ1sE+BNxYIJPPg73UkfOsVOLwqVc/6ETrIA==,
      }

  "@types/ssh2-streams@0.1.12":
    resolution:
      {
        integrity: sha512-Sy8tpEmCce4Tq0oSOYdfqaBpA3hDM8SoxoFh5vzFsu2oL+znzGz8oVWW7xb4K920yYMUY+PIG31qZnFMfPWNCg==,
      }

  "@types/ssh2@0.5.52":
    resolution:
      {
        integrity: sha512-lbLLlXxdCZOSJMCInKH2+9V/77ET2J6NPQHpFI0kda61Dd1KglJs+fPQBchizmzYSOJBgdTajhPqBO1xxLywvg==,
      }

  "@types/ssh2@1.15.0":
    resolution:
      {
        integrity: sha512-YcT8jP5F8NzWeevWvcyrrLB3zcneVjzYY9ZDSMAMboI+2zR1qYWFhwsyOFVzT7Jorn67vqxC0FRiw8YyG9P1ww==,
      }

  "@types/stack-utils@2.0.3":
    resolution:
      {
        integrity: sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==,
      }

  "@types/yargs-parser@21.0.3":
    resolution:
      {
        integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==,
      }

  "@types/yargs@17.0.32":
    resolution:
      {
        integrity: sha512-xQ67Yc/laOG5uMfX/093MRlGGCIBzZMarVa+gfNKJxWAIgykYpVGkBdbqEzGDDfCrVUj6Hiff4mTZ5BA6TmAog==,
      }

  "@xenova/transformers@2.17.2":
    resolution:
      {
        integrity: sha512-lZmHqzrVIkSvZdKZEx7IYY51TK0WDrC8eR0c5IMnBsO8di8are1zzw8BlLhyO2TklZKLN5UffNGs1IJwT6oOqQ==,
      }

  abbrev@1.1.1:
    resolution:
      {
        integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==,
      }

  abort-controller@3.0.0:
    resolution:
      {
        integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==,
      }
    engines: { node: ">=6.5" }

  acorn-walk@8.3.2:
    resolution:
      {
        integrity: sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==,
      }
    engines: { node: ">=0.4.0" }

  acorn@8.11.3:
    resolution:
      {
        integrity: sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==,
      }
    engines: { node: ">=0.4.0" }
    hasBin: true

  agent-base@6.0.2:
    resolution:
      {
        integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==,
      }
    engines: { node: ">= 6.0.0" }

  agentkeepalive@4.5.0:
    resolution:
      {
        integrity: sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==,
      }
    engines: { node: ">= 8.0.0" }

  ajv-draft-04@1.0.0:
    resolution:
      {
        integrity: sha512-mv00Te6nmYbRp5DCwclxtt7yV/joXJPGS7nM+97GdxvuttCOfgI3K4U25zboyeX0O+myI8ERluxQe5wljMmVIw==,
      }
    peerDependencies:
      ajv: ^8.5.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv@8.16.0:
    resolution:
      {
        integrity: sha512-F0twR8U1ZU67JIEtekUcLkXkoO5mMMmgGD8sK/xUFzJ805jxHQl92hImFAqqXMyMYjSPOyUPAwHYhB72g5sTXw==,
      }

  ansi-colors@4.1.3:
    resolution:
      {
        integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==,
      }
    engines: { node: ">=6" }

  ansi-escapes@4.3.2:
    resolution:
      {
        integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==,
      }
    engines: { node: ">=8" }

  ansi-regex@5.0.1:
    resolution:
      {
        integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==,
      }
    engines: { node: ">=8" }

  ansi-regex@6.0.1:
    resolution:
      {
        integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==,
      }
    engines: { node: ">=12" }

  ansi-styles@3.2.1:
    resolution:
      {
        integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==,
      }
    engines: { node: ">=4" }

  ansi-styles@4.3.0:
    resolution:
      {
        integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==,
      }
    engines: { node: ">=8" }

  ansi-styles@5.2.0:
    resolution:
      {
        integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==,
      }
    engines: { node: ">=10" }

  ansi-styles@6.2.1:
    resolution:
      {
        integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==,
      }
    engines: { node: ">=12" }

  any-promise@1.3.0:
    resolution:
      {
        integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==,
      }

  anymatch@3.1.3:
    resolution:
      {
        integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==,
      }
    engines: { node: ">= 8" }

  aproba@2.0.0:
    resolution:
      {
        integrity: sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==,
      }

  archiver-utils@2.1.0:
    resolution:
      {
        integrity: sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==,
      }
    engines: { node: ">= 6" }

  archiver-utils@3.0.4:
    resolution:
      {
        integrity: sha512-KVgf4XQVrTjhyWmx6cte4RxonPLR9onExufI1jhvw/MQ4BB6IsZD5gT8Lq+u/+pRkWna/6JoHpiQioaqFP5Rzw==,
      }
    engines: { node: ">= 10" }

  archiver@5.3.2:
    resolution:
      {
        integrity: sha512-+25nxyyznAXF7Nef3y0EbBeqmGZgeN/BxHX29Rs39djAfaFalmQ89SE6CWyDCHzGL0yt/ycBtNOmGTW0FyGWNw==,
      }
    engines: { node: ">= 10" }

  are-we-there-yet@2.0.0:
    resolution:
      {
        integrity: sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==,
      }
    engines: { node: ">=10" }
    deprecated: This package is no longer supported.

  arg@4.1.3:
    resolution:
      {
        integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==,
      }

  argparse@1.0.10:
    resolution:
      {
        integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==,
      }

  argparse@2.0.1:
    resolution:
      {
        integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==,
      }

  array-buffer-byte-length@1.0.1:
    resolution:
      {
        integrity: sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==,
      }
    engines: { node: ">= 0.4" }

  array-union@2.1.0:
    resolution:
      {
        integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==,
      }
    engines: { node: ">=8" }

  arraybuffer.prototype.slice@1.0.3:
    resolution:
      {
        integrity: sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==,
      }
    engines: { node: ">= 0.4" }

  arrify@1.0.1:
    resolution:
      {
        integrity: sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==,
      }
    engines: { node: ">=0.10.0" }

  asn1@0.2.6:
    resolution:
      {
        integrity: sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==,
      }

  async-lock@1.4.1:
    resolution:
      {
        integrity: sha512-Az2ZTpuytrtqENulXwO3GGv1Bztugx6TT37NIo7imr/Qo0gsYiGtSdBa2B6fsXhTpVZDNfu1Qn3pk531e3q+nQ==,
      }

  async@3.2.5:
    resolution:
      {
        integrity: sha512-baNZyqaaLhyLVKm/DlvdW051MSgO6b8eVfIezl9E5PqWxFgzLm/wQntEW4zOytVburDEr0JlALEpdOFwvErLsg==,
      }

  asynckit@0.4.0:
    resolution:
      {
        integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==,
      }

  available-typed-arrays@1.0.7:
    resolution:
      {
        integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==,
      }
    engines: { node: ">= 0.4" }

  b4a@1.6.6:
    resolution:
      {
        integrity: sha512-5Tk1HLk6b6ctmjIkAcU/Ujv/1WqiDl0F0JdRCR80VsOcUlHcu7pWeWRlOqQLHfDEsVx9YH/aif5AG4ehoCtTmg==,
      }

  babel-jest@29.7.0:
    resolution:
      {
        integrity: sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      "@babel/core": ^7.8.0

  babel-plugin-istanbul@6.1.1:
    resolution:
      {
        integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==,
      }
    engines: { node: ">=8" }

  babel-plugin-jest-hoist@29.6.3:
    resolution:
      {
        integrity: sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  babel-preset-current-node-syntax@1.0.1:
    resolution:
      {
        integrity: sha512-M7LQ0bxarkxQoN+vz5aJPsLBn77n8QgTFmo8WK0/44auK2xlCXrYcUxHFxgU7qW5Yzw/CjmLRK2uJzaCd7LvqQ==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0

  babel-preset-jest@29.6.3:
    resolution:
      {
        integrity: sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      "@babel/core": ^7.0.0

  balanced-match@1.0.2:
    resolution:
      {
        integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==,
      }

  bare-events@2.4.2:
    resolution:
      {
        integrity: sha512-qMKFd2qG/36aA4GwvKq8MxnPgCQAmBWmSyLWsJcbn8v03wvIPQ/hG1Ms8bPzndZxMDoHpxez5VOS+gC9Yi24/Q==,
      }

  bare-fs@2.3.1:
    resolution:
      {
        integrity: sha512-W/Hfxc/6VehXlsgFtbB5B4xFcsCl+pAh30cYhoFyXErf6oGrwjh8SwiPAdHgpmWonKuYpZgGywN0SXt7dgsADA==,
      }

  bare-os@2.4.0:
    resolution:
      {
        integrity: sha512-v8DTT08AS/G0F9xrhyLtepoo9EJBJ85FRSMbu1pQUlAf6A8T0tEEQGMVObWeqpjhSPXsE0VGlluFBJu2fdoTNg==,
      }

  bare-path@2.1.3:
    resolution:
      {
        integrity: sha512-lh/eITfU8hrj9Ru5quUp0Io1kJWIk1bTjzo7JH1P5dWmQ2EL4hFUlfI8FonAhSlgIfhn63p84CDY/x+PisgcXA==,
      }

  bare-stream@2.1.3:
    resolution:
      {
        integrity: sha512-tiDAH9H/kP+tvNO5sczyn9ZAA7utrSMobyDchsnyyXBuUe2FSQWbxhtuHB8jwpHYYevVo2UJpcmvvjrbHboUUQ==,
      }

  base64-js@1.5.1:
    resolution:
      {
        integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==,
      }

  bcrypt-pbkdf@1.0.2:
    resolution:
      {
        integrity: sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==,
      }

  bcrypt@5.1.1:
    resolution:
      {
        integrity: sha512-AGBHOG5hPYZ5Xl9KXzU5iKq9516yEmvCKDg3ecP5kX2aB6UqTeXZxk2ELnDgDm6BQSMlLt9rDB4LoSMx0rYwww==,
      }
    engines: { node: ">= 10.0.0" }

  binary-extensions@2.3.0:
    resolution:
      {
        integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==,
      }
    engines: { node: ">=8" }

  bl@4.1.0:
    resolution:
      {
        integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==,
      }

  bowser@2.11.0:
    resolution:
      {
        integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==,
      }

  brace-expansion@1.1.11:
    resolution:
      {
        integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==,
      }

  brace-expansion@2.0.1:
    resolution:
      {
        integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==,
      }

  braces@3.0.3:
    resolution:
      {
        integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==,
      }
    engines: { node: ">=8" }

  browserslist@4.23.1:
    resolution:
      {
        integrity: sha512-TUfofFo/KsK/bWZ9TWQ5O26tsWW4Uhmt8IYklbnUa70udB6P2wA7w7o4PY4muaEPBQaAX+CEnmmIA41NVHtPVw==,
      }
    engines: { node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7 }
    hasBin: true

  bs-logger@0.2.6:
    resolution:
      {
        integrity: sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==,
      }
    engines: { node: ">= 6" }

  bser@2.1.1:
    resolution:
      {
        integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==,
      }

  buffer-crc32@0.2.13:
    resolution:
      {
        integrity: sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==,
      }

  buffer-from@1.1.2:
    resolution:
      {
        integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==,
      }

  buffer@5.7.1:
    resolution:
      {
        integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==,
      }

  buffer@6.0.3:
    resolution:
      {
        integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==,
      }

  buildcheck@0.0.6:
    resolution:
      {
        integrity: sha512-8f9ZJCUXyT1M35Jx7MkBgmBMo3oHTTBIPLiY9xyL0pl3T5RwcPEY8cUHr5LBNfu/fk6c2T4DJZuVM/8ZZT2D2A==,
      }
    engines: { node: ">=10.0.0" }

  bundle-require@4.2.1:
    resolution:
      {
        integrity: sha512-7Q/6vkyYAwOmQNRw75x+4yRtZCZJXUDmHHlFdkiV0wgv/reNjtJwpu1jPJ0w2kbEpIM0uoKI3S4/f39dU7AjSA==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }
    peerDependencies:
      esbuild: ">=0.17"

  byline@5.0.0:
    resolution:
      {
        integrity: sha512-s6webAy+R4SR8XVuJWt2V2rGvhnrhxN+9S15GNuTK3wKPOXFF6RNc+8ug2XhH+2s4f+uudG4kUVYmYOQWL2g0Q==,
      }
    engines: { node: ">=0.10.0" }

  cac@6.7.14:
    resolution:
      {
        integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==,
      }
    engines: { node: ">=8" }

  call-bind@1.0.7:
    resolution:
      {
        integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==,
      }
    engines: { node: ">= 0.4" }

  call-me-maybe@1.0.2:
    resolution:
      {
        integrity: sha512-HpX65o1Hnr9HH25ojC1YGs7HCQLq0GCOibSaWER0eNpgJ/Z1MZv2mTc7+xh6WOPxbRVcmgbv4hGU+uSQ/2xFZQ==,
      }

  callsites@3.1.0:
    resolution:
      {
        integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==,
      }
    engines: { node: ">=6" }

  camel-case@4.1.2:
    resolution:
      {
        integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==,
      }

  camelcase-keys@6.2.2:
    resolution:
      {
        integrity: sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==,
      }
    engines: { node: ">=8" }

  camelcase@5.3.1:
    resolution:
      {
        integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==,
      }
    engines: { node: ">=6" }

  camelcase@6.3.0:
    resolution:
      {
        integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==,
      }
    engines: { node: ">=10" }

  caniuse-lite@1.0.30001633:
    resolution:
      {
        integrity: sha512-6sT0yf/z5jqf8tISAgpJDrmwOpLsrpnyCdD/lOZKvKkkJK4Dn0X5i7KF7THEZhOq+30bmhwBlNEaqPUiHiKtZg==,
      }

  capital-case@1.0.4:
    resolution:
      {
        integrity: sha512-ds37W8CytHgwnhGGTi88pcPyR15qoNkOpYwmMMfnWqqWgESapLqvDx6huFjQ5vqWSn2Z06173XNA7LtMOeUh1A==,
      }

  chalk@2.4.2:
    resolution:
      {
        integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==,
      }
    engines: { node: ">=4" }

  chalk@4.1.2:
    resolution:
      {
        integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==,
      }
    engines: { node: ">=10" }

  change-case@4.1.2:
    resolution:
      {
        integrity: sha512-bSxY2ws9OtviILG1EiY5K7NNxkqg/JnRnFxLtKQ96JaviiIxi7djMrSd0ECT9AC+lttClmYwKw53BWpOMblo7A==,
      }

  char-regex@1.0.2:
    resolution:
      {
        integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==,
      }
    engines: { node: ">=10" }

  chokidar@3.6.0:
    resolution:
      {
        integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==,
      }
    engines: { node: ">= 8.10.0" }

  chownr@1.1.4:
    resolution:
      {
        integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==,
      }

  chownr@2.0.0:
    resolution:
      {
        integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==,
      }
    engines: { node: ">=10" }

  chromadb-default-embed@2.14.0:
    resolution:
      {
        integrity: sha512-odCiCzZ5jqNI0sS6RcRxObx8gM7aCPULQkdWw/OgqIGdIUOKUj9b8jDElLbZ6feMKNB0MSQhtXi0P8QEeVO75w==,
      }

  ci-info@3.9.0:
    resolution:
      {
        integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==,
      }
    engines: { node: ">=8" }

  cjs-module-lexer@1.3.1:
    resolution:
      {
        integrity: sha512-a3KdPAANPbNE4ZUv9h6LckSl9zLsYOP4MBmhIPkRaeyybt+r4UghLvq+xw/YwUcC1gqylCkL4rdVs3Lwupjm4Q==,
      }

  cliui@8.0.1:
    resolution:
      {
        integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==,
      }
    engines: { node: ">=12" }

  co@4.6.0:
    resolution:
      {
        integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==,
      }
    engines: { iojs: ">= 1.0.0", node: ">= 0.12.0" }

  cohere-ai@7.10.5:
    resolution:
      {
        integrity: sha512-mfMiWktE07z0BzhRl0pJSLsNEXMlPPfkxR75PCEc99tuK5auVD2ZLhr37GtByQbN2ToIq34HrfQvMCZFO5V5pA==,
      }

  collect-v8-coverage@1.0.2:
    resolution:
      {
        integrity: sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==,
      }

  color-convert@1.9.3:
    resolution:
      {
        integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==,
      }

  color-convert@2.0.1:
    resolution:
      {
        integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==,
      }
    engines: { node: ">=7.0.0" }

  color-name@1.1.3:
    resolution:
      {
        integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==,
      }

  color-name@1.1.4:
    resolution:
      {
        integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==,
      }

  color-string@1.9.1:
    resolution:
      {
        integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==,
      }

  color-support@1.1.3:
    resolution:
      {
        integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==,
      }
    hasBin: true

  color@4.2.3:
    resolution:
      {
        integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==,
      }
    engines: { node: ">=12.5.0" }

  combined-stream@1.0.8:
    resolution:
      {
        integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==,
      }
    engines: { node: ">= 0.8" }

  commander@4.1.1:
    resolution:
      {
        integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==,
      }
    engines: { node: ">= 6" }

  compress-commons@4.1.2:
    resolution:
      {
        integrity: sha512-D3uMHtGc/fcO1Gt1/L7i1e33VOvD4A9hfQLP+6ewd+BvG/gQ84Yh4oftEhAdjSMgBgwGL+jsppT7JYNpo6MHHg==,
      }
    engines: { node: ">= 10" }

  concat-map@0.0.1:
    resolution:
      {
        integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==,
      }

  console-control-strings@1.1.0:
    resolution:
      {
        integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==,
      }

  constant-case@3.0.4:
    resolution:
      {
        integrity: sha512-I2hSBi7Vvs7BEuJDr5dDHfzb/Ruj3FyvFyh7KLilAjNQw3Be+xgqUBA2W6scVEcL0hL1dwPRtIqEPVUCKkSsyQ==,
      }

  convert-source-map@2.0.0:
    resolution:
      {
        integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==,
      }

  core-util-is@1.0.3:
    resolution:
      {
        integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==,
      }

  cpu-features@0.0.10:
    resolution:
      {
        integrity: sha512-9IkYqtX3YHPCzoVg1Py+o9057a3i0fp7S530UWokCSaFVTc7CwXPRiOjRjBQQ18ZCNafx78YfnG+HALxtVmOGA==,
      }
    engines: { node: ">=10.0.0" }

  crc-32@1.2.2:
    resolution:
      {
        integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==,
      }
    engines: { node: ">=0.8" }
    hasBin: true

  crc32-stream@4.0.3:
    resolution:
      {
        integrity: sha512-NT7w2JVU7DFroFdYkeq8cywxrgjPHWkdX1wjpRQXPX5Asews3tA+Ght6lddQO5Mkumffp3X7GEqku3epj2toIw==,
      }
    engines: { node: ">= 10" }

  create-jest@29.7.0:
    resolution:
      {
        integrity: sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    hasBin: true

  create-require@1.1.1:
    resolution:
      {
        integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==,
      }

  cross-spawn@6.0.5:
    resolution:
      {
        integrity: sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==,
      }
    engines: { node: ">=4.8" }

  cross-spawn@7.0.3:
    resolution:
      {
        integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==,
      }
    engines: { node: ">= 8" }

  data-view-buffer@1.0.1:
    resolution:
      {
        integrity: sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==,
      }
    engines: { node: ">= 0.4" }

  data-view-byte-length@1.0.1:
    resolution:
      {
        integrity: sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==,
      }
    engines: { node: ">= 0.4" }

  data-view-byte-offset@1.0.0:
    resolution:
      {
        integrity: sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==,
      }
    engines: { node: ">= 0.4" }

  debug@4.3.5:
    resolution:
      {
        integrity: sha512-pt0bNEmneDIvdL1Xsd9oDQ/wrQRkXDT4AUWlNZNPKvW5x/jyO9VFXkJUP07vQ2upmw5PlaITaPKc31jK13V+jg==,
      }
    engines: { node: ">=6.0" }
    peerDependencies:
      supports-color: "*"
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize-keys@1.1.1:
    resolution:
      {
        integrity: sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==,
      }
    engines: { node: ">=0.10.0" }

  decamelize@1.2.0:
    resolution:
      {
        integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==,
      }
    engines: { node: ">=0.10.0" }

  decompress-response@6.0.0:
    resolution:
      {
        integrity: sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==,
      }
    engines: { node: ">=10" }

  dedent@1.5.3:
    resolution:
      {
        integrity: sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==,
      }
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  deep-extend@0.6.0:
    resolution:
      {
        integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==,
      }
    engines: { node: ">=4.0.0" }

  deepmerge@4.3.1:
    resolution:
      {
        integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==,
      }
    engines: { node: ">=0.10.0" }

  define-data-property@1.1.4:
    resolution:
      {
        integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==,
      }
    engines: { node: ">= 0.4" }

  define-properties@1.2.1:
    resolution:
      {
        integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==,
      }
    engines: { node: ">= 0.4" }

  delayed-stream@1.0.0:
    resolution:
      {
        integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==,
      }
    engines: { node: ">=0.4.0" }

  delegates@1.0.0:
    resolution:
      {
        integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==,
      }

  detect-libc@2.0.3:
    resolution:
      {
        integrity: sha512-bwy0MGW55bG41VqxxypOsdSdGqLwXPI/focwgTYCFMbdUiBAxLg9CFzG08sz2aqzknwiX7Hkl0bQENjg8iLByw==,
      }
    engines: { node: ">=8" }

  detect-newline@3.1.0:
    resolution:
      {
        integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==,
      }
    engines: { node: ">=8" }

  diff-sequences@29.6.3:
    resolution:
      {
        integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  diff@4.0.2:
    resolution:
      {
        integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==,
      }
    engines: { node: ">=0.3.1" }

  dir-glob@3.0.1:
    resolution:
      {
        integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==,
      }
    engines: { node: ">=8" }

  docker-compose@0.24.8:
    resolution:
      {
        integrity: sha512-plizRs/Vf15H+GCVxq2EUvyPK7ei9b/cVesHvjnX4xaXjM9spHe2Ytq0BitndFgvTJ3E3NljPNUEl7BAN43iZw==,
      }
    engines: { node: ">= 6.0.0" }

  docker-modem@3.0.8:
    resolution:
      {
        integrity: sha512-f0ReSURdM3pcKPNS30mxOHSbaFLcknGmQjwSfmbcdOw1XWKXVhukM3NJHhr7NpY9BIyyWQb0EBo3KQvvuU5egQ==,
      }
    engines: { node: ">= 8.0" }

  dockerode@3.3.5:
    resolution:
      {
        integrity: sha512-/0YNa3ZDNeLr/tSckmD69+Gq+qVNhvKfAHNeZJBnp7EOP6RGKV8ORrJHkUn20So5wU+xxT7+1n5u8PjHbfjbSA==,
      }
    engines: { node: ">= 8.0" }

  dot-case@3.0.4:
    resolution:
      {
        integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==,
      }

  eastasianwidth@0.2.0:
    resolution:
      {
        integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==,
      }

  electron-to-chromium@1.4.802:
    resolution:
      {
        integrity: sha512-TnTMUATbgNdPXVSHsxvNVSG0uEd6cSZsANjm8c9HbvflZVVn1yTRcmVXYT1Ma95/ssB/Dcd30AHweH2TE+dNpA==,
      }

  emittery@0.13.1:
    resolution:
      {
        integrity: sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==,
      }
    engines: { node: ">=12" }

  emoji-regex@8.0.0:
    resolution:
      {
        integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==,
      }

  emoji-regex@9.2.2:
    resolution:
      {
        integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==,
      }

  end-of-stream@1.4.4:
    resolution:
      {
        integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==,
      }

  error-ex@1.3.2:
    resolution:
      {
        integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==,
      }

  es-abstract@1.23.3:
    resolution:
      {
        integrity: sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A==,
      }
    engines: { node: ">= 0.4" }

  es-define-property@1.0.0:
    resolution:
      {
        integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==,
      }
    engines: { node: ">= 0.4" }

  es-errors@1.3.0:
    resolution:
      {
        integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==,
      }
    engines: { node: ">= 0.4" }

  es-object-atoms@1.0.0:
    resolution:
      {
        integrity: sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==,
      }
    engines: { node: ">= 0.4" }

  es-set-tostringtag@2.0.3:
    resolution:
      {
        integrity: sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==,
      }
    engines: { node: ">= 0.4" }

  es-to-primitive@1.2.1:
    resolution:
      {
        integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==,
      }
    engines: { node: ">= 0.4" }

  esbuild@0.19.12:
    resolution:
      {
        integrity: sha512-aARqgq8roFBj054KvQr5f1sFu0D65G+miZRCuJyJ0G13Zwx7vRar5Zhn2tkQNzIXcBrNVsv/8stehpj+GAjgbg==,
      }
    engines: { node: ">=12" }
    hasBin: true

  escalade@3.1.2:
    resolution:
      {
        integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==,
      }
    engines: { node: ">=6" }

  escape-string-regexp@1.0.5:
    resolution:
      {
        integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==,
      }
    engines: { node: ">=0.8.0" }

  escape-string-regexp@2.0.0:
    resolution:
      {
        integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==,
      }
    engines: { node: ">=8" }

  eslint-formatter-pretty@4.1.0:
    resolution:
      {
        integrity: sha512-IsUTtGxF1hrH6lMWiSl1WbGaiP01eT6kzywdY1U+zLc0MP+nwEnUiS9UI8IaOTUhTeQJLlCEWIbXINBH4YJbBQ==,
      }
    engines: { node: ">=10" }

  eslint-rule-docs@1.1.235:
    resolution:
      {
        integrity: sha512-+TQ+x4JdTnDoFEXXb3fDvfGOwnyNV7duH8fXWTPD1ieaBmB8omj7Gw/pMBBu4uI2uJCCU8APDaQJzWuXnTsH4A==,
      }

  esprima@4.0.1:
    resolution:
      {
        integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==,
      }
    engines: { node: ">=4" }
    hasBin: true

  event-target-shim@5.0.1:
    resolution:
      {
        integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==,
      }
    engines: { node: ">=6" }

  events@3.3.0:
    resolution:
      {
        integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==,
      }
    engines: { node: ">=0.8.x" }

  execa@5.1.1:
    resolution:
      {
        integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==,
      }
    engines: { node: ">=10" }

  exit@0.1.2:
    resolution:
      {
        integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==,
      }
    engines: { node: ">= 0.8.0" }

  expand-template@2.0.3:
    resolution:
      {
        integrity: sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==,
      }
    engines: { node: ">=6" }

  expect@29.7.0:
    resolution:
      {
        integrity: sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  fast-deep-equal@3.1.3:
    resolution:
      {
        integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==,
      }

  fast-fifo@1.3.2:
    resolution:
      {
        integrity: sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ==,
      }

  fast-glob@3.3.2:
    resolution:
      {
        integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==,
      }
    engines: { node: ">=8.6.0" }

  fast-json-stable-stringify@2.1.0:
    resolution:
      {
        integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==,
      }

  fast-xml-parser@4.2.5:
    resolution:
      {
        integrity: sha512-B9/wizE4WngqQftFPmdaMYlXoJlJOYxGQOanC77fq9k8+Z0v5dDSVh+3glErdIROP//s/jgb7ZuxKfB8nVyo0g==,
      }
    hasBin: true

  fastq@1.17.1:
    resolution:
      {
        integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==,
      }

  fb-watchman@2.0.2:
    resolution:
      {
        integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==,
      }

  fill-range@7.1.1:
    resolution:
      {
        integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==,
      }
    engines: { node: ">=8" }

  find-up@4.1.0:
    resolution:
      {
        integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==,
      }
    engines: { node: ">=8" }

  flatbuffers@1.12.0:
    resolution:
      {
        integrity: sha512-c7CZADjRcl6j0PlvFy0ZqXQ67qSEZfrVPynmnL+2zPc+NtMvrF8Y0QceMo7QqnSPc7+uWjUIAbvCQ5WIKlMVdQ==,
      }

  for-each@0.3.3:
    resolution:
      {
        integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==,
      }

  foreground-child@3.2.0:
    resolution:
      {
        integrity: sha512-CrWQNaEl1/6WeZoarcM9LHupTo3RpZO2Pdk1vktwzPiQTsJnAKJmm3TACKeG5UZbWDfaH2AbvYxzP96y0MT7fA==,
      }
    engines: { node: ">=14" }

  form-data-encoder@1.7.2:
    resolution:
      {
        integrity: sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==,
      }

  form-data-encoder@4.0.2:
    resolution:
      {
        integrity: sha512-KQVhvhK8ZkWzxKxOr56CPulAhH3dobtuQ4+hNQ+HekH/Wp5gSOafqRAeTphQUJAIk0GBvHZgJ2ZGRWd5kphMuw==,
      }
    engines: { node: ">= 18" }

  form-data@4.0.0:
    resolution:
      {
        integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==,
      }
    engines: { node: ">= 6" }

  formdata-node@4.4.1:
    resolution:
      {
        integrity: sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==,
      }
    engines: { node: ">= 12.20" }

  formdata-node@6.0.3:
    resolution:
      {
        integrity: sha512-8e1++BCiTzUno9v5IZ2J6bv4RU+3UKDmqWUQD0MIMVCd9AdhWkO1gw57oo1mNEX1dMq2EGI+FbWz4B92pscSQg==,
      }
    engines: { node: ">= 18" }

  fs-constants@1.0.0:
    resolution:
      {
        integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==,
      }

  fs-minipass@2.1.0:
    resolution:
      {
        integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==,
      }
    engines: { node: ">= 8" }

  fs.realpath@1.0.0:
    resolution:
      {
        integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==,
      }

  fsevents@2.3.3:
    resolution:
      {
        integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==,
      }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  function-bind@1.1.2:
    resolution:
      {
        integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==,
      }

  function.prototype.name@1.1.6:
    resolution:
      {
        integrity: sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==,
      }
    engines: { node: ">= 0.4" }

  functions-have-names@1.2.3:
    resolution:
      {
        integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==,
      }

  gauge@3.0.2:
    resolution:
      {
        integrity: sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==,
      }
    engines: { node: ">=10" }
    deprecated: This package is no longer supported.

  gensync@1.0.0-beta.2:
    resolution:
      {
        integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==,
      }
    engines: { node: ">=6.9.0" }

  get-caller-file@2.0.5:
    resolution:
      {
        integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==,
      }
    engines: { node: 6.* || 8.* || >= 10.* }

  get-intrinsic@1.2.4:
    resolution:
      {
        integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==,
      }
    engines: { node: ">= 0.4" }

  get-package-type@0.1.0:
    resolution:
      {
        integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==,
      }
    engines: { node: ">=8.0.0" }

  get-port@5.1.1:
    resolution:
      {
        integrity: sha512-g/Q1aTSDOxFpchXC4i8ZWvxA1lnPqx/JHqcpIw0/LX9T8x/GBbi6YnlN5nhaKIFkT8oFsscUKgDJYxfwfS6QsQ==,
      }
    engines: { node: ">=8" }

  get-stream@6.0.1:
    resolution:
      {
        integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==,
      }
    engines: { node: ">=10" }

  get-symbol-description@1.0.2:
    resolution:
      {
        integrity: sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==,
      }
    engines: { node: ">= 0.4" }

  getopts@2.3.0:
    resolution:
      {
        integrity: sha512-5eDf9fuSXwxBL6q5HX+dhDj+dslFGWzU5thZ9kNKUkcPtaPdatmUFKwHFrLb/uf/WpA4BHET+AX3Scl56cAjpA==,
      }

  github-from-package@0.0.0:
    resolution:
      {
        integrity: sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw==,
      }

  glob-parent@5.1.2:
    resolution:
      {
        integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==,
      }
    engines: { node: ">= 6" }

  glob@10.4.1:
    resolution:
      {
        integrity: sha512-2jelhlq3E4ho74ZyVLN03oKdAZVUa6UDZzFLVH1H7dnoax+y9qyaq8zBkfDIggjniU19z0wU18y16jMB2eyVIw==,
      }
    engines: { node: ">=16 || 14 >=14.18" }
    hasBin: true

  glob@7.2.3:
    resolution:
      {
        integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==,
      }
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution:
      {
        integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==,
      }
    engines: { node: ">=4" }

  globalthis@1.0.4:
    resolution:
      {
        integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==,
      }
    engines: { node: ">= 0.4" }

  globby@11.1.0:
    resolution:
      {
        integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==,
      }
    engines: { node: ">=10" }

  gopd@1.0.1:
    resolution:
      {
        integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==,
      }

  graceful-fs@4.2.11:
    resolution:
      {
        integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==,
      }

  guid-typescript@1.0.9:
    resolution:
      {
        integrity: sha512-Y8T4vYhEfwJOTbouREvG+3XDsjr8E3kIr7uf+JZ0BYloFsttiHU0WfvANVsR7TxNUJa/WpCnw/Ino/p+DeBhBQ==,
      }

  handlebars@4.7.8:
    resolution:
      {
        integrity: sha512-vafaFqs8MZkRrSX7sFVUdo3ap/eNiLnb4IakshzvP56X5Nr1iGKAIqdX6tMlm6HcNRIkr6AxO5jFEoJzzpT8aQ==,
      }
    engines: { node: ">=0.4.7" }
    hasBin: true

  hard-rejection@2.1.0:
    resolution:
      {
        integrity: sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==,
      }
    engines: { node: ">=6" }

  has-bigints@1.0.2:
    resolution:
      {
        integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==,
      }

  has-flag@3.0.0:
    resolution:
      {
        integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==,
      }
    engines: { node: ">=4" }

  has-flag@4.0.0:
    resolution:
      {
        integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==,
      }
    engines: { node: ">=8" }

  has-property-descriptors@1.0.2:
    resolution:
      {
        integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==,
      }

  has-proto@1.0.3:
    resolution:
      {
        integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==,
      }
    engines: { node: ">= 0.4" }

  has-symbols@1.0.3:
    resolution:
      {
        integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==,
      }
    engines: { node: ">= 0.4" }

  has-tostringtag@1.0.2:
    resolution:
      {
        integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==,
      }
    engines: { node: ">= 0.4" }

  has-unicode@2.0.1:
    resolution:
      {
        integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==,
      }

  hasown@2.0.2:
    resolution:
      {
        integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==,
      }
    engines: { node: ">= 0.4" }

  header-case@2.0.4:
    resolution:
      {
        integrity: sha512-H/vuk5TEEVZwrR0lp2zed9OCo1uAILMlx0JEMgC26rzyJJ3N1v6XkwHHXJQdR2doSjcGPM6OKPYoJgf0plJ11Q==,
      }

  hosted-git-info@2.8.9:
    resolution:
      {
        integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==,
      }

  hosted-git-info@4.1.0:
    resolution:
      {
        integrity: sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==,
      }
    engines: { node: ">=10" }

  html-escaper@2.0.2:
    resolution:
      {
        integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==,
      }

  https-proxy-agent@5.0.1:
    resolution:
      {
        integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==,
      }
    engines: { node: ">= 6" }

  human-signals@2.1.0:
    resolution:
      {
        integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==,
      }
    engines: { node: ">=10.17.0" }

  humanize-ms@1.2.1:
    resolution:
      {
        integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==,
      }

  ieee754@1.2.1:
    resolution:
      {
        integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==,
      }

  ignore@5.3.1:
    resolution:
      {
        integrity: sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==,
      }
    engines: { node: ">= 4" }

  import-local@3.1.0:
    resolution:
      {
        integrity: sha512-ASB07uLtnDs1o6EHjKpX34BKYDSqnFerfTOJL2HvMqF70LnxpjkzDB8J44oT9pu4AMPkQwf8jl6szgvNd2tRIg==,
      }
    engines: { node: ">=8" }
    hasBin: true

  imurmurhash@0.1.4:
    resolution:
      {
        integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==,
      }
    engines: { node: ">=0.8.19" }

  indent-string@4.0.0:
    resolution:
      {
        integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==,
      }
    engines: { node: ">=8" }

  inflight@1.0.6:
    resolution:
      {
        integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==,
      }
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution:
      {
        integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==,
      }

  ini@1.3.8:
    resolution:
      {
        integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==,
      }

  internal-slot@1.0.7:
    resolution:
      {
        integrity: sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==,
      }
    engines: { node: ">= 0.4" }

  irregular-plurals@3.5.0:
    resolution:
      {
        integrity: sha512-1ANGLZ+Nkv1ptFb2pa8oG8Lem4krflKuX/gINiHJHjJUKaJHk/SXk5x6K3J+39/p0h1RQ2saROclJJ+QLvETCQ==,
      }
    engines: { node: ">=8" }

  is-array-buffer@3.0.4:
    resolution:
      {
        integrity: sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==,
      }
    engines: { node: ">= 0.4" }

  is-arrayish@0.2.1:
    resolution:
      {
        integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==,
      }

  is-arrayish@0.3.2:
    resolution:
      {
        integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==,
      }

  is-bigint@1.0.4:
    resolution:
      {
        integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==,
      }

  is-binary-path@2.1.0:
    resolution:
      {
        integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==,
      }
    engines: { node: ">=8" }

  is-boolean-object@1.1.2:
    resolution:
      {
        integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==,
      }
    engines: { node: ">= 0.4" }

  is-callable@1.2.7:
    resolution:
      {
        integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==,
      }
    engines: { node: ">= 0.4" }

  is-core-module@2.13.1:
    resolution:
      {
        integrity: sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==,
      }

  is-data-view@1.0.1:
    resolution:
      {
        integrity: sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==,
      }
    engines: { node: ">= 0.4" }

  is-date-object@1.0.5:
    resolution:
      {
        integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==,
      }
    engines: { node: ">= 0.4" }

  is-extglob@2.1.1:
    resolution:
      {
        integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==,
      }
    engines: { node: ">=0.10.0" }

  is-fullwidth-code-point@3.0.0:
    resolution:
      {
        integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==,
      }
    engines: { node: ">=8" }

  is-generator-fn@2.1.0:
    resolution:
      {
        integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==,
      }
    engines: { node: ">=6" }

  is-glob@4.0.3:
    resolution:
      {
        integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==,
      }
    engines: { node: ">=0.10.0" }

  is-negative-zero@2.0.3:
    resolution:
      {
        integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==,
      }
    engines: { node: ">= 0.4" }

  is-number-object@1.0.7:
    resolution:
      {
        integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==,
      }
    engines: { node: ">= 0.4" }

  is-number@7.0.0:
    resolution:
      {
        integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==,
      }
    engines: { node: ">=0.12.0" }

  is-plain-obj@1.1.0:
    resolution:
      {
        integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==,
      }
    engines: { node: ">=0.10.0" }

  is-regex@1.1.4:
    resolution:
      {
        integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==,
      }
    engines: { node: ">= 0.4" }

  is-shared-array-buffer@1.0.3:
    resolution:
      {
        integrity: sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==,
      }
    engines: { node: ">= 0.4" }

  is-stream@2.0.1:
    resolution:
      {
        integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==,
      }
    engines: { node: ">=8" }

  is-string@1.0.7:
    resolution:
      {
        integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==,
      }
    engines: { node: ">= 0.4" }

  is-symbol@1.0.4:
    resolution:
      {
        integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==,
      }
    engines: { node: ">= 0.4" }

  is-typed-array@1.1.13:
    resolution:
      {
        integrity: sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==,
      }
    engines: { node: ">= 0.4" }

  is-unicode-supported@0.1.0:
    resolution:
      {
        integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==,
      }
    engines: { node: ">=10" }

  is-weakref@1.0.2:
    resolution:
      {
        integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==,
      }

  isarray@1.0.0:
    resolution:
      {
        integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==,
      }

  isarray@2.0.5:
    resolution:
      {
        integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==,
      }

  isexe@2.0.0:
    resolution:
      {
        integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==,
      }

  isomorphic-fetch@3.0.0:
    resolution:
      {
        integrity: sha512-qvUtwJ3j6qwsF3jLxkZ72qCgjMysPzDfeV240JHiGZsANBYd+EEuu35v7dfrJ9Up0Ak07D7GGSkGhCHTqg/5wA==,
      }

  istanbul-lib-coverage@3.2.2:
    resolution:
      {
        integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==,
      }
    engines: { node: ">=8" }

  istanbul-lib-instrument@5.2.1:
    resolution:
      {
        integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==,
      }
    engines: { node: ">=8" }

  istanbul-lib-instrument@6.0.2:
    resolution:
      {
        integrity: sha512-1WUsZ9R1lA0HtBSohTkm39WTPlNKSJ5iFk7UwqXkBLoHQT+hfqPsfsTDVuZdKGaBwn7din9bS7SsnoAr943hvw==,
      }
    engines: { node: ">=10" }

  istanbul-lib-report@3.0.1:
    resolution:
      {
        integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==,
      }
    engines: { node: ">=10" }

  istanbul-lib-source-maps@4.0.1:
    resolution:
      {
        integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==,
      }
    engines: { node: ">=10" }

  istanbul-reports@3.1.7:
    resolution:
      {
        integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==,
      }
    engines: { node: ">=8" }

  jackspeak@3.4.0:
    resolution:
      {
        integrity: sha512-JVYhQnN59LVPFCEcVa2C3CrEKYacvjRfqIQl+h8oi91aLYQVWRYbxjPcv1bUiUy/kLmQaANrYfNMCO3kuEDHfw==,
      }
    engines: { node: ">=14" }

  jest-changed-files@29.7.0:
    resolution:
      {
        integrity: sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-circus@29.7.0:
    resolution:
      {
        integrity: sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-cli@29.7.0:
    resolution:
      {
        integrity: sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jest-config@29.7.0:
    resolution:
      {
        integrity: sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    peerDependencies:
      "@types/node": "*"
      ts-node: ">=9.0.0"
    peerDependenciesMeta:
      "@types/node":
        optional: true
      ts-node:
        optional: true

  jest-diff@29.7.0:
    resolution:
      {
        integrity: sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-docblock@29.7.0:
    resolution:
      {
        integrity: sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-each@29.7.0:
    resolution:
      {
        integrity: sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-environment-node@29.7.0:
    resolution:
      {
        integrity: sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-get-type@29.6.3:
    resolution:
      {
        integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-haste-map@29.7.0:
    resolution:
      {
        integrity: sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-leak-detector@29.7.0:
    resolution:
      {
        integrity: sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-matcher-utils@29.7.0:
    resolution:
      {
        integrity: sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-message-util@29.7.0:
    resolution:
      {
        integrity: sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-mock@29.7.0:
    resolution:
      {
        integrity: sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-pnp-resolver@1.2.3:
    resolution:
      {
        integrity: sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==,
      }
    engines: { node: ">=6" }
    peerDependencies:
      jest-resolve: "*"
    peerDependenciesMeta:
      jest-resolve:
        optional: true

  jest-regex-util@29.6.3:
    resolution:
      {
        integrity: sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-resolve-dependencies@29.7.0:
    resolution:
      {
        integrity: sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-resolve@29.7.0:
    resolution:
      {
        integrity: sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-runner@29.7.0:
    resolution:
      {
        integrity: sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-runtime@29.7.0:
    resolution:
      {
        integrity: sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-snapshot@29.7.0:
    resolution:
      {
        integrity: sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-util@29.7.0:
    resolution:
      {
        integrity: sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-validate@29.7.0:
    resolution:
      {
        integrity: sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-watcher@29.7.0:
    resolution:
      {
        integrity: sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest-worker@29.7.0:
    resolution:
      {
        integrity: sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  jest@29.7.0:
    resolution:
      {
        integrity: sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  joycon@3.1.1:
    resolution:
      {
        integrity: sha512-34wB/Y7MW7bzjKRjUKTa46I2Z7eV62Rkhva+KkopW7Qvv/OSWBqvkSY7vusOPrNuZcUG3tApvdVgNB8POj3SPw==,
      }
    engines: { node: ">=10" }

  js-base64@3.7.2:
    resolution:
      {
        integrity: sha512-NnRs6dsyqUXejqk/yv2aiXlAvOs56sLkX6nUdeaNezI5LFFLlsZjOThmwnrcwh5ZZRwZlCMnVAY3CvhIhoVEKQ==,
      }

  js-tokens@4.0.0:
    resolution:
      {
        integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==,
      }

  js-yaml@3.14.1:
    resolution:
      {
        integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==,
      }
    hasBin: true

  js-yaml@4.1.0:
    resolution:
      {
        integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==,
      }
    hasBin: true

  jsesc@2.5.2:
    resolution:
      {
        integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==,
      }
    engines: { node: ">=4" }
    hasBin: true

  json-parse-better-errors@1.0.2:
    resolution:
      {
        integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==,
      }

  json-parse-even-better-errors@2.3.1:
    resolution:
      {
        integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==,
      }

  json-schema-traverse@1.0.0:
    resolution:
      {
        integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==,
      }

  json5@2.2.3:
    resolution:
      {
        integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==,
      }
    engines: { node: ">=6" }
    hasBin: true

  kind-of@6.0.3:
    resolution:
      {
        integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==,
      }
    engines: { node: ">=0.10.0" }

  kleur@3.0.3:
    resolution:
      {
        integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==,
      }
    engines: { node: ">=6" }

  lazystream@1.0.1:
    resolution:
      {
        integrity: sha512-b94GiNHQNy6JNTrt5w6zNyffMrNkXZb3KTkCZJb2V1xaEGCk093vkZ2jk3tpaeP33/OiXC+WvK9AxUebnf5nbw==,
      }
    engines: { node: ">= 0.6.3" }

  leven@3.1.0:
    resolution:
      {
        integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==,
      }
    engines: { node: ">=6" }

  lilconfig@3.1.2:
    resolution:
      {
        integrity: sha512-eop+wDAvpItUys0FWkHIKeC9ybYrTGbU41U5K7+bttZZeohvnY7M9dZ5kB21GNWiFT2q1OoPTvncPCgSOVO5ow==,
      }
    engines: { node: ">=14" }

  lines-and-columns@1.2.4:
    resolution:
      {
        integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==,
      }

  load-json-file@4.0.0:
    resolution:
      {
        integrity: sha512-Kx8hMakjX03tiGTLAIdJ+lL0htKnXjEZN6hk/tozf/WOuYGdZBJrZ+rCJRbVCugsjB3jMLn9746NsQIf5VjBMw==,
      }
    engines: { node: ">=4" }

  load-tsconfig@0.2.5:
    resolution:
      {
        integrity: sha512-IXO6OCs9yg8tMKzfPZ1YmheJbZCiEsnBdcB03l0OcfK9prKnJb96siuHCr5Fl37/yo9DnKU+TLpxzTUspw9shg==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  locate-path@5.0.0:
    resolution:
      {
        integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==,
      }
    engines: { node: ">=8" }

  lodash.defaults@4.2.0:
    resolution:
      {
        integrity: sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ==,
      }

  lodash.difference@4.5.0:
    resolution:
      {
        integrity: sha512-dS2j+W26TQ7taQBGN8Lbbq04ssV3emRw4NY58WErlTO29pIqS0HmoT5aJ9+TUQ1N3G+JOZSji4eugsWwGp9yPA==,
      }

  lodash.flatten@4.4.0:
    resolution:
      {
        integrity: sha512-C5N2Z3DgnnKr0LOpv/hKCgKdb7ZZwafIrsesve6lmzvZIRZRGaZ/l6Q8+2W7NaT+ZwO3fFlSCzCzrDCFdJfZ4g==,
      }

  lodash.isplainobject@4.0.6:
    resolution:
      {
        integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==,
      }

  lodash.memoize@4.1.2:
    resolution:
      {
        integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==,
      }

  lodash.union@4.6.0:
    resolution:
      {
        integrity: sha512-c4pB2CdGrGdjMKYLA+XiRDO7Y0PRQbm/Gzg8qMj+QH+pFVAoTp5sBpO0odL3FjoPCGjK96p6qsP+yQoiLoOBcw==,
      }

  lodash@4.17.21:
    resolution:
      {
        integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==,
      }

  log-symbols@4.1.0:
    resolution:
      {
        integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==,
      }
    engines: { node: ">=10" }

  long@4.0.0:
    resolution:
      {
        integrity: sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA==,
      }

  lower-case@2.0.2:
    resolution:
      {
        integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==,
      }

  lru-cache@10.2.2:
    resolution:
      {
        integrity: sha512-9hp3Vp2/hFQUiIwKo8XCeFVnrg8Pk3TYNPIR7tJADKi5YfcF7vEaK7avFHTlSy3kOKYaJQaalfEo6YuXdceBOQ==,
      }
    engines: { node: 14 || >=16.14 }

  lru-cache@5.1.1:
    resolution:
      {
        integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==,
      }

  lru-cache@6.0.0:
    resolution:
      {
        integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==,
      }
    engines: { node: ">=10" }

  make-dir@3.1.0:
    resolution:
      {
        integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==,
      }
    engines: { node: ">=8" }

  make-dir@4.0.0:
    resolution:
      {
        integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==,
      }
    engines: { node: ">=10" }

  make-error@1.3.6:
    resolution:
      {
        integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==,
      }

  makeerror@1.0.12:
    resolution:
      {
        integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==,
      }

  map-obj@1.0.1:
    resolution:
      {
        integrity: sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==,
      }
    engines: { node: ">=0.10.0" }

  map-obj@4.3.0:
    resolution:
      {
        integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==,
      }
    engines: { node: ">=8" }

  marked@12.0.2:
    resolution:
      {
        integrity: sha512-qXUm7e/YKFoqFPYPa3Ukg9xlI5cyAtGmyEIzMfW//m6kXwCy2Ps9DYf5ioijFKQ8qyuscrHoY04iJGctu2Kg0Q==,
      }
    engines: { node: ">= 18" }
    hasBin: true

  memorystream@0.3.1:
    resolution:
      {
        integrity: sha512-S3UwM3yj5mtUSEfP41UZmt/0SCoVYUcU1rkXv+BQ5Ig8ndL4sPoJNBUJERafdPb5jjHJGuMgytgKvKIf58XNBw==,
      }
    engines: { node: ">= 0.10.0" }

  meow@9.0.0:
    resolution:
      {
        integrity: sha512-+obSblOQmRhcyBt62furQqRAQpNyWXo8BuQ5bN7dG8wmwQ+vwHKp/rCFD4CrTP8CsDQD1sjoZ94K417XEUk8IQ==,
      }
    engines: { node: ">=10" }

  merge-stream@2.0.0:
    resolution:
      {
        integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==,
      }

  merge2@1.4.1:
    resolution:
      {
        integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==,
      }
    engines: { node: ">= 8" }

  micromatch@4.0.7:
    resolution:
      {
        integrity: sha512-LPP/3KorzCwBxfeUuZmaR6bG2kdeHSbe0P2tY3FLRU4vYrjYz5hI4QZwV0njUx3jeuKe67YukQ1LSPZBKDqO/Q==,
      }
    engines: { node: ">=8.6" }

  mime-db@1.52.0:
    resolution:
      {
        integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==,
      }
    engines: { node: ">= 0.6" }

  mime-types@2.1.35:
    resolution:
      {
        integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==,
      }
    engines: { node: ">= 0.6" }

  mimic-fn@2.1.0:
    resolution:
      {
        integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==,
      }
    engines: { node: ">=6" }

  mimic-response@3.1.0:
    resolution:
      {
        integrity: sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==,
      }
    engines: { node: ">=10" }

  min-indent@1.0.1:
    resolution:
      {
        integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==,
      }
    engines: { node: ">=4" }

  minimatch@3.1.2:
    resolution:
      {
        integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==,
      }

  minimatch@5.1.6:
    resolution:
      {
        integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==,
      }
    engines: { node: ">=10" }

  minimatch@9.0.4:
    resolution:
      {
        integrity: sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw==,
      }
    engines: { node: ">=16 || 14 >=14.17" }

  minimist-options@4.1.0:
    resolution:
      {
        integrity: sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==,
      }
    engines: { node: ">= 6" }

  minimist@1.2.8:
    resolution:
      {
        integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==,
      }

  minipass@3.3.6:
    resolution:
      {
        integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==,
      }
    engines: { node: ">=8" }

  minipass@5.0.0:
    resolution:
      {
        integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==,
      }
    engines: { node: ">=8" }

  minipass@7.1.2:
    resolution:
      {
        integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==,
      }
    engines: { node: ">=16 || 14 >=14.17" }

  minizlib@2.1.2:
    resolution:
      {
        integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==,
      }
    engines: { node: ">= 8" }

  mkdirp-classic@0.5.3:
    resolution:
      {
        integrity: sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==,
      }

  mkdirp@1.0.4:
    resolution:
      {
        integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==,
      }
    engines: { node: ">=10" }
    hasBin: true

  ms@2.1.2:
    resolution:
      {
        integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==,
      }

  ms@2.1.3:
    resolution:
      {
        integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==,
      }

  mz@2.7.0:
    resolution:
      {
        integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==,
      }

  nan@2.20.0:
    resolution:
      {
        integrity: sha512-bk3gXBZDGILuuo/6sKtr0DQmSThYHLtNCdSdXk9YkxD/jK6X2vmCyyXBBxyqZ4XcnzTyYEAThfX3DCEnLf6igw==,
      }

  nanoid@3.3.8:
    resolution:
      {
        integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==,
      }
    engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
    hasBin: true

  napi-build-utils@2.0.0:
    resolution:
      {
        integrity: sha512-GEbrYkbfF7MoNaoh2iGG84Mnf/WZfB0GdGEsM8wz7Expx/LlWf5U8t9nvJKXSp3qr5IsEbK04cBGhol/KwOsWA==,
      }

  natural-compare@1.4.0:
    resolution:
      {
        integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==,
      }

  neo-async@2.6.2:
    resolution:
      {
        integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==,
      }

  nice-try@1.0.5:
    resolution:
      {
        integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==,
      }

  no-case@3.0.4:
    resolution:
      {
        integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==,
      }

  node-abi@3.74.0:
    resolution:
      {
        integrity: sha512-c5XK0MjkGBrQPGYG24GBADZud0NCbznxNx0ZkS+ebUTrmV1qTDxPxSL8zEAPURXSbLRWVexxmP4986BziahL5w==,
      }
    engines: { node: ">=10" }

  node-addon-api@5.1.0:
    resolution:
      {
        integrity: sha512-eh0GgfEkpnoWDq+VY8OyvYhFEzBk6jIYbRKdIlyTiAXIVJ8PyBaKb0rp7oDtoddbdoHWhq8wwr+XZ81F1rpNdA==,
      }

  node-addon-api@6.1.0:
    resolution:
      {
        integrity: sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA==,
      }

  node-domexception@1.0.0:
    resolution:
      {
        integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==,
      }
    engines: { node: ">=10.5.0" }

  node-fetch@2.7.0:
    resolution:
      {
        integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==,
      }
    engines: { node: 4.x || >=6.0.0 }
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-int64@0.4.0:
    resolution:
      {
        integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==,
      }

  node-releases@2.0.14:
    resolution:
      {
        integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==,
      }

  node-watch@0.7.4:
    resolution:
      {
        integrity: sha512-RinNxoz4W1cep1b928fuFhvAQ5ag/+1UlMDV7rbyGthBIgsiEouS4kvRayvvboxii4m8eolKOIBo3OjDqbc+uQ==,
      }
    engines: { node: ">=6" }

  nopt@5.0.0:
    resolution:
      {
        integrity: sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==,
      }
    engines: { node: ">=6" }
    hasBin: true

  normalize-package-data@2.5.0:
    resolution:
      {
        integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==,
      }

  normalize-package-data@3.0.3:
    resolution:
      {
        integrity: sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==,
      }
    engines: { node: ">=10" }

  normalize-path@3.0.0:
    resolution:
      {
        integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==,
      }
    engines: { node: ">=0.10.0" }

  npm-run-all@4.1.5:
    resolution:
      {
        integrity: sha512-Oo82gJDAVcaMdi3nuoKFavkIHBRVqQ1qvMb+9LHk/cF4P6B2m8aP04hGf7oL6wZ9BuGwX1onlLhpuoofSyoQDQ==,
      }
    engines: { node: ">= 4" }
    hasBin: true

  npm-run-path@4.0.1:
    resolution:
      {
        integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==,
      }
    engines: { node: ">=8" }

  npmlog@5.0.1:
    resolution:
      {
        integrity: sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==,
      }
    deprecated: This package is no longer supported.

  object-assign@4.1.1:
    resolution:
      {
        integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==,
      }
    engines: { node: ">=0.10.0" }

  object-inspect@1.13.1:
    resolution:
      {
        integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==,
      }

  object-keys@1.1.1:
    resolution:
      {
        integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==,
      }
    engines: { node: ">= 0.4" }

  object.assign@4.1.5:
    resolution:
      {
        integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==,
      }
    engines: { node: ">= 0.4" }

  ollama@0.5.12:
    resolution:
      {
        integrity: sha512-flVH1fn1c9NF7VV3bW9kSu0E+bYc40b4DxL/gS2Debhao35osJFRDiPOj9sIWTMvcyj78Paw1OuhfIe7uhDWfQ==,
      }

  once@1.4.0:
    resolution:
      {
        integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==,
      }

  onetime@5.1.2:
    resolution:
      {
        integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==,
      }
    engines: { node: ">=6" }

  onnx-proto@4.0.4:
    resolution:
      {
        integrity: sha512-aldMOB3HRoo6q/phyB6QRQxSt895HNNw82BNyZ2CMh4bjeKv7g/c+VpAFtJuEMVfYLMbRx61hbuqnKceLeDcDA==,
      }

  onnxruntime-common@1.14.0:
    resolution:
      {
        integrity: sha512-3LJpegM2iMNRX2wUmtYfeX/ytfOzNwAWKSq1HbRrKc9+uqG/FsEA0bbKZl1btQeZaXhC26l44NWpNUeXPII7Ew==,
      }

  onnxruntime-node@1.14.0:
    resolution:
      {
        integrity: sha512-5ba7TWomIV/9b6NH/1x/8QEeowsb+jBEvFzU6z0T4mNsFwdPqXeFUM7uxC6QeSRkEbWu3qEB0VMjrvzN/0S9+w==,
      }
    os: [win32, darwin, linux]

  onnxruntime-web@1.14.0:
    resolution:
      {
        integrity: sha512-Kcqf43UMfW8mCydVGcX9OMXI2VN17c0p6XvR7IPSZzBf/6lteBzXHvcEVWDPmCKuGombl997HgLqj91F11DzXw==,
      }

  openai@4.51.0:
    resolution:
      {
        integrity: sha512-UKuWc3/qQyklqhHM8CbdXCv0Z0obap6T0ECdcO5oATQxAbKE5Ky3YCXFQY207z+eGG6ez4U9wvAcuMygxhmStg==,
      }
    hasBin: true

  openapi-generator-plus@2.20.0:
    resolution:
      {
        integrity: sha512-WNh+6FPABZG7MzRQ51GwFv4N7A3aB31ZDnNofWzCdF0agB9ikZLQCeV6o9B3chObpWKiSP/qW7yLZYohbOVE6w==,
      }
    hasBin: true
    peerDependencies:
      "@openapi-generator-plus/core": ">=2"

  openapi-types@12.1.3:
    resolution:
      {
        integrity: sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==,
      }

  p-limit@2.3.0:
    resolution:
      {
        integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==,
      }
    engines: { node: ">=6" }

  p-limit@3.1.0:
    resolution:
      {
        integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==,
      }
    engines: { node: ">=10" }

  p-locate@4.1.0:
    resolution:
      {
        integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==,
      }
    engines: { node: ">=8" }

  p-try@2.2.0:
    resolution:
      {
        integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==,
      }
    engines: { node: ">=6" }

  param-case@3.0.4:
    resolution:
      {
        integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==,
      }

  parse-json@4.0.0:
    resolution:
      {
        integrity: sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==,
      }
    engines: { node: ">=4" }

  parse-json@5.2.0:
    resolution:
      {
        integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==,
      }
    engines: { node: ">=8" }

  pascal-case@3.1.2:
    resolution:
      {
        integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==,
      }

  path-case@3.0.4:
    resolution:
      {
        integrity: sha512-qO4qCFjXqVTrcbPt/hQfhTQ+VhFsqNKOPtytgNKkKxSoEp3XPUQ8ObFuePylOIok5gjn69ry8XiULxCwot3Wfg==,
      }

  path-exists@4.0.0:
    resolution:
      {
        integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==,
      }
    engines: { node: ">=8" }

  path-is-absolute@1.0.1:
    resolution:
      {
        integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==,
      }
    engines: { node: ">=0.10.0" }

  path-key@2.0.1:
    resolution:
      {
        integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==,
      }
    engines: { node: ">=4" }

  path-key@3.1.1:
    resolution:
      {
        integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==,
      }
    engines: { node: ">=8" }

  path-parse@1.0.7:
    resolution:
      {
        integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==,
      }

  path-scurry@1.11.1:
    resolution:
      {
        integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==,
      }
    engines: { node: ">=16 || 14 >=14.18" }

  path-type@3.0.0:
    resolution:
      {
        integrity: sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==,
      }
    engines: { node: ">=4" }

  path-type@4.0.0:
    resolution:
      {
        integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==,
      }
    engines: { node: ">=8" }

  picocolors@1.0.1:
    resolution:
      {
        integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==,
      }

  picocolors@1.1.1:
    resolution:
      {
        integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==,
      }

  picomatch@2.3.1:
    resolution:
      {
        integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==,
      }
    engines: { node: ">=8.6" }

  pidtree@0.3.1:
    resolution:
      {
        integrity: sha512-qQbW94hLHEqCg7nhby4yRC7G2+jYHY4Rguc2bjw7Uug4GIJuu1tvf2uHaZv5Q8zdt+WKJ6qK1FOI6amaWUo5FA==,
      }
    engines: { node: ">=0.10" }
    hasBin: true

  pify@3.0.0:
    resolution:
      {
        integrity: sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==,
      }
    engines: { node: ">=4" }

  pirates@4.0.6:
    resolution:
      {
        integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==,
      }
    engines: { node: ">= 6" }

  pkg-dir@4.2.0:
    resolution:
      {
        integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==,
      }
    engines: { node: ">=8" }

  platform@1.3.6:
    resolution:
      {
        integrity: sha512-fnWVljUchTro6RiCFvCXBbNhJc2NijN7oIQxbwsyL0buWJPG85v81ehlHI9fXrJsMNgTofEoWIQeClKpgxFLrg==,
      }

  plur@4.0.0:
    resolution:
      {
        integrity: sha512-4UGewrYgqDFw9vV6zNV+ADmPAUAfJPKtGvb/VdpQAx25X5f3xXdGdyOEVFwkl8Hl/tl7+xbeHqSEM+D5/TirUg==,
      }
    engines: { node: ">=10" }

  pluralize@8.0.0:
    resolution:
      {
        integrity: sha512-Nc3IT5yHzflTfbjgqWcCPpo7DaKy4FnpB0l/zCAW0Tc7jxAiuqSxHasntB3D7887LSrA93kDJ9IXovxJYxyLCA==,
      }
    engines: { node: ">=4" }

  possible-typed-array-names@1.0.0:
    resolution:
      {
        integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==,
      }
    engines: { node: ">= 0.4" }

  postcss-load-config@4.0.2:
    resolution:
      {
        integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==,
      }
    engines: { node: ">= 14" }
    peerDependencies:
      postcss: ">=8.0.9"
      ts-node: ">=9.0.0"
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss@8.5.3:
    resolution:
      {
        integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==,
      }
    engines: { node: ^10 || ^12 || >=14 }

  prebuild-install@7.1.3:
    resolution:
      {
        integrity: sha512-8Mf2cbV7x1cXPUILADGI3wuhfqWvtiLA1iclTDbFRZkgRQS0NqsPZphna9V+HyTEadheuPmjaJMsbzKQFOzLug==,
      }
    engines: { node: ">=10" }
    hasBin: true

  prettier@2.8.7:
    resolution:
      {
        integrity: sha512-yPngTo3aXUUmyuTjeTUT75txrf+aMh9FiD7q9ZE/i6r0bPb22g4FsE6Y338PQX1bmfy08i9QQCB7/rcUAVntfw==,
      }
    engines: { node: ">=10.13.0" }
    hasBin: true

  pretty-format@29.7.0:
    resolution:
      {
        integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || >=18.0.0 }

  process-nextick-args@2.0.1:
    resolution:
      {
        integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==,
      }

  process@0.11.10:
    resolution:
      {
        integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==,
      }
    engines: { node: ">= 0.6.0" }

  prompts@2.4.2:
    resolution:
      {
        integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==,
      }
    engines: { node: ">= 6" }

  proper-lockfile@4.1.2:
    resolution:
      {
        integrity: sha512-TjNPblN4BwAWMXU8s9AEz4JmQxnD1NNL7bNOY/AKUzyamc379FWASUhc/K1pL2noVb+XmZKLL68cjzLsiOAMaA==,
      }

  properties-reader@2.3.0:
    resolution:
      {
        integrity: sha512-z597WicA7nDZxK12kZqHr2TcvwNU1GCfA5UwfDY/HDp3hXPoPlb5rlEx9bwGTiJnc0OqbBTkU975jDToth8Gxw==,
      }
    engines: { node: ">=14" }

  protobufjs@6.11.4:
    resolution:
      {
        integrity: sha512-5kQWPaJHi1WoCpjTGszzQ32PG2F4+wRY6BmAT4Vfw56Q2FZ4YZzK20xUYQH4YkfehY1e6QSICrJquM6xXZNcrw==,
      }
    hasBin: true

  pump@3.0.0:
    resolution:
      {
        integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==,
      }

  punycode@2.3.1:
    resolution:
      {
        integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==,
      }
    engines: { node: ">=6" }

  pure-rand@6.1.0:
    resolution:
      {
        integrity: sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==,
      }

  qs@6.11.2:
    resolution:
      {
        integrity: sha512-tDNIz22aBzCDxLtVH++VnTfzxlfeK5CbqohpSqpJgj1Wg/cQbStNAz3NuqCs5vV+pjBsK4x4pN9HlVh7rcYRiA==,
      }
    engines: { node: ">=0.6" }

  querystringify@2.2.0:
    resolution:
      {
        integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==,
      }

  queue-microtask@1.2.3:
    resolution:
      {
        integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==,
      }

  queue-tick@1.0.1:
    resolution:
      {
        integrity: sha512-kJt5qhMxoszgU/62PLP1CJytzd2NKetjSRnyuj31fDd3Rlcz3fzlFdFLD1SItunPwyqEOkca6GbV612BWfaBag==,
      }

  quick-lru@4.0.1:
    resolution:
      {
        integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==,
      }
    engines: { node: ">=8" }

  rc@1.2.8:
    resolution:
      {
        integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==,
      }
    hasBin: true

  react-is@18.3.1:
    resolution:
      {
        integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==,
      }

  read-pkg-up@7.0.1:
    resolution:
      {
        integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==,
      }
    engines: { node: ">=8" }

  read-pkg@3.0.0:
    resolution:
      {
        integrity: sha512-BLq/cCO9two+lBgiTYNqD6GdtK8s4NpaWrl6/rCO9w0TUS8oJl7cmToOZfRYllKTISY6nt1U7jQ53brmKqY6BA==,
      }
    engines: { node: ">=4" }

  read-pkg@5.2.0:
    resolution:
      {
        integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==,
      }
    engines: { node: ">=8" }

  readable-stream@2.3.8:
    resolution:
      {
        integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==,
      }

  readable-stream@3.6.2:
    resolution:
      {
        integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==,
      }
    engines: { node: ">= 6" }

  readable-stream@4.5.2:
    resolution:
      {
        integrity: sha512-yjavECdqeZ3GLXNgRXgeQEdz9fvDDkNKyHnbHRFtOr7/LcfgBcmct7t/ET+HaCTqfh06OzoAxrkN/IfjJBVe+g==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  readdir-glob@1.1.3:
    resolution:
      {
        integrity: sha512-v05I2k7xN8zXvPD9N+z/uhXPaj0sUFCe2rcWZIpBsqxfP7xXFQ0tipAd/wjj1YxWyWtUS5IDJpOG82JKt2EAVA==,
      }

  readdirp@3.6.0:
    resolution:
      {
        integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==,
      }
    engines: { node: ">=8.10.0" }

  redent@3.0.0:
    resolution:
      {
        integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==,
      }
    engines: { node: ">=8" }

  regexp.prototype.flags@1.5.2:
    resolution:
      {
        integrity: sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==,
      }
    engines: { node: ">= 0.4" }

  require-directory@2.1.1:
    resolution:
      {
        integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==,
      }
    engines: { node: ">=0.10.0" }

  require-from-string@2.0.2:
    resolution:
      {
        integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==,
      }
    engines: { node: ">=0.10.0" }

  requires-port@1.0.0:
    resolution:
      {
        integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==,
      }

  resolve-cwd@3.0.0:
    resolution:
      {
        integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==,
      }
    engines: { node: ">=8" }

  resolve-from@5.0.0:
    resolution:
      {
        integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==,
      }
    engines: { node: ">=8" }

  resolve.exports@2.0.2:
    resolution:
      {
        integrity: sha512-X2UW6Nw3n/aMgDVy+0rSqgHlv39WZAlZrXCdnbyEiKm17DSqHX4MmQMaST3FbeWR5FTuRcUwYAziZajji0Y7mg==,
      }
    engines: { node: ">=10" }

  resolve@1.22.8:
    resolution:
      {
        integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==,
      }
    hasBin: true

  retry@0.12.0:
    resolution:
      {
        integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==,
      }
    engines: { node: ">= 4" }

  reusify@1.0.4:
    resolution:
      {
        integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==,
      }
    engines: { iojs: ">=1.0.0", node: ">=0.10.0" }

  rimraf@3.0.2:
    resolution:
      {
        integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==,
      }
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@5.0.7:
    resolution:
      {
        integrity: sha512-nV6YcJo5wbLW77m+8KjH8aB/7/rxQy9SZ0HY5shnwULfS+9nmTtVXAJET5NdZmCzA4fPI/Hm1wo/Po/4mopOdg==,
      }
    engines: { node: ">=14.18" }
    hasBin: true

  rollup@4.18.0:
    resolution:
      {
        integrity: sha512-QmJz14PX3rzbJCN1SG4Xe/bAAX2a6NpCP8ab2vfu2GiUr8AQcr2nCV/oEO3yneFarB67zk8ShlIyWb2LGTb3Sg==,
      }
    engines: { node: ">=18.0.0", npm: ">=8.0.0" }
    hasBin: true

  run-parallel@1.2.0:
    resolution:
      {
        integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==,
      }

  safe-array-concat@1.1.2:
    resolution:
      {
        integrity: sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==,
      }
    engines: { node: ">=0.4" }

  safe-buffer@5.1.2:
    resolution:
      {
        integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==,
      }

  safe-buffer@5.2.1:
    resolution:
      {
        integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==,
      }

  safe-regex-test@1.0.3:
    resolution:
      {
        integrity: sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==,
      }
    engines: { node: ">= 0.4" }

  safer-buffer@2.1.2:
    resolution:
      {
        integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==,
      }

  semver@5.7.2:
    resolution:
      {
        integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==,
      }
    hasBin: true

  semver@6.3.1:
    resolution:
      {
        integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==,
      }
    hasBin: true

  semver@7.7.1:
    resolution:
      {
        integrity: sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==,
      }
    engines: { node: ">=10" }
    hasBin: true

  sentence-case@3.0.4:
    resolution:
      {
        integrity: sha512-8LS0JInaQMCRoQ7YUytAo/xUu5W2XnQxV2HI/6uM6U7CITS1RqPElr30V6uIqyMKM9lJGRVFy5/4CuzcixNYSg==,
      }

  set-blocking@2.0.0:
    resolution:
      {
        integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==,
      }

  set-function-length@1.2.2:
    resolution:
      {
        integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==,
      }
    engines: { node: ">= 0.4" }

  set-function-name@2.0.2:
    resolution:
      {
        integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==,
      }
    engines: { node: ">= 0.4" }

  sharp@0.32.6:
    resolution:
      {
        integrity: sha512-KyLTWwgcR9Oe4d9HwCwNM2l7+J0dUQwn/yf7S0EnTtb0eVS4RxO0eUSvxPtzT4F3SY+C4K6fqdv/DO27sJ/v/w==,
      }
    engines: { node: ">=14.15.0" }

  shebang-command@1.2.0:
    resolution:
      {
        integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==,
      }
    engines: { node: ">=0.10.0" }

  shebang-command@2.0.0:
    resolution:
      {
        integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==,
      }
    engines: { node: ">=8" }

  shebang-regex@1.0.0:
    resolution:
      {
        integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==,
      }
    engines: { node: ">=0.10.0" }

  shebang-regex@3.0.0:
    resolution:
      {
        integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==,
      }
    engines: { node: ">=8" }

  shell-quote@1.8.1:
    resolution:
      {
        integrity: sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==,
      }

  side-channel@1.0.6:
    resolution:
      {
        integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==,
      }
    engines: { node: ">= 0.4" }

  signal-exit@3.0.7:
    resolution:
      {
        integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==,
      }

  signal-exit@4.1.0:
    resolution:
      {
        integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==,
      }
    engines: { node: ">=14" }

  simple-concat@1.0.1:
    resolution:
      {
        integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==,
      }

  simple-get@4.0.1:
    resolution:
      {
        integrity: sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==,
      }

  simple-swizzle@0.2.2:
    resolution:
      {
        integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==,
      }

  sisteransi@1.0.5:
    resolution:
      {
        integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==,
      }

  slash@3.0.0:
    resolution:
      {
        integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==,
      }
    engines: { node: ">=8" }

  snake-case@3.0.4:
    resolution:
      {
        integrity: sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==,
      }

  source-map-js@1.2.1:
    resolution:
      {
        integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==,
      }
    engines: { node: ">=0.10.0" }

  source-map-support@0.5.13:
    resolution:
      {
        integrity: sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==,
      }

  source-map@0.6.1:
    resolution:
      {
        integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==,
      }
    engines: { node: ">=0.10.0" }

  source-map@0.8.0-beta.0:
    resolution:
      {
        integrity: sha512-2ymg6oRBpebeZi9UUNsgQ89bhx01TcTkmNTGnNO88imTmbSgy4nfujrgVEFKWpMTEGA11EDkTt7mqObTPdigIA==,
      }
    engines: { node: ">= 8" }

  spdx-correct@3.2.0:
    resolution:
      {
        integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==,
      }

  spdx-exceptions@2.5.0:
    resolution:
      {
        integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==,
      }

  spdx-expression-parse@3.0.1:
    resolution:
      {
        integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==,
      }

  spdx-license-ids@3.0.18:
    resolution:
      {
        integrity: sha512-xxRs31BqRYHwiMzudOrpSiHtZ8i/GeionCBDSilhYRj+9gIcI8wCZTlXZKu9vZIVqViP3dcp9qE5G6AlIaD+TQ==,
      }

  split-ca@1.0.1:
    resolution:
      {
        integrity: sha512-Q5thBSxp5t8WPTTJQS59LrGqOZqOsrhDGDVm8azCqIBjSBd7nd9o2PM+mDulQQkh8h//4U6hFZnc/mul8t5pWQ==,
      }

  sprintf-js@1.0.3:
    resolution:
      {
        integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==,
      }

  ssh-remote-port-forward@1.0.4:
    resolution:
      {
        integrity: sha512-x0LV1eVDwjf1gmG7TTnfqIzf+3VPRz7vrNIjX6oYLbeCrf/PeVY6hkT68Mg+q02qXxQhrLjB0jfgvhevoCRmLQ==,
      }

  ssh2@1.15.0:
    resolution:
      {
        integrity: sha512-C0PHgX4h6lBxYx7hcXwu3QWdh4tg6tZZsTfXcdvc5caW/EMxaB4H9dWsl7qk+F7LAW762hp8VbXOX7x4xUYvEw==,
      }
    engines: { node: ">=10.16.0" }

  stack-utils@2.0.6:
    resolution:
      {
        integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==,
      }
    engines: { node: ">=10" }

  streamx@2.18.0:
    resolution:
      {
        integrity: sha512-LLUC1TWdjVdn1weXGcSxyTR3T4+acB6tVGXT95y0nGbca4t4o/ng1wKAGTljm9VicuCVLvRlqFYXYy5GwgM7sQ==,
      }

  string-length@4.0.2:
    resolution:
      {
        integrity: sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==,
      }
    engines: { node: ">=10" }

  string-width@4.2.3:
    resolution:
      {
        integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==,
      }
    engines: { node: ">=8" }

  string-width@5.1.2:
    resolution:
      {
        integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==,
      }
    engines: { node: ">=12" }

  string.prototype.padend@3.1.6:
    resolution:
      {
        integrity: sha512-XZpspuSB7vJWhvJc9DLSlrXl1mcA2BdoY5jjnS135ydXqLoqhs96JjDtCkjJEQHvfqZIp9hBuBMgI589peyx9Q==,
      }
    engines: { node: ">= 0.4" }

  string.prototype.trim@1.2.9:
    resolution:
      {
        integrity: sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==,
      }
    engines: { node: ">= 0.4" }

  string.prototype.trimend@1.0.8:
    resolution:
      {
        integrity: sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==,
      }

  string.prototype.trimstart@1.0.8:
    resolution:
      {
        integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==,
      }
    engines: { node: ">= 0.4" }

  string_decoder@1.1.1:
    resolution:
      {
        integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==,
      }

  string_decoder@1.3.0:
    resolution:
      {
        integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==,
      }

  strip-ansi@6.0.1:
    resolution:
      {
        integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==,
      }
    engines: { node: ">=8" }

  strip-ansi@7.1.0:
    resolution:
      {
        integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==,
      }
    engines: { node: ">=12" }

  strip-bom@3.0.0:
    resolution:
      {
        integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==,
      }
    engines: { node: ">=4" }

  strip-bom@4.0.0:
    resolution:
      {
        integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==,
      }
    engines: { node: ">=8" }

  strip-final-newline@2.0.0:
    resolution:
      {
        integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==,
      }
    engines: { node: ">=6" }

  strip-indent@3.0.0:
    resolution:
      {
        integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==,
      }
    engines: { node: ">=8" }

  strip-json-comments@2.0.1:
    resolution:
      {
        integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==,
      }
    engines: { node: ">=0.10.0" }

  strip-json-comments@3.1.1:
    resolution:
      {
        integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==,
      }
    engines: { node: ">=8" }

  strnum@1.0.5:
    resolution:
      {
        integrity: sha512-J8bbNyKKXl5qYcR36TIO8W3mVGVHrmmxsd5PAItGkmyzwJvybiw2IVq5nqd0i4LSNSkB/sx9VHllbfFdr9k1JA==,
      }

  sucrase@3.35.0:
    resolution:
      {
        integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==,
      }
    engines: { node: ">=16 || 14 >=14.17" }
    hasBin: true

  supports-color@5.5.0:
    resolution:
      {
        integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==,
      }
    engines: { node: ">=4" }

  supports-color@7.2.0:
    resolution:
      {
        integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==,
      }
    engines: { node: ">=8" }

  supports-color@8.1.1:
    resolution:
      {
        integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==,
      }
    engines: { node: ">=10" }

  supports-hyperlinks@2.3.0:
    resolution:
      {
        integrity: sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==,
      }
    engines: { node: ">=8" }

  supports-preserve-symlinks-flag@1.0.0:
    resolution:
      {
        integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==,
      }
    engines: { node: ">= 0.4" }

  tar-fs@2.0.1:
    resolution:
      {
        integrity: sha512-6tzWDMeroL87uF/+lin46k+Q+46rAJ0SyPGz7OW7wTgblI273hsBqk2C1j0/xNadNLKDTUL9BukSjB7cwgmlPA==,
      }

  tar-fs@3.0.6:
    resolution:
      {
        integrity: sha512-iokBDQQkUyeXhgPYaZxmczGPhnhXZ0CmrqI+MOb/WFGS9DW5wnfrLgtjUJBvz50vQ3qfRwJ62QVoCFu8mPVu5w==,
      }

  tar-stream@2.2.0:
    resolution:
      {
        integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==,
      }
    engines: { node: ">=6" }

  tar-stream@3.1.7:
    resolution:
      {
        integrity: sha512-qJj60CXt7IU1Ffyc3NJMjh6EkuCFej46zUqJ4J7pqYlThyd9bO0XBTmcOIhSzZJVWfsLks0+nle/j538YAW9RQ==,
      }

  tar@6.2.1:
    resolution:
      {
        integrity: sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==,
      }
    engines: { node: ">=10" }

  test-exclude@6.0.0:
    resolution:
      {
        integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==,
      }
    engines: { node: ">=8" }

  testcontainers@10.10.3:
    resolution:
      {
        integrity: sha512-QuHKgGbMo+rM+AvrHNzQFAu8/D37Od1sQCW8lNR5+KvGM82mDJndTkpPXiUaFpVIZ99wNQfhZbZwSTBULerUiQ==,
      }

  text-decoder@1.1.1:
    resolution:
      {
        integrity: sha512-8zll7REEv4GDD3x4/0pW+ppIxSNs7H1J10IKFZsuOMscumCdM2a+toDGLPA3T+1+fLBql4zbt5z83GEQGGV5VA==,
      }

  thenify-all@1.6.0:
    resolution:
      {
        integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==,
      }
    engines: { node: ">=0.8" }

  thenify@3.3.1:
    resolution:
      {
        integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==,
      }

  tmp@0.2.3:
    resolution:
      {
        integrity: sha512-nZD7m9iCPC5g0pYmcaxogYKggSfLsdxl8of3Q/oIbqCqLLIO9IAF0GWjX1z9NZRHPiXv8Wex4yDCaZsgEw0Y8w==,
      }
    engines: { node: ">=14.14" }

  tmpl@1.0.5:
    resolution:
      {
        integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==,
      }

  to-fast-properties@2.0.0:
    resolution:
      {
        integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==,
      }
    engines: { node: ">=4" }

  to-regex-range@5.0.1:
    resolution:
      {
        integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==,
      }
    engines: { node: ">=8.0" }

  tr46@4.1.1:
    resolution:
      {
        integrity: sha512-2lv/66T7e5yNyhAAC4NaKe5nVavzuGJQVVtRYLyQ2OI8tsJ61PMLlelehb0wi2Hx6+hT/OJUWZcw8MjlSRnxvw==,
      }
    engines: { node: ">=14" }

  tree-kill@1.2.2:
    resolution:
      {
        integrity: sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==,
      }
    hasBin: true

  trim-newlines@3.0.1:
    resolution:
      {
        integrity: sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==,
      }
    engines: { node: ">=8" }

  ts-interface-checker@0.1.13:
    resolution:
      {
        integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==,
      }

  ts-jest@29.1.4:
    resolution:
      {
        integrity: sha512-YiHwDhSvCiItoAgsKtoLFCuakDzDsJ1DLDnSouTaTmdOcOwIkSzbLXduaQ6M5DRVhuZC/NYaaZ/mtHbWMv/S6Q==,
      }
    engines: { node: ^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0 }
    hasBin: true
    peerDependencies:
      "@babel/core": ">=7.0.0-beta.0 <8"
      "@jest/transform": ^29.0.0
      "@jest/types": ^29.0.0
      babel-jest: ^29.0.0
      esbuild: "*"
      jest: ^29.0.0
      typescript: ">=4.3 <6"
    peerDependenciesMeta:
      "@babel/core":
        optional: true
      "@jest/transform":
        optional: true
      "@jest/types":
        optional: true
      babel-jest:
        optional: true
      esbuild:
        optional: true

  ts-node@10.9.2:
    resolution:
      {
        integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==,
      }
    hasBin: true
    peerDependencies:
      "@swc/core": ">=1.2.50"
      "@swc/wasm": ">=1.2.50"
      "@types/node": "*"
      typescript: ">=2.7"
    peerDependenciesMeta:
      "@swc/core":
        optional: true
      "@swc/wasm":
        optional: true

  tsd@0.28.1:
    resolution:
      {
        integrity: sha512-FeYrfJ05QgEMW/qOukNCr4fAJHww4SaKnivAXRv4g5kj4FeLpNV7zH4dorzB9zAfVX4wmA7zWu/wQf7kkcvfbw==,
      }
    engines: { node: ">=14.16" }
    hasBin: true

  tslib@1.14.1:
    resolution:
      {
        integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==,
      }

  tslib@2.6.3:
    resolution:
      {
        integrity: sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==,
      }

  tsup@7.3.0:
    resolution:
      {
        integrity: sha512-Ja1eaSRrE+QarmATlNO5fse2aOACYMBX+IZRKy1T+gpyH+jXgRrl5l4nHIQJQ1DoDgEjHDTw8cpE085UdBZuWQ==,
      }
    engines: { node: ">=18" }
    deprecated: Breaking node 16
    hasBin: true
    peerDependencies:
      "@swc/core": ^1
      postcss: ^8.4.12
      typescript: ">=4.5.0"
    peerDependenciesMeta:
      "@swc/core":
        optional: true
      postcss:
        optional: true
      typescript:
        optional: true

  tunnel-agent@0.6.0:
    resolution:
      {
        integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==,
      }

  tweetnacl@0.14.5:
    resolution:
      {
        integrity: sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==,
      }

  type-detect@4.0.8:
    resolution:
      {
        integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==,
      }
    engines: { node: ">=4" }

  type-fest@0.18.1:
    resolution:
      {
        integrity: sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==,
      }
    engines: { node: ">=10" }

  type-fest@0.21.3:
    resolution:
      {
        integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==,
      }
    engines: { node: ">=10" }

  type-fest@0.6.0:
    resolution:
      {
        integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==,
      }
    engines: { node: ">=8" }

  type-fest@0.8.1:
    resolution:
      {
        integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==,
      }
    engines: { node: ">=8" }

  typed-array-buffer@1.0.2:
    resolution:
      {
        integrity: sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==,
      }
    engines: { node: ">= 0.4" }

  typed-array-byte-length@1.0.1:
    resolution:
      {
        integrity: sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==,
      }
    engines: { node: ">= 0.4" }

  typed-array-byte-offset@1.0.2:
    resolution:
      {
        integrity: sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA==,
      }
    engines: { node: ">= 0.4" }

  typed-array-length@1.0.6:
    resolution:
      {
        integrity: sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g==,
      }
    engines: { node: ">= 0.4" }

  typescript@5.4.5:
    resolution:
      {
        integrity: sha512-vcI4UpRgg81oIRUFwR0WSIHKt11nJ7SAVlYNIu+QpqeyXP+gpQJy/Z4+F0aGxSE4MqwjyXvW/TzgkLAx2AGHwQ==,
      }
    engines: { node: ">=14.17" }
    hasBin: true

  uglify-js@3.18.0:
    resolution:
      {
        integrity: sha512-SyVVbcNBCk0dzr9XL/R/ySrmYf0s372K6/hFklzgcp2lBFyXtw4I7BOdDjlLhE1aVqaI/SHWXWmYdlZxuyF38A==,
      }
    engines: { node: ">=0.8.0" }
    hasBin: true

  unbox-primitive@1.0.2:
    resolution:
      {
        integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==,
      }

  undici-types@5.26.5:
    resolution:
      {
        integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==,
      }

  update-browserslist-db@1.0.16:
    resolution:
      {
        integrity: sha512-KVbTxlBYlckhF5wgfyZXTWnMn7MMZjMu9XG8bPlliUOP9ThaF4QnhP8qrjrH7DRzHfSk0oQv1wToW+iA5GajEQ==,
      }
    hasBin: true
    peerDependencies:
      browserslist: ">= 4.21.0"

  upper-case-first@2.0.2:
    resolution:
      {
        integrity: sha512-514ppYHBaKwfJRK/pNC6c/OxfGa0obSnAl106u97Ed0I625Nin96KAjttZF6ZL3e1XLtphxnqrOi9iWgm+u+bg==,
      }

  upper-case@2.0.2:
    resolution:
      {
        integrity: sha512-KgdgDGJt2TpuwBUIjgG6lzw2GWFRCW9Qkfkiv0DxqHHLYJHmtmdUIKcZd8rHgFSjopVTlw6ggzCm1b8MFQwikg==,
      }

  uri-js@4.4.1:
    resolution:
      {
        integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==,
      }

  url-join@4.0.1:
    resolution:
      {
        integrity: sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==,
      }

  url-parse@1.5.10:
    resolution:
      {
        integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==,
      }

  util-deprecate@1.0.2:
    resolution:
      {
        integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==,
      }

  uuid@9.0.1:
    resolution:
      {
        integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==,
      }
    hasBin: true

  v8-compile-cache-lib@3.0.1:
    resolution:
      {
        integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==,
      }

  v8-to-istanbul@9.2.0:
    resolution:
      {
        integrity: sha512-/EH/sDgxU2eGxajKdwLCDmQ4FWq+kpi3uCmBGpw1xJtnAxEjlD8j8PEiGWpCIMIs3ciNAgH0d3TTJiUkYzyZjA==,
      }
    engines: { node: ">=10.12.0" }

  validate-npm-package-license@3.0.4:
    resolution:
      {
        integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==,
      }

  voyageai@0.0.3-1:
    resolution:
      {
        integrity: sha512-R3jN/xnILWoMBL3jPY61Ydm1JbpK3J+VmXBoHvlNg1Xz8h0xdX7sEffXeSu+sAEKQaPyWXVQDmM/jpBhPXw58g==,
      }

  walker@1.0.8:
    resolution:
      {
        integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==,
      }

  web-streams-polyfill@3.3.3:
    resolution:
      {
        integrity: sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==,
      }
    engines: { node: ">= 8" }

  web-streams-polyfill@4.0.0:
    resolution:
      {
        integrity: sha512-0zJXHRAYEjM2tUfZ2DiSOHAa2aw1tisnnhU3ufD57R8iefL+DcdJyRBRyJpG+NUimDgbTI/lH+gAE1PAvV3Cgw==,
      }
    engines: { node: ">= 8" }

  web-streams-polyfill@4.0.0-beta.3:
    resolution:
      {
        integrity: sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==,
      }
    engines: { node: ">= 14" }

  webidl-conversions@7.0.0:
    resolution:
      {
        integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==,
      }
    engines: { node: ">=12" }

  whatwg-fetch@3.6.20:
    resolution:
      {
        integrity: sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==,
      }

  whatwg-url@13.0.0:
    resolution:
      {
        integrity: sha512-9WWbymnqj57+XEuqADHrCJ2eSXzn8WXIW/YSGaZtb2WKAInQ6CHfaUUcTyyver0p8BDg5StLQq8h1vtZuwmOig==,
      }
    engines: { node: ">=16" }

  which-boxed-primitive@1.0.2:
    resolution:
      {
        integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==,
      }

  which-typed-array@1.1.15:
    resolution:
      {
        integrity: sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==,
      }
    engines: { node: ">= 0.4" }

  which@1.3.1:
    resolution:
      {
        integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==,
      }
    hasBin: true

  which@2.0.2:
    resolution:
      {
        integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==,
      }
    engines: { node: ">= 8" }
    hasBin: true

  wide-align@1.1.5:
    resolution:
      {
        integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==,
      }

  wordwrap@1.0.0:
    resolution:
      {
        integrity: sha512-gvVzJFlPycKc5dZN4yPkP8w7Dc37BtP1yczEneOb4uq34pXZcvrtRTmWV8W+Ume+XCxKgbjM+nevkyFPMybd4Q==,
      }

  wrap-ansi@7.0.0:
    resolution:
      {
        integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==,
      }
    engines: { node: ">=10" }

  wrap-ansi@8.1.0:
    resolution:
      {
        integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==,
      }
    engines: { node: ">=12" }

  wrappy@1.0.2:
    resolution:
      {
        integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==,
      }

  write-file-atomic@4.0.2:
    resolution:
      {
        integrity: sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==,
      }
    engines: { node: ^12.13.0 || ^14.15.0 || >=16.0.0 }

  y18n@5.0.8:
    resolution:
      {
        integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==,
      }
    engines: { node: ">=10" }

  yallist@3.1.1:
    resolution:
      {
        integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==,
      }

  yallist@4.0.0:
    resolution:
      {
        integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==,
      }

  yaml@2.4.5:
    resolution:
      {
        integrity: sha512-aBx2bnqDzVOyNKfsysjA2ms5ZlnjSAW2eG3/L5G/CSujfjLJTJsEw1bGw8kCf04KodQWk1pxlGnZ56CRxiawmg==,
      }
    engines: { node: ">= 14" }
    hasBin: true

  yargs-parser@20.2.9:
    resolution:
      {
        integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==,
      }
    engines: { node: ">=10" }

  yargs-parser@21.1.1:
    resolution:
      {
        integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==,
      }
    engines: { node: ">=12" }

  yargs@17.7.2:
    resolution:
      {
        integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==,
      }
    engines: { node: ">=12" }

  yn@3.1.1:
    resolution:
      {
        integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==,
      }
    engines: { node: ">=6" }

  yocto-queue@0.1.0:
    resolution:
      {
        integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==,
      }
    engines: { node: ">=10" }

  zip-stream@4.1.1:
    resolution:
      {
        integrity: sha512-9qv4rlDiopXg4E69k+vMHjNN63YFMe9sZMrdlvKnCjlCRWeCBswPPMPUfx+ipsAWq1LXHe70RcbaHdJJpS6hyQ==,
      }
    engines: { node: ">= 10" }

snapshots:
  "@ampproject/remapping@2.3.0":
    dependencies:
      "@jridgewell/gen-mapping": 0.3.5
      "@jridgewell/trace-mapping": 0.3.25

  "@apidevtools/openapi-schemas@2.1.0": {}

  "@apidevtools/swagger-methods@3.0.2": {}

  "@aws-crypto/crc32@3.0.0":
    dependencies:
      "@aws-crypto/util": 3.0.0
      "@aws-sdk/types": 3.577.0
      tslib: 1.14.1

  "@aws-crypto/ie11-detection@3.0.0":
    dependencies:
      tslib: 1.14.1

  "@aws-crypto/sha256-browser@3.0.0":
    dependencies:
      "@aws-crypto/ie11-detection": 3.0.0
      "@aws-crypto/sha256-js": 3.0.0
      "@aws-crypto/supports-web-crypto": 3.0.0
      "@aws-crypto/util": 3.0.0
      "@aws-sdk/types": 3.577.0
      "@aws-sdk/util-locate-window": 3.568.0
      "@aws-sdk/util-utf8-browser": 3.259.0
      tslib: 1.14.1

  "@aws-crypto/sha256-js@3.0.0":
    dependencies:
      "@aws-crypto/util": 3.0.0
      "@aws-sdk/types": 3.577.0
      tslib: 1.14.1

  "@aws-crypto/supports-web-crypto@3.0.0":
    dependencies:
      tslib: 1.14.1

  "@aws-crypto/util@3.0.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@aws-sdk/util-utf8-browser": 3.259.0
      tslib: 1.14.1

  "@aws-sdk/client-cognito-identity@3.596.0":
    dependencies:
      "@aws-crypto/sha256-browser": 3.0.0
      "@aws-crypto/sha256-js": 3.0.0
      "@aws-sdk/client-sso-oidc": 3.596.0
      "@aws-sdk/client-sts": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/core": 3.592.0
      "@aws-sdk/credential-provider-node": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/middleware-host-header": 3.577.0
      "@aws-sdk/middleware-logger": 3.577.0
      "@aws-sdk/middleware-recursion-detection": 3.577.0
      "@aws-sdk/middleware-user-agent": 3.587.0
      "@aws-sdk/region-config-resolver": 3.587.0
      "@aws-sdk/types": 3.577.0
      "@aws-sdk/util-endpoints": 3.587.0
      "@aws-sdk/util-user-agent-browser": 3.577.0
      "@aws-sdk/util-user-agent-node": 3.587.0
      "@smithy/config-resolver": 3.0.2
      "@smithy/core": 2.2.1
      "@smithy/fetch-http-handler": 3.0.2
      "@smithy/hash-node": 3.0.1
      "@smithy/invalid-dependency": 3.0.1
      "@smithy/middleware-content-length": 3.0.1
      "@smithy/middleware-endpoint": 3.0.2
      "@smithy/middleware-retry": 3.0.4
      "@smithy/middleware-serde": 3.0.1
      "@smithy/middleware-stack": 3.0.1
      "@smithy/node-config-provider": 3.1.1
      "@smithy/node-http-handler": 3.0.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      "@smithy/url-parser": 3.0.1
      "@smithy/util-base64": 3.0.0
      "@smithy/util-body-length-browser": 3.0.0
      "@smithy/util-body-length-node": 3.0.0
      "@smithy/util-defaults-mode-browser": 3.0.4
      "@smithy/util-defaults-mode-node": 3.0.4
      "@smithy/util-endpoints": 2.0.2
      "@smithy/util-middleware": 3.0.1
      "@smithy/util-retry": 3.0.1
      "@smithy/util-utf8": 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  "@aws-sdk/client-sagemaker@3.596.0":
    dependencies:
      "@aws-crypto/sha256-browser": 3.0.0
      "@aws-crypto/sha256-js": 3.0.0
      "@aws-sdk/client-sso-oidc": 3.596.0
      "@aws-sdk/client-sts": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/core": 3.592.0
      "@aws-sdk/credential-provider-node": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/middleware-host-header": 3.577.0
      "@aws-sdk/middleware-logger": 3.577.0
      "@aws-sdk/middleware-recursion-detection": 3.577.0
      "@aws-sdk/middleware-user-agent": 3.587.0
      "@aws-sdk/region-config-resolver": 3.587.0
      "@aws-sdk/types": 3.577.0
      "@aws-sdk/util-endpoints": 3.587.0
      "@aws-sdk/util-user-agent-browser": 3.577.0
      "@aws-sdk/util-user-agent-node": 3.587.0
      "@smithy/config-resolver": 3.0.2
      "@smithy/core": 2.2.1
      "@smithy/fetch-http-handler": 3.0.2
      "@smithy/hash-node": 3.0.1
      "@smithy/invalid-dependency": 3.0.1
      "@smithy/middleware-content-length": 3.0.1
      "@smithy/middleware-endpoint": 3.0.2
      "@smithy/middleware-retry": 3.0.4
      "@smithy/middleware-serde": 3.0.1
      "@smithy/middleware-stack": 3.0.1
      "@smithy/node-config-provider": 3.1.1
      "@smithy/node-http-handler": 3.0.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      "@smithy/url-parser": 3.0.1
      "@smithy/util-base64": 3.0.0
      "@smithy/util-body-length-browser": 3.0.0
      "@smithy/util-body-length-node": 3.0.0
      "@smithy/util-defaults-mode-browser": 3.0.4
      "@smithy/util-defaults-mode-node": 3.0.4
      "@smithy/util-endpoints": 2.0.2
      "@smithy/util-middleware": 3.0.1
      "@smithy/util-retry": 3.0.1
      "@smithy/util-utf8": 3.0.0
      "@smithy/util-waiter": 3.0.1
      tslib: 2.6.3
      uuid: 9.0.1
    transitivePeerDependencies:
      - aws-crt

  "@aws-sdk/client-sso-oidc@3.596.0":
    dependencies:
      "@aws-crypto/sha256-browser": 3.0.0
      "@aws-crypto/sha256-js": 3.0.0
      "@aws-sdk/client-sts": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/core": 3.592.0
      "@aws-sdk/credential-provider-node": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/middleware-host-header": 3.577.0
      "@aws-sdk/middleware-logger": 3.577.0
      "@aws-sdk/middleware-recursion-detection": 3.577.0
      "@aws-sdk/middleware-user-agent": 3.587.0
      "@aws-sdk/region-config-resolver": 3.587.0
      "@aws-sdk/types": 3.577.0
      "@aws-sdk/util-endpoints": 3.587.0
      "@aws-sdk/util-user-agent-browser": 3.577.0
      "@aws-sdk/util-user-agent-node": 3.587.0
      "@smithy/config-resolver": 3.0.2
      "@smithy/core": 2.2.1
      "@smithy/fetch-http-handler": 3.0.2
      "@smithy/hash-node": 3.0.1
      "@smithy/invalid-dependency": 3.0.1
      "@smithy/middleware-content-length": 3.0.1
      "@smithy/middleware-endpoint": 3.0.2
      "@smithy/middleware-retry": 3.0.4
      "@smithy/middleware-serde": 3.0.1
      "@smithy/middleware-stack": 3.0.1
      "@smithy/node-config-provider": 3.1.1
      "@smithy/node-http-handler": 3.0.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      "@smithy/url-parser": 3.0.1
      "@smithy/util-base64": 3.0.0
      "@smithy/util-body-length-browser": 3.0.0
      "@smithy/util-body-length-node": 3.0.0
      "@smithy/util-defaults-mode-browser": 3.0.4
      "@smithy/util-defaults-mode-node": 3.0.4
      "@smithy/util-endpoints": 2.0.2
      "@smithy/util-middleware": 3.0.1
      "@smithy/util-retry": 3.0.1
      "@smithy/util-utf8": 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  "@aws-sdk/client-sso@3.592.0":
    dependencies:
      "@aws-crypto/sha256-browser": 3.0.0
      "@aws-crypto/sha256-js": 3.0.0
      "@aws-sdk/core": 3.592.0
      "@aws-sdk/middleware-host-header": 3.577.0
      "@aws-sdk/middleware-logger": 3.577.0
      "@aws-sdk/middleware-recursion-detection": 3.577.0
      "@aws-sdk/middleware-user-agent": 3.587.0
      "@aws-sdk/region-config-resolver": 3.587.0
      "@aws-sdk/types": 3.577.0
      "@aws-sdk/util-endpoints": 3.587.0
      "@aws-sdk/util-user-agent-browser": 3.577.0
      "@aws-sdk/util-user-agent-node": 3.587.0
      "@smithy/config-resolver": 3.0.2
      "@smithy/core": 2.2.1
      "@smithy/fetch-http-handler": 3.0.2
      "@smithy/hash-node": 3.0.1
      "@smithy/invalid-dependency": 3.0.1
      "@smithy/middleware-content-length": 3.0.1
      "@smithy/middleware-endpoint": 3.0.2
      "@smithy/middleware-retry": 3.0.4
      "@smithy/middleware-serde": 3.0.1
      "@smithy/middleware-stack": 3.0.1
      "@smithy/node-config-provider": 3.1.1
      "@smithy/node-http-handler": 3.0.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      "@smithy/url-parser": 3.0.1
      "@smithy/util-base64": 3.0.0
      "@smithy/util-body-length-browser": 3.0.0
      "@smithy/util-body-length-node": 3.0.0
      "@smithy/util-defaults-mode-browser": 3.0.4
      "@smithy/util-defaults-mode-node": 3.0.4
      "@smithy/util-endpoints": 2.0.2
      "@smithy/util-middleware": 3.0.1
      "@smithy/util-retry": 3.0.1
      "@smithy/util-utf8": 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  "@aws-sdk/client-sts@3.596.0(@aws-sdk/client-sso-oidc@3.596.0)":
    dependencies:
      "@aws-crypto/sha256-browser": 3.0.0
      "@aws-crypto/sha256-js": 3.0.0
      "@aws-sdk/client-sso-oidc": 3.596.0
      "@aws-sdk/core": 3.592.0
      "@aws-sdk/credential-provider-node": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/middleware-host-header": 3.577.0
      "@aws-sdk/middleware-logger": 3.577.0
      "@aws-sdk/middleware-recursion-detection": 3.577.0
      "@aws-sdk/middleware-user-agent": 3.587.0
      "@aws-sdk/region-config-resolver": 3.587.0
      "@aws-sdk/types": 3.577.0
      "@aws-sdk/util-endpoints": 3.587.0
      "@aws-sdk/util-user-agent-browser": 3.577.0
      "@aws-sdk/util-user-agent-node": 3.587.0
      "@smithy/config-resolver": 3.0.2
      "@smithy/core": 2.2.1
      "@smithy/fetch-http-handler": 3.0.2
      "@smithy/hash-node": 3.0.1
      "@smithy/invalid-dependency": 3.0.1
      "@smithy/middleware-content-length": 3.0.1
      "@smithy/middleware-endpoint": 3.0.2
      "@smithy/middleware-retry": 3.0.4
      "@smithy/middleware-serde": 3.0.1
      "@smithy/middleware-stack": 3.0.1
      "@smithy/node-config-provider": 3.1.1
      "@smithy/node-http-handler": 3.0.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      "@smithy/url-parser": 3.0.1
      "@smithy/util-base64": 3.0.0
      "@smithy/util-body-length-browser": 3.0.0
      "@smithy/util-body-length-node": 3.0.0
      "@smithy/util-defaults-mode-browser": 3.0.4
      "@smithy/util-defaults-mode-node": 3.0.4
      "@smithy/util-endpoints": 2.0.2
      "@smithy/util-middleware": 3.0.1
      "@smithy/util-retry": 3.0.1
      "@smithy/util-utf8": 3.0.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - "@aws-sdk/client-sso-oidc"
      - aws-crt

  "@aws-sdk/core@3.592.0":
    dependencies:
      "@smithy/core": 2.2.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/signature-v4": 3.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      fast-xml-parser: 4.2.5
      tslib: 2.6.3

  "@aws-sdk/credential-provider-cognito-identity@3.596.0":
    dependencies:
      "@aws-sdk/client-cognito-identity": 3.596.0
      "@aws-sdk/types": 3.577.0
      "@smithy/property-provider": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - aws-crt

  "@aws-sdk/credential-provider-env@3.587.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/property-provider": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/credential-provider-http@3.596.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/fetch-http-handler": 3.0.2
      "@smithy/node-http-handler": 3.0.1
      "@smithy/property-provider": 3.1.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      "@smithy/util-stream": 3.0.2
      tslib: 2.6.3

  "@aws-sdk/credential-provider-ini@3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)":
    dependencies:
      "@aws-sdk/client-sts": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/credential-provider-env": 3.587.0
      "@aws-sdk/credential-provider-http": 3.596.0
      "@aws-sdk/credential-provider-process": 3.587.0
      "@aws-sdk/credential-provider-sso": 3.592.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/credential-provider-web-identity": 3.587.0(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/types": 3.577.0
      "@smithy/credential-provider-imds": 3.1.1
      "@smithy/property-provider": 3.1.1
      "@smithy/shared-ini-file-loader": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - "@aws-sdk/client-sso-oidc"
      - aws-crt

  "@aws-sdk/credential-provider-node@3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)":
    dependencies:
      "@aws-sdk/credential-provider-env": 3.587.0
      "@aws-sdk/credential-provider-http": 3.596.0
      "@aws-sdk/credential-provider-ini": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/credential-provider-process": 3.587.0
      "@aws-sdk/credential-provider-sso": 3.592.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/credential-provider-web-identity": 3.587.0(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/types": 3.577.0
      "@smithy/credential-provider-imds": 3.1.1
      "@smithy/property-provider": 3.1.1
      "@smithy/shared-ini-file-loader": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - "@aws-sdk/client-sso-oidc"
      - "@aws-sdk/client-sts"
      - aws-crt

  "@aws-sdk/credential-provider-process@3.587.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/property-provider": 3.1.1
      "@smithy/shared-ini-file-loader": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/credential-provider-sso@3.592.0(@aws-sdk/client-sso-oidc@3.596.0)":
    dependencies:
      "@aws-sdk/client-sso": 3.592.0
      "@aws-sdk/token-providers": 3.587.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/types": 3.577.0
      "@smithy/property-provider": 3.1.1
      "@smithy/shared-ini-file-loader": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - "@aws-sdk/client-sso-oidc"
      - aws-crt

  "@aws-sdk/credential-provider-web-identity@3.587.0(@aws-sdk/client-sts@3.596.0)":
    dependencies:
      "@aws-sdk/client-sts": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/types": 3.577.0
      "@smithy/property-provider": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/credential-providers@3.596.0(@aws-sdk/client-sso-oidc@3.596.0)":
    dependencies:
      "@aws-sdk/client-cognito-identity": 3.596.0
      "@aws-sdk/client-sso": 3.592.0
      "@aws-sdk/client-sts": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/credential-provider-cognito-identity": 3.596.0
      "@aws-sdk/credential-provider-env": 3.587.0
      "@aws-sdk/credential-provider-http": 3.596.0
      "@aws-sdk/credential-provider-ini": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/credential-provider-node": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/credential-provider-process": 3.587.0
      "@aws-sdk/credential-provider-sso": 3.592.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/credential-provider-web-identity": 3.587.0(@aws-sdk/client-sts@3.596.0)
      "@aws-sdk/types": 3.577.0
      "@smithy/credential-provider-imds": 3.1.1
      "@smithy/property-provider": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3
    transitivePeerDependencies:
      - "@aws-sdk/client-sso-oidc"
      - aws-crt

  "@aws-sdk/middleware-host-header@3.577.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/protocol-http": 4.0.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/middleware-logger@3.577.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/middleware-recursion-detection@3.577.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/protocol-http": 4.0.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/middleware-user-agent@3.587.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@aws-sdk/util-endpoints": 3.587.0
      "@smithy/protocol-http": 4.0.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/protocol-http@3.374.0":
    dependencies:
      "@smithy/protocol-http": 1.2.0
      tslib: 2.6.3

  "@aws-sdk/region-config-resolver@3.587.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/node-config-provider": 3.1.1
      "@smithy/types": 3.1.0
      "@smithy/util-config-provider": 3.0.0
      "@smithy/util-middleware": 3.0.1
      tslib: 2.6.3

  "@aws-sdk/signature-v4@3.374.0":
    dependencies:
      "@smithy/signature-v4": 1.1.0
      tslib: 2.6.3

  "@aws-sdk/token-providers@3.587.0(@aws-sdk/client-sso-oidc@3.596.0)":
    dependencies:
      "@aws-sdk/client-sso-oidc": 3.596.0
      "@aws-sdk/types": 3.577.0
      "@smithy/property-provider": 3.1.1
      "@smithy/shared-ini-file-loader": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/types@3.577.0":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/util-endpoints@3.587.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/types": 3.1.0
      "@smithy/util-endpoints": 2.0.2
      tslib: 2.6.3

  "@aws-sdk/util-locate-window@3.568.0":
    dependencies:
      tslib: 2.6.3

  "@aws-sdk/util-user-agent-browser@3.577.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/types": 3.1.0
      bowser: 2.11.0
      tslib: 2.6.3

  "@aws-sdk/util-user-agent-node@3.587.0":
    dependencies:
      "@aws-sdk/types": 3.577.0
      "@smithy/node-config-provider": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@aws-sdk/util-utf8-browser@3.259.0":
    dependencies:
      tslib: 2.6.3

  "@babel/code-frame@7.24.7":
    dependencies:
      "@babel/highlight": 7.24.7
      picocolors: 1.0.1

  "@babel/compat-data@7.24.7": {}

  "@babel/core@7.24.7":
    dependencies:
      "@ampproject/remapping": 2.3.0
      "@babel/code-frame": 7.24.7
      "@babel/generator": 7.24.7
      "@babel/helper-compilation-targets": 7.24.7
      "@babel/helper-module-transforms": 7.24.7(@babel/core@7.24.7)
      "@babel/helpers": 7.24.7
      "@babel/parser": 7.24.7
      "@babel/template": 7.24.7
      "@babel/traverse": 7.24.7
      "@babel/types": 7.24.7
      convert-source-map: 2.0.0
      debug: 4.3.5
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  "@babel/generator@7.24.7":
    dependencies:
      "@babel/types": 7.24.7
      "@jridgewell/gen-mapping": 0.3.5
      "@jridgewell/trace-mapping": 0.3.25
      jsesc: 2.5.2

  "@babel/helper-compilation-targets@7.24.7":
    dependencies:
      "@babel/compat-data": 7.24.7
      "@babel/helper-validator-option": 7.24.7
      browserslist: 4.23.1
      lru-cache: 5.1.1
      semver: 6.3.1

  "@babel/helper-environment-visitor@7.24.7":
    dependencies:
      "@babel/types": 7.24.7

  "@babel/helper-function-name@7.24.7":
    dependencies:
      "@babel/template": 7.24.7
      "@babel/types": 7.24.7

  "@babel/helper-hoist-variables@7.24.7":
    dependencies:
      "@babel/types": 7.24.7

  "@babel/helper-module-imports@7.24.7":
    dependencies:
      "@babel/traverse": 7.24.7
      "@babel/types": 7.24.7
    transitivePeerDependencies:
      - supports-color

  "@babel/helper-module-transforms@7.24.7(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-environment-visitor": 7.24.7
      "@babel/helper-module-imports": 7.24.7
      "@babel/helper-simple-access": 7.24.7
      "@babel/helper-split-export-declaration": 7.24.7
      "@babel/helper-validator-identifier": 7.24.7
    transitivePeerDependencies:
      - supports-color

  "@babel/helper-plugin-utils@7.24.7": {}

  "@babel/helper-simple-access@7.24.7":
    dependencies:
      "@babel/traverse": 7.24.7
      "@babel/types": 7.24.7
    transitivePeerDependencies:
      - supports-color

  "@babel/helper-split-export-declaration@7.24.7":
    dependencies:
      "@babel/types": 7.24.7

  "@babel/helper-string-parser@7.24.7": {}

  "@babel/helper-validator-identifier@7.24.7": {}

  "@babel/helper-validator-option@7.24.7": {}

  "@babel/helpers@7.24.7":
    dependencies:
      "@babel/template": 7.24.7
      "@babel/types": 7.24.7

  "@babel/highlight@7.24.7":
    dependencies:
      "@babel/helper-validator-identifier": 7.24.7
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.0.1

  "@babel/parser@7.24.7":
    dependencies:
      "@babel/types": 7.24.7

  "@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-jsx@7.24.7(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/plugin-syntax-typescript@7.24.7(@babel/core@7.24.7)":
    dependencies:
      "@babel/core": 7.24.7
      "@babel/helper-plugin-utils": 7.24.7

  "@babel/template@7.24.7":
    dependencies:
      "@babel/code-frame": 7.24.7
      "@babel/parser": 7.24.7
      "@babel/types": 7.24.7

  "@babel/traverse@7.24.7":
    dependencies:
      "@babel/code-frame": 7.24.7
      "@babel/generator": 7.24.7
      "@babel/helper-environment-visitor": 7.24.7
      "@babel/helper-function-name": 7.24.7
      "@babel/helper-hoist-variables": 7.24.7
      "@babel/helper-split-export-declaration": 7.24.7
      "@babel/parser": 7.24.7
      "@babel/types": 7.24.7
      debug: 4.3.5
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  "@babel/types@7.24.7":
    dependencies:
      "@babel/helper-string-parser": 7.24.7
      "@babel/helper-validator-identifier": 7.24.7
      to-fast-properties: 2.0.0

  "@balena/dockerignore@1.0.2": {}

  "@bcoe/v8-coverage@0.2.3": {}

  "@cspotcode/source-map-support@0.8.1":
    dependencies:
      "@jridgewell/trace-mapping": 0.3.9

  "@esbuild/aix-ppc64@0.19.12":
    optional: true

  "@esbuild/android-arm64@0.19.12":
    optional: true

  "@esbuild/android-arm@0.19.12":
    optional: true

  "@esbuild/android-x64@0.19.12":
    optional: true

  "@esbuild/darwin-arm64@0.19.12":
    optional: true

  "@esbuild/darwin-x64@0.19.12":
    optional: true

  "@esbuild/freebsd-arm64@0.19.12":
    optional: true

  "@esbuild/freebsd-x64@0.19.12":
    optional: true

  "@esbuild/linux-arm64@0.19.12":
    optional: true

  "@esbuild/linux-arm@0.19.12":
    optional: true

  "@esbuild/linux-ia32@0.19.12":
    optional: true

  "@esbuild/linux-loong64@0.19.12":
    optional: true

  "@esbuild/linux-mips64el@0.19.12":
    optional: true

  "@esbuild/linux-ppc64@0.19.12":
    optional: true

  "@esbuild/linux-riscv64@0.19.12":
    optional: true

  "@esbuild/linux-s390x@0.19.12":
    optional: true

  "@esbuild/linux-x64@0.19.12":
    optional: true

  "@esbuild/netbsd-x64@0.19.12":
    optional: true

  "@esbuild/openbsd-x64@0.19.12":
    optional: true

  "@esbuild/sunos-x64@0.19.12":
    optional: true

  "@esbuild/win32-arm64@0.19.12":
    optional: true

  "@esbuild/win32-ia32@0.19.12":
    optional: true

  "@esbuild/win32-x64@0.19.12":
    optional: true

  "@google/generative-ai@0.1.3": {}

  "@huggingface/jinja@0.1.3": {}

  "@huggingface/jinja@0.2.2": {}

  "@isaacs/cliui@8.0.2":
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  "@istanbuljs/load-nyc-config@1.1.0":
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  "@istanbuljs/schema@0.1.3": {}

  "@jest/console@29.7.0":
    dependencies:
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      chalk: 4.1.2
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0

  "@jest/core@29.7.0(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))":
    dependencies:
      "@jest/console": 29.7.0
      "@jest/reporters": 29.7.0
      "@jest/test-result": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      ci-info: 3.9.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 29.7.0
      jest-config: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-resolve-dependencies: 29.7.0
      jest-runner: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      jest-watcher: 29.7.0
      micromatch: 4.0.7
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
      - ts-node

  "@jest/environment@29.7.0":
    dependencies:
      "@jest/fake-timers": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      jest-mock: 29.7.0

  "@jest/expect-utils@29.7.0":
    dependencies:
      jest-get-type: 29.6.3

  "@jest/expect@29.7.0":
    dependencies:
      expect: 29.7.0
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  "@jest/fake-timers@29.7.0":
    dependencies:
      "@jest/types": 29.6.3
      "@sinonjs/fake-timers": 10.3.0
      "@types/node": 20.14.2
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-util: 29.7.0

  "@jest/globals@29.7.0":
    dependencies:
      "@jest/environment": 29.7.0
      "@jest/expect": 29.7.0
      "@jest/types": 29.6.3
      jest-mock: 29.7.0
    transitivePeerDependencies:
      - supports-color

  "@jest/reporters@29.7.0":
    dependencies:
      "@bcoe/v8-coverage": 0.2.3
      "@jest/console": 29.7.0
      "@jest/test-result": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      "@jridgewell/trace-mapping": 0.3.25
      "@types/node": 20.14.2
      chalk: 4.1.2
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 6.0.2
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.7
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      jest-worker: 29.7.0
      slash: 3.0.0
      string-length: 4.0.2
      strip-ansi: 6.0.1
      v8-to-istanbul: 9.2.0
    transitivePeerDependencies:
      - supports-color

  "@jest/schemas@29.6.3":
    dependencies:
      "@sinclair/typebox": 0.27.8

  "@jest/source-map@29.6.3":
    dependencies:
      "@jridgewell/trace-mapping": 0.3.25
      callsites: 3.1.0
      graceful-fs: 4.2.11

  "@jest/test-result@29.7.0":
    dependencies:
      "@jest/console": 29.7.0
      "@jest/types": 29.6.3
      "@types/istanbul-lib-coverage": 2.0.6
      collect-v8-coverage: 1.0.2

  "@jest/test-sequencer@29.7.0":
    dependencies:
      "@jest/test-result": 29.7.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      slash: 3.0.0

  "@jest/transform@29.7.0":
    dependencies:
      "@babel/core": 7.24.7
      "@jest/types": 29.6.3
      "@jridgewell/trace-mapping": 0.3.25
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 2.0.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      micromatch: 4.0.7
      pirates: 4.0.6
      slash: 3.0.0
      write-file-atomic: 4.0.2
    transitivePeerDependencies:
      - supports-color

  "@jest/types@29.6.3":
    dependencies:
      "@jest/schemas": 29.6.3
      "@types/istanbul-lib-coverage": 2.0.6
      "@types/istanbul-reports": 3.0.4
      "@types/node": 20.14.2
      "@types/yargs": 17.0.32
      chalk: 4.1.2

  "@jridgewell/gen-mapping@0.3.5":
    dependencies:
      "@jridgewell/set-array": 1.2.1
      "@jridgewell/sourcemap-codec": 1.4.15
      "@jridgewell/trace-mapping": 0.3.25

  "@jridgewell/resolve-uri@3.1.2": {}

  "@jridgewell/set-array@1.2.1": {}

  "@jridgewell/sourcemap-codec@1.4.15": {}

  "@jridgewell/trace-mapping@0.3.25":
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.4.15

  "@jridgewell/trace-mapping@0.3.9":
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.4.15

  "@jsdevtools/ono@7.1.3": {}

  "@mapbox/node-pre-gyp@1.0.11":
    dependencies:
      detect-libc: 2.0.3
      https-proxy-agent: 5.0.1
      make-dir: 3.1.0
      node-fetch: 2.7.0
      nopt: 5.0.0
      npmlog: 5.0.1
      rimraf: 3.0.2
      semver: 7.7.1
      tar: 6.2.1
    transitivePeerDependencies:
      - encoding
      - supports-color

  "@nodelib/fs.scandir@2.1.5":
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      run-parallel: 1.2.0

  "@nodelib/fs.stat@2.0.5": {}

  "@nodelib/fs.walk@1.2.8":
    dependencies:
      "@nodelib/fs.scandir": 2.1.5
      fastq: 1.17.1

  "@openapi-generator-plus/core@2.23.0(openapi-types@12.1.3)":
    dependencies:
      "@openapi-generator-plus/indexed-type": 1.0.0
      "@openapi-generator-plus/swagger-parser": 10.1.0(openapi-types@12.1.3)
      "@openapi-generator-plus/types": 2.19.0
      "@openapi-generator-plus/utils": 1.1.4
      lodash: 4.17.21
      pluralize: 8.0.0
    transitivePeerDependencies:
      - openapi-types

  "@openapi-generator-plus/generator-common@1.7.1(openapi-types@12.1.3)":
    dependencies:
      "@openapi-generator-plus/core": 2.23.0(openapi-types@12.1.3)
      "@openapi-generator-plus/types": 2.19.0
      "@openapi-generator-plus/utils": 1.1.4
      pluralize: 8.0.0
      url-parse: 1.5.10
    transitivePeerDependencies:
      - openapi-types

  "@openapi-generator-plus/handlebars-templates@1.11.1(openapi-types@12.1.3)":
    dependencies:
      "@openapi-generator-plus/generator-common": 1.7.1(openapi-types@12.1.3)
      "@openapi-generator-plus/indexed-type": 1.0.0
      "@openapi-generator-plus/types": 2.19.0
      change-case: 4.1.2
      handlebars: 4.7.8
      marked: 12.0.2
      pluralize: 8.0.0
    transitivePeerDependencies:
      - openapi-types

  "@openapi-generator-plus/indexed-type@1.0.0": {}

  "@openapi-generator-plus/java-like-generator-helper@2.6.0(openapi-types@12.1.3)":
    dependencies:
      "@openapi-generator-plus/generator-common": 1.7.1(openapi-types@12.1.3)
      "@openapi-generator-plus/types": 2.19.0
      change-case: 4.1.2
    transitivePeerDependencies:
      - openapi-types

  "@openapi-generator-plus/json-schema-ref-parser@9.0.11":
    dependencies:
      "@jsdevtools/ono": 7.1.3
      "@types/json-schema": 7.0.15
      call-me-maybe: 1.0.2
      js-yaml: 4.1.0

  "@openapi-generator-plus/swagger-parser@10.1.0(openapi-types@12.1.3)":
    dependencies:
      "@apidevtools/openapi-schemas": 2.1.0
      "@apidevtools/swagger-methods": 3.0.2
      "@jsdevtools/ono": 7.1.3
      "@openapi-generator-plus/json-schema-ref-parser": 9.0.11
      ajv: 8.16.0
      ajv-draft-04: 1.0.0(ajv@8.16.0)
      call-me-maybe: 1.0.2
      openapi-types: 12.1.3

  "@openapi-generator-plus/types@2.19.0": {}

  "@openapi-generator-plus/typescript-fetch-client-generator@1.11.0(openapi-types@12.1.3)":
    dependencies:
      "@openapi-generator-plus/generator-common": 1.7.1(openapi-types@12.1.3)
      "@openapi-generator-plus/handlebars-templates": 1.11.1(openapi-types@12.1.3)
      "@openapi-generator-plus/indexed-type": 1.0.0
      "@openapi-generator-plus/types": 2.19.0
      "@openapi-generator-plus/typescript-generator-common": 1.12.0(openapi-types@12.1.3)
      change-case: 4.1.2
    transitivePeerDependencies:
      - openapi-types

  "@openapi-generator-plus/typescript-generator-common@1.12.0(openapi-types@12.1.3)":
    dependencies:
      "@openapi-generator-plus/generator-common": 1.7.1(openapi-types@12.1.3)
      "@openapi-generator-plus/handlebars-templates": 1.11.1(openapi-types@12.1.3)
      "@openapi-generator-plus/java-like-generator-helper": 2.6.0(openapi-types@12.1.3)
      "@openapi-generator-plus/types": 2.19.0
      handlebars: 4.7.8
      pluralize: 8.0.0
    transitivePeerDependencies:
      - openapi-types

  "@openapi-generator-plus/utils@1.1.4":
    dependencies:
      "@openapi-generator-plus/indexed-type": 1.0.0
      "@openapi-generator-plus/types": 2.19.0

  "@pkgjs/parseargs@0.11.0":
    optional: true

  "@protobufjs/aspromise@1.1.2": {}

  "@protobufjs/base64@1.1.2": {}

  "@protobufjs/codegen@2.0.4": {}

  "@protobufjs/eventemitter@1.1.0": {}

  "@protobufjs/fetch@1.1.0":
    dependencies:
      "@protobufjs/aspromise": 1.1.2
      "@protobufjs/inquire": 1.1.0

  "@protobufjs/float@1.0.2": {}

  "@protobufjs/inquire@1.1.0": {}

  "@protobufjs/path@1.1.2": {}

  "@protobufjs/pool@1.1.0": {}

  "@protobufjs/utf8@1.1.0": {}

  "@rollup/rollup-android-arm-eabi@4.18.0":
    optional: true

  "@rollup/rollup-android-arm64@4.18.0":
    optional: true

  "@rollup/rollup-darwin-arm64@4.18.0":
    optional: true

  "@rollup/rollup-darwin-x64@4.18.0":
    optional: true

  "@rollup/rollup-linux-arm-gnueabihf@4.18.0":
    optional: true

  "@rollup/rollup-linux-arm-musleabihf@4.18.0":
    optional: true

  "@rollup/rollup-linux-arm64-gnu@4.18.0":
    optional: true

  "@rollup/rollup-linux-arm64-musl@4.18.0":
    optional: true

  "@rollup/rollup-linux-powerpc64le-gnu@4.18.0":
    optional: true

  "@rollup/rollup-linux-riscv64-gnu@4.18.0":
    optional: true

  "@rollup/rollup-linux-s390x-gnu@4.18.0":
    optional: true

  "@rollup/rollup-linux-x64-gnu@4.18.0":
    optional: true

  "@rollup/rollup-linux-x64-musl@4.18.0":
    optional: true

  "@rollup/rollup-win32-arm64-msvc@4.18.0":
    optional: true

  "@rollup/rollup-win32-ia32-msvc@4.18.0":
    optional: true

  "@rollup/rollup-win32-x64-msvc@4.18.0":
    optional: true

  "@sinclair/typebox@0.27.8": {}

  "@sinonjs/commons@3.0.1":
    dependencies:
      type-detect: 4.0.8

  "@sinonjs/fake-timers@10.3.0":
    dependencies:
      "@sinonjs/commons": 3.0.1

  "@smithy/abort-controller@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/config-resolver@3.0.2":
    dependencies:
      "@smithy/node-config-provider": 3.1.1
      "@smithy/types": 3.1.0
      "@smithy/util-config-provider": 3.0.0
      "@smithy/util-middleware": 3.0.1
      tslib: 2.6.3

  "@smithy/core@2.2.1":
    dependencies:
      "@smithy/middleware-endpoint": 3.0.2
      "@smithy/middleware-retry": 3.0.4
      "@smithy/middleware-serde": 3.0.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      "@smithy/util-middleware": 3.0.1
      tslib: 2.6.3

  "@smithy/credential-provider-imds@3.1.1":
    dependencies:
      "@smithy/node-config-provider": 3.1.1
      "@smithy/property-provider": 3.1.1
      "@smithy/types": 3.1.0
      "@smithy/url-parser": 3.0.1
      tslib: 2.6.3

  "@smithy/eventstream-codec@1.1.0":
    dependencies:
      "@aws-crypto/crc32": 3.0.0
      "@smithy/types": 1.2.0
      "@smithy/util-hex-encoding": 1.1.0
      tslib: 2.6.3

  "@smithy/fetch-http-handler@3.0.2":
    dependencies:
      "@smithy/protocol-http": 4.0.1
      "@smithy/querystring-builder": 3.0.1
      "@smithy/types": 3.1.0
      "@smithy/util-base64": 3.0.0
      tslib: 2.6.3

  "@smithy/hash-node@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      "@smithy/util-buffer-from": 3.0.0
      "@smithy/util-utf8": 3.0.0
      tslib: 2.6.3

  "@smithy/invalid-dependency@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/is-array-buffer@1.1.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/is-array-buffer@3.0.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/middleware-content-length@3.0.1":
    dependencies:
      "@smithy/protocol-http": 4.0.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/middleware-endpoint@3.0.2":
    dependencies:
      "@smithy/middleware-serde": 3.0.1
      "@smithy/node-config-provider": 3.1.1
      "@smithy/shared-ini-file-loader": 3.1.1
      "@smithy/types": 3.1.0
      "@smithy/url-parser": 3.0.1
      "@smithy/util-middleware": 3.0.1
      tslib: 2.6.3

  "@smithy/middleware-retry@3.0.4":
    dependencies:
      "@smithy/node-config-provider": 3.1.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/service-error-classification": 3.0.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      "@smithy/util-middleware": 3.0.1
      "@smithy/util-retry": 3.0.1
      tslib: 2.6.3
      uuid: 9.0.1

  "@smithy/middleware-serde@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/middleware-stack@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/node-config-provider@3.1.1":
    dependencies:
      "@smithy/property-provider": 3.1.1
      "@smithy/shared-ini-file-loader": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/node-http-handler@3.0.1":
    dependencies:
      "@smithy/abort-controller": 3.0.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/querystring-builder": 3.0.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/property-provider@3.1.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/protocol-http@1.2.0":
    dependencies:
      "@smithy/types": 1.2.0
      tslib: 2.6.3

  "@smithy/protocol-http@4.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/querystring-builder@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      "@smithy/util-uri-escape": 3.0.0
      tslib: 2.6.3

  "@smithy/querystring-parser@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/service-error-classification@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0

  "@smithy/shared-ini-file-loader@3.1.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/signature-v4@1.1.0":
    dependencies:
      "@smithy/eventstream-codec": 1.1.0
      "@smithy/is-array-buffer": 1.1.0
      "@smithy/types": 1.2.0
      "@smithy/util-hex-encoding": 1.1.0
      "@smithy/util-middleware": 1.1.0
      "@smithy/util-uri-escape": 1.1.0
      "@smithy/util-utf8": 1.1.0
      tslib: 2.6.3

  "@smithy/signature-v4@3.0.1":
    dependencies:
      "@smithy/is-array-buffer": 3.0.0
      "@smithy/types": 3.1.0
      "@smithy/util-hex-encoding": 3.0.0
      "@smithy/util-middleware": 3.0.1
      "@smithy/util-uri-escape": 3.0.0
      "@smithy/util-utf8": 3.0.0
      tslib: 2.6.3

  "@smithy/smithy-client@3.1.2":
    dependencies:
      "@smithy/middleware-endpoint": 3.0.2
      "@smithy/middleware-stack": 3.0.1
      "@smithy/protocol-http": 4.0.1
      "@smithy/types": 3.1.0
      "@smithy/util-stream": 3.0.2
      tslib: 2.6.3

  "@smithy/types@1.2.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/types@3.1.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/url-parser@3.0.1":
    dependencies:
      "@smithy/querystring-parser": 3.0.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/util-base64@3.0.0":
    dependencies:
      "@smithy/util-buffer-from": 3.0.0
      "@smithy/util-utf8": 3.0.0
      tslib: 2.6.3

  "@smithy/util-body-length-browser@3.0.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/util-body-length-node@3.0.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/util-buffer-from@1.1.0":
    dependencies:
      "@smithy/is-array-buffer": 1.1.0
      tslib: 2.6.3

  "@smithy/util-buffer-from@3.0.0":
    dependencies:
      "@smithy/is-array-buffer": 3.0.0
      tslib: 2.6.3

  "@smithy/util-config-provider@3.0.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/util-defaults-mode-browser@3.0.4":
    dependencies:
      "@smithy/property-provider": 3.1.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      bowser: 2.11.0
      tslib: 2.6.3

  "@smithy/util-defaults-mode-node@3.0.4":
    dependencies:
      "@smithy/config-resolver": 3.0.2
      "@smithy/credential-provider-imds": 3.1.1
      "@smithy/node-config-provider": 3.1.1
      "@smithy/property-provider": 3.1.1
      "@smithy/smithy-client": 3.1.2
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/util-endpoints@2.0.2":
    dependencies:
      "@smithy/node-config-provider": 3.1.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/util-hex-encoding@1.1.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/util-hex-encoding@3.0.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/util-middleware@1.1.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/util-middleware@3.0.1":
    dependencies:
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/util-retry@3.0.1":
    dependencies:
      "@smithy/service-error-classification": 3.0.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@smithy/util-stream@3.0.2":
    dependencies:
      "@smithy/fetch-http-handler": 3.0.2
      "@smithy/node-http-handler": 3.0.1
      "@smithy/types": 3.1.0
      "@smithy/util-base64": 3.0.0
      "@smithy/util-buffer-from": 3.0.0
      "@smithy/util-hex-encoding": 3.0.0
      "@smithy/util-utf8": 3.0.0
      tslib: 2.6.3

  "@smithy/util-uri-escape@1.1.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/util-uri-escape@3.0.0":
    dependencies:
      tslib: 2.6.3

  "@smithy/util-utf8@1.1.0":
    dependencies:
      "@smithy/util-buffer-from": 1.1.0
      tslib: 2.6.3

  "@smithy/util-utf8@3.0.0":
    dependencies:
      "@smithy/util-buffer-from": 3.0.0
      tslib: 2.6.3

  "@smithy/util-waiter@3.0.1":
    dependencies:
      "@smithy/abort-controller": 3.0.1
      "@smithy/types": 3.1.0
      tslib: 2.6.3

  "@tsconfig/node10@1.0.11": {}

  "@tsconfig/node12@1.0.11": {}

  "@tsconfig/node14@1.0.3": {}

  "@tsconfig/node16@1.0.4": {}

  "@tsd/typescript@5.0.4": {}

  "@types/babel__core@7.20.5":
    dependencies:
      "@babel/parser": 7.24.7
      "@babel/types": 7.24.7
      "@types/babel__generator": 7.6.8
      "@types/babel__template": 7.4.4
      "@types/babel__traverse": 7.20.6

  "@types/babel__generator@7.6.8":
    dependencies:
      "@babel/types": 7.24.7

  "@types/babel__template@7.4.4":
    dependencies:
      "@babel/parser": 7.24.7
      "@babel/types": 7.24.7

  "@types/babel__traverse@7.20.6":
    dependencies:
      "@babel/types": 7.24.7

  "@types/bcrypt@5.0.2":
    dependencies:
      "@types/node": 20.14.2

  "@types/docker-modem@3.0.6":
    dependencies:
      "@types/node": 20.14.2
      "@types/ssh2": 1.15.0

  "@types/dockerode@3.3.29":
    dependencies:
      "@types/docker-modem": 3.0.6
      "@types/node": 20.14.2
      "@types/ssh2": 1.15.0

  "@types/eslint@7.29.0":
    dependencies:
      "@types/estree": 1.0.5
      "@types/json-schema": 7.0.15

  "@types/estree@1.0.5": {}

  "@types/graceful-fs@4.1.9":
    dependencies:
      "@types/node": 20.14.2

  "@types/istanbul-lib-coverage@2.0.6": {}

  "@types/istanbul-lib-report@3.0.3":
    dependencies:
      "@types/istanbul-lib-coverage": 2.0.6

  "@types/istanbul-reports@3.0.4":
    dependencies:
      "@types/istanbul-lib-report": 3.0.3

  "@types/jest@29.5.12":
    dependencies:
      expect: 29.7.0
      pretty-format: 29.7.0

  "@types/json-schema@7.0.15": {}

  "@types/long@4.0.2": {}

  "@types/minimist@1.2.5": {}

  "@types/node-fetch@2.6.11":
    dependencies:
      "@types/node": 20.14.2
      form-data: 4.0.0

  "@types/node@18.19.34":
    dependencies:
      undici-types: 5.26.5

  "@types/node@20.14.2":
    dependencies:
      undici-types: 5.26.5

  "@types/normalize-package-data@2.4.4": {}

  "@types/semver@7.7.0": {}

  "@types/ssh2-streams@0.1.12":
    dependencies:
      "@types/node": 20.14.2

  "@types/ssh2@0.5.52":
    dependencies:
      "@types/node": 20.14.2
      "@types/ssh2-streams": 0.1.12

  "@types/ssh2@1.15.0":
    dependencies:
      "@types/node": 18.19.34

  "@types/stack-utils@2.0.3": {}

  "@types/yargs-parser@21.0.3": {}

  "@types/yargs@17.0.32":
    dependencies:
      "@types/yargs-parser": 21.0.3

  "@xenova/transformers@2.17.2":
    dependencies:
      "@huggingface/jinja": 0.2.2
      onnxruntime-web: 1.14.0
      sharp: 0.32.6
    optionalDependencies:
      onnxruntime-node: 1.14.0

  abbrev@1.1.1: {}

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  acorn-walk@8.3.2: {}

  acorn@8.11.3: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.3.5
    transitivePeerDependencies:
      - supports-color

  agentkeepalive@4.5.0:
    dependencies:
      humanize-ms: 1.2.1

  ajv-draft-04@1.0.0(ajv@8.16.0):
    optionalDependencies:
      ajv: 8.16.0

  ajv@8.16.0:
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  ansi-colors@4.1.3: {}

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  aproba@2.0.0: {}

  archiver-utils@2.1.0:
    dependencies:
      glob: 7.2.3
      graceful-fs: 4.2.11
      lazystream: 1.0.1
      lodash.defaults: 4.2.0
      lodash.difference: 4.5.0
      lodash.flatten: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.union: 4.6.0
      normalize-path: 3.0.0
      readable-stream: 2.3.8

  archiver-utils@3.0.4:
    dependencies:
      glob: 7.2.3
      graceful-fs: 4.2.11
      lazystream: 1.0.1
      lodash.defaults: 4.2.0
      lodash.difference: 4.5.0
      lodash.flatten: 4.4.0
      lodash.isplainobject: 4.0.6
      lodash.union: 4.6.0
      normalize-path: 3.0.0
      readable-stream: 3.6.2

  archiver@5.3.2:
    dependencies:
      archiver-utils: 2.1.0
      async: 3.2.5
      buffer-crc32: 0.2.13
      readable-stream: 3.6.2
      readdir-glob: 1.1.3
      tar-stream: 2.2.0
      zip-stream: 4.1.1

  are-we-there-yet@2.0.0:
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2

  arg@4.1.3: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  array-buffer-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      is-array-buffer: 3.0.4

  array-union@2.1.0: {}

  arraybuffer.prototype.slice@1.0.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      is-array-buffer: 3.0.4
      is-shared-array-buffer: 1.0.3

  arrify@1.0.1: {}

  asn1@0.2.6:
    dependencies:
      safer-buffer: 2.1.2

  async-lock@1.4.1: {}

  async@3.2.5: {}

  asynckit@0.4.0: {}

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.0.0

  b4a@1.6.6: {}

  babel-jest@29.7.0(@babel/core@7.24.7):
    dependencies:
      "@babel/core": 7.24.7
      "@jest/transform": 29.7.0
      "@types/babel__core": 7.20.5
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 29.6.3(@babel/core@7.24.7)
      chalk: 4.1.2
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-istanbul@6.1.1:
    dependencies:
      "@babel/helper-plugin-utils": 7.24.7
      "@istanbuljs/load-nyc-config": 1.1.0
      "@istanbuljs/schema": 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-jest-hoist@29.6.3:
    dependencies:
      "@babel/template": 7.24.7
      "@babel/types": 7.24.7
      "@types/babel__core": 7.20.5
      "@types/babel__traverse": 7.20.6

  babel-preset-current-node-syntax@1.0.1(@babel/core@7.24.7):
    dependencies:
      "@babel/core": 7.24.7
      "@babel/plugin-syntax-async-generators": 7.8.4(@babel/core@7.24.7)
      "@babel/plugin-syntax-bigint": 7.8.3(@babel/core@7.24.7)
      "@babel/plugin-syntax-class-properties": 7.12.13(@babel/core@7.24.7)
      "@babel/plugin-syntax-import-meta": 7.10.4(@babel/core@7.24.7)
      "@babel/plugin-syntax-json-strings": 7.8.3(@babel/core@7.24.7)
      "@babel/plugin-syntax-logical-assignment-operators": 7.10.4(@babel/core@7.24.7)
      "@babel/plugin-syntax-nullish-coalescing-operator": 7.8.3(@babel/core@7.24.7)
      "@babel/plugin-syntax-numeric-separator": 7.10.4(@babel/core@7.24.7)
      "@babel/plugin-syntax-object-rest-spread": 7.8.3(@babel/core@7.24.7)
      "@babel/plugin-syntax-optional-catch-binding": 7.8.3(@babel/core@7.24.7)
      "@babel/plugin-syntax-optional-chaining": 7.8.3(@babel/core@7.24.7)
      "@babel/plugin-syntax-top-level-await": 7.14.5(@babel/core@7.24.7)

  babel-preset-jest@29.6.3(@babel/core@7.24.7):
    dependencies:
      "@babel/core": 7.24.7
      babel-plugin-jest-hoist: 29.6.3
      babel-preset-current-node-syntax: 1.0.1(@babel/core@7.24.7)

  balanced-match@1.0.2: {}

  bare-events@2.4.2:
    optional: true

  bare-fs@2.3.1:
    dependencies:
      bare-events: 2.4.2
      bare-path: 2.1.3
      bare-stream: 2.1.3
    optional: true

  bare-os@2.4.0:
    optional: true

  bare-path@2.1.3:
    dependencies:
      bare-os: 2.4.0
    optional: true

  bare-stream@2.1.3:
    dependencies:
      streamx: 2.18.0
    optional: true

  base64-js@1.5.1: {}

  bcrypt-pbkdf@1.0.2:
    dependencies:
      tweetnacl: 0.14.5

  bcrypt@5.1.1:
    dependencies:
      "@mapbox/node-pre-gyp": 1.0.11
      node-addon-api: 5.1.0
    transitivePeerDependencies:
      - encoding
      - supports-color

  binary-extensions@2.3.0: {}

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  bowser@2.11.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.23.1:
    dependencies:
      caniuse-lite: 1.0.30001633
      electron-to-chromium: 1.4.802
      node-releases: 2.0.14
      update-browserslist-db: 1.0.16(browserslist@4.23.1)

  bs-logger@0.2.6:
    dependencies:
      fast-json-stable-stringify: 2.1.0

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buffer-crc32@0.2.13: {}

  buffer-from@1.1.2: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buildcheck@0.0.6:
    optional: true

  bundle-require@4.2.1(esbuild@0.19.12):
    dependencies:
      esbuild: 0.19.12
      load-tsconfig: 0.2.5

  byline@5.0.0: {}

  cac@6.7.14: {}

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  call-me-maybe@1.0.2: {}

  callsites@3.1.0: {}

  camel-case@4.1.2:
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.6.3

  camelcase-keys@6.2.2:
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001633: {}

  capital-case@1.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3
      upper-case-first: 2.0.2

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  change-case@4.1.2:
    dependencies:
      camel-case: 4.1.2
      capital-case: 1.0.4
      constant-case: 3.0.4
      dot-case: 3.0.4
      header-case: 2.0.4
      no-case: 3.0.4
      param-case: 3.0.4
      pascal-case: 3.1.2
      path-case: 3.0.4
      sentence-case: 3.0.4
      snake-case: 3.0.4
      tslib: 2.6.3

  char-regex@1.0.2: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chownr@1.1.4: {}

  chownr@2.0.0: {}

  chromadb-default-embed@2.14.0:
    dependencies:
      "@huggingface/jinja": 0.1.3
      onnxruntime-web: 1.14.0
      sharp: 0.32.6
    optionalDependencies:
      onnxruntime-node: 1.14.0

  ci-info@3.9.0: {}

  cjs-module-lexer@1.3.1: {}

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  co@4.6.0: {}

  cohere-ai@7.10.5(@aws-sdk/client-sso-oidc@3.596.0):
    dependencies:
      "@aws-sdk/client-sagemaker": 3.596.0
      "@aws-sdk/credential-providers": 3.596.0(@aws-sdk/client-sso-oidc@3.596.0)
      "@aws-sdk/protocol-http": 3.374.0
      "@aws-sdk/signature-v4": 3.374.0
      form-data: 4.0.0
      form-data-encoder: 4.0.2
      formdata-node: 6.0.3
      js-base64: 3.7.2
      node-fetch: 2.7.0
      qs: 6.11.2
      readable-stream: 4.5.2
      url-join: 4.0.1
      web-streams-polyfill: 4.0.0
    transitivePeerDependencies:
      - "@aws-sdk/client-sso-oidc"
      - aws-crt
      - encoding

  collect-v8-coverage@1.0.2: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color-support@1.1.3: {}

  color@4.2.3:
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@4.1.1: {}

  compress-commons@4.1.2:
    dependencies:
      buffer-crc32: 0.2.13
      crc32-stream: 4.0.3
      normalize-path: 3.0.0
      readable-stream: 3.6.2

  concat-map@0.0.1: {}

  console-control-strings@1.1.0: {}

  constant-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3
      upper-case: 2.0.2

  convert-source-map@2.0.0: {}

  core-util-is@1.0.3: {}

  cpu-features@0.0.10:
    dependencies:
      buildcheck: 0.0.6
      nan: 2.20.0
    optional: true

  crc-32@1.2.2: {}

  crc32-stream@4.0.3:
    dependencies:
      crc-32: 1.2.2
      readable-stream: 3.6.2

  create-jest@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)):
    dependencies:
      "@jest/types": 29.6.3
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-config: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      jest-util: 29.7.0
      prompts: 2.4.2
    transitivePeerDependencies:
      - "@types/node"
      - babel-plugin-macros
      - supports-color
      - ts-node

  create-require@1.1.1: {}

  cross-spawn@6.0.5:
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  data-view-buffer@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-offset@1.0.0:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  debug@4.3.5:
    dependencies:
      ms: 2.1.2

  decamelize-keys@1.1.1:
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1

  decamelize@1.2.0: {}

  decompress-response@6.0.0:
    dependencies:
      mimic-response: 3.1.0

  dedent@1.5.3: {}

  deep-extend@0.6.0: {}

  deepmerge@4.3.1: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  delegates@1.0.0: {}

  detect-libc@2.0.3: {}

  detect-newline@3.1.0: {}

  diff-sequences@29.6.3: {}

  diff@4.0.2: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  docker-compose@0.24.8:
    dependencies:
      yaml: 2.4.5

  docker-modem@3.0.8:
    dependencies:
      debug: 4.3.5
      readable-stream: 3.6.2
      split-ca: 1.0.1
      ssh2: 1.15.0
    transitivePeerDependencies:
      - supports-color

  dockerode@3.3.5:
    dependencies:
      "@balena/dockerignore": 1.0.2
      docker-modem: 3.0.8
      tar-fs: 2.0.1
    transitivePeerDependencies:
      - supports-color

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3

  eastasianwidth@0.2.0: {}

  electron-to-chromium@1.4.802: {}

  emittery@0.13.1: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-abstract@1.23.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      arraybuffer.prototype.slice: 1.0.3
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      data-view-buffer: 1.0.1
      data-view-byte-length: 1.0.1
      data-view-byte-offset: 1.0.0
      es-define-property: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.0.3
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.6
      get-intrinsic: 1.2.4
      get-symbol-description: 1.0.2
      globalthis: 1.0.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
      internal-slot: 1.0.7
      is-array-buffer: 3.0.4
      is-callable: 1.2.7
      is-data-view: 1.0.1
      is-negative-zero: 2.0.3
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      is-string: 1.0.7
      is-typed-array: 1.1.13
      is-weakref: 1.0.2
      object-inspect: 1.13.1
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      safe-array-concat: 1.1.2
      safe-regex-test: 1.0.3
      string.prototype.trim: 1.2.9
      string.prototype.trimend: 1.0.8
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.2
      typed-array-byte-length: 1.0.1
      typed-array-byte-offset: 1.0.2
      typed-array-length: 1.0.6
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.15

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  es-object-atoms@1.0.0:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.0.3:
    dependencies:
      get-intrinsic: 1.2.4
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-to-primitive@1.2.1:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4

  esbuild@0.19.12:
    optionalDependencies:
      "@esbuild/aix-ppc64": 0.19.12
      "@esbuild/android-arm": 0.19.12
      "@esbuild/android-arm64": 0.19.12
      "@esbuild/android-x64": 0.19.12
      "@esbuild/darwin-arm64": 0.19.12
      "@esbuild/darwin-x64": 0.19.12
      "@esbuild/freebsd-arm64": 0.19.12
      "@esbuild/freebsd-x64": 0.19.12
      "@esbuild/linux-arm": 0.19.12
      "@esbuild/linux-arm64": 0.19.12
      "@esbuild/linux-ia32": 0.19.12
      "@esbuild/linux-loong64": 0.19.12
      "@esbuild/linux-mips64el": 0.19.12
      "@esbuild/linux-ppc64": 0.19.12
      "@esbuild/linux-riscv64": 0.19.12
      "@esbuild/linux-s390x": 0.19.12
      "@esbuild/linux-x64": 0.19.12
      "@esbuild/netbsd-x64": 0.19.12
      "@esbuild/openbsd-x64": 0.19.12
      "@esbuild/sunos-x64": 0.19.12
      "@esbuild/win32-arm64": 0.19.12
      "@esbuild/win32-ia32": 0.19.12
      "@esbuild/win32-x64": 0.19.12

  escalade@3.1.2: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@2.0.0: {}

  eslint-formatter-pretty@4.1.0:
    dependencies:
      "@types/eslint": 7.29.0
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      eslint-rule-docs: 1.1.235
      log-symbols: 4.1.0
      plur: 4.0.0
      string-width: 4.2.3
      supports-hyperlinks: 2.3.0

  eslint-rule-docs@1.1.235: {}

  esprima@4.0.1: {}

  event-target-shim@5.0.1: {}

  events@3.3.0: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exit@0.1.2: {}

  expand-template@2.0.3: {}

  expect@29.7.0:
    dependencies:
      "@jest/expect-utils": 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0

  fast-deep-equal@3.1.3: {}

  fast-fifo@1.3.2: {}

  fast-glob@3.3.2:
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      "@nodelib/fs.walk": 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.7

  fast-json-stable-stringify@2.1.0: {}

  fast-xml-parser@4.2.5:
    dependencies:
      strnum: 1.0.5

  fastq@1.17.1:
    dependencies:
      reusify: 1.0.4

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  flatbuffers@1.12.0: {}

  for-each@0.3.3:
    dependencies:
      is-callable: 1.2.7

  foreground-child@3.2.0:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0

  form-data-encoder@1.7.2: {}

  form-data-encoder@4.0.2: {}

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  formdata-node@4.4.1:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 4.0.0-beta.3

  formdata-node@6.0.3: {}

  fs-constants@1.0.0: {}

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      functions-have-names: 1.2.3

  functions-have-names@1.2.3: {}

  gauge@3.0.2:
    dependencies:
      aproba: 2.0.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      object-assign: 4.1.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  get-package-type@0.1.0: {}

  get-port@5.1.1: {}

  get-stream@6.0.1: {}

  get-symbol-description@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4

  getopts@2.3.0: {}

  github-from-package@0.0.0: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.4.1:
    dependencies:
      foreground-child: 3.2.0
      jackspeak: 3.4.0
      minimatch: 9.0.4
      minipass: 7.1.2
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.0.1

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.1
      merge2: 1.4.1
      slash: 3.0.0

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  graceful-fs@4.2.11: {}

  guid-typescript@1.0.9: {}

  handlebars@4.7.8:
    dependencies:
      minimist: 1.2.8
      neo-async: 2.6.2
      source-map: 0.6.1
      wordwrap: 1.0.0
    optionalDependencies:
      uglify-js: 3.18.0

  hard-rejection@2.1.0: {}

  has-bigints@1.0.2: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.0.3

  has-unicode@2.0.1: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  header-case@2.0.4:
    dependencies:
      capital-case: 1.0.4
      tslib: 2.6.3

  hosted-git-info@2.8.9: {}

  hosted-git-info@4.1.0:
    dependencies:
      lru-cache: 6.0.0

  html-escaper@2.0.2: {}

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.5
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  ieee754@1.2.1: {}

  ignore@5.3.1: {}

  import-local@3.1.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@1.3.8: {}

  internal-slot@1.0.7:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.0.6

  irregular-plurals@3.5.0: {}

  is-array-buffer@3.0.4:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  is-arrayish@0.2.1: {}

  is-arrayish@0.3.2: {}

  is-bigint@1.0.4:
    dependencies:
      has-bigints: 1.0.2

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.1.2:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.13.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.1:
    dependencies:
      is-typed-array: 1.1.13

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.2

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-generator-fn@2.1.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-negative-zero@2.0.3: {}

  is-number-object@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-plain-obj@1.1.0: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-shared-array-buffer@1.0.3:
    dependencies:
      call-bind: 1.0.7

  is-stream@2.0.1: {}

  is-string@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-symbol@1.0.4:
    dependencies:
      has-symbols: 1.0.3

  is-typed-array@1.1.13:
    dependencies:
      which-typed-array: 1.1.15

  is-unicode-supported@0.1.0: {}

  is-weakref@1.0.2:
    dependencies:
      call-bind: 1.0.7

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isomorphic-fetch@3.0.0:
    dependencies:
      node-fetch: 2.7.0
      whatwg-fetch: 3.6.20
    transitivePeerDependencies:
      - encoding

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-instrument@5.2.1:
    dependencies:
      "@babel/core": 7.24.7
      "@babel/parser": 7.24.7
      "@istanbuljs/schema": 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-instrument@6.0.2:
    dependencies:
      "@babel/core": 7.24.7
      "@babel/parser": 7.24.7
      "@istanbuljs/schema": 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.3.5
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jackspeak@3.4.0:
    dependencies:
      "@isaacs/cliui": 8.0.2
    optionalDependencies:
      "@pkgjs/parseargs": 0.11.0

  jest-changed-files@29.7.0:
    dependencies:
      execa: 5.1.1
      jest-util: 29.7.0
      p-limit: 3.1.0

  jest-circus@29.7.0:
    dependencies:
      "@jest/environment": 29.7.0
      "@jest/expect": 29.7.0
      "@jest/test-result": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      chalk: 4.1.2
      co: 4.6.0
      dedent: 1.5.3
      is-generator-fn: 2.1.0
      jest-each: 29.7.0
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      p-limit: 3.1.0
      pretty-format: 29.7.0
      pure-rand: 6.1.0
      slash: 3.0.0
      stack-utils: 2.0.6
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-cli@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)):
    dependencies:
      "@jest/core": 29.7.0(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      "@jest/test-result": 29.7.0
      "@jest/types": 29.6.3
      chalk: 4.1.2
      create-jest: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      exit: 0.1.2
      import-local: 3.1.0
      jest-config: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      jest-util: 29.7.0
      jest-validate: 29.7.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - "@types/node"
      - babel-plugin-macros
      - supports-color
      - ts-node

  jest-config@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)):
    dependencies:
      "@babel/core": 7.24.7
      "@jest/test-sequencer": 29.7.0
      "@jest/types": 29.6.3
      babel-jest: 29.7.0(@babel/core@7.24.7)
      chalk: 4.1.2
      ci-info: 3.9.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-circus: 29.7.0
      jest-environment-node: 29.7.0
      jest-get-type: 29.6.3
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-runner: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      micromatch: 4.0.7
      parse-json: 5.2.0
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-json-comments: 3.1.1
    optionalDependencies:
      "@types/node": 20.14.2
      ts-node: 10.9.2(@types/node@20.14.2)(typescript@5.4.5)
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-docblock@29.7.0:
    dependencies:
      detect-newline: 3.1.0

  jest-each@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      chalk: 4.1.2
      jest-get-type: 29.6.3
      jest-util: 29.7.0
      pretty-format: 29.7.0

  jest-environment-node@29.7.0:
    dependencies:
      "@jest/environment": 29.7.0
      "@jest/fake-timers": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      jest-mock: 29.7.0
      jest-util: 29.7.0

  jest-get-type@29.6.3: {}

  jest-haste-map@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      "@types/graceful-fs": 4.1.9
      "@types/node": 20.14.2
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      jest-worker: 29.7.0
      micromatch: 4.0.7
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.3

  jest-leak-detector@29.7.0:
    dependencies:
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-matcher-utils@29.7.0:
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-message-util@29.7.0:
    dependencies:
      "@babel/code-frame": 7.24.7
      "@jest/types": 29.6.3
      "@types/stack-utils": 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.7
      pretty-format: 29.7.0
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-mock@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      jest-util: 29.7.0

  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    optionalDependencies:
      jest-resolve: 29.7.0

  jest-regex-util@29.6.3: {}

  jest-resolve-dependencies@29.7.0:
    dependencies:
      jest-regex-util: 29.6.3
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  jest-resolve@29.7.0:
    dependencies:
      chalk: 4.1.2
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-pnp-resolver: 1.2.3(jest-resolve@29.7.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      resolve: 1.22.8
      resolve.exports: 2.0.2
      slash: 3.0.0

  jest-runner@29.7.0:
    dependencies:
      "@jest/console": 29.7.0
      "@jest/environment": 29.7.0
      "@jest/test-result": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      chalk: 4.1.2
      emittery: 0.13.1
      graceful-fs: 4.2.11
      jest-docblock: 29.7.0
      jest-environment-node: 29.7.0
      jest-haste-map: 29.7.0
      jest-leak-detector: 29.7.0
      jest-message-util: 29.7.0
      jest-resolve: 29.7.0
      jest-runtime: 29.7.0
      jest-util: 29.7.0
      jest-watcher: 29.7.0
      jest-worker: 29.7.0
      p-limit: 3.1.0
      source-map-support: 0.5.13
    transitivePeerDependencies:
      - supports-color

  jest-runtime@29.7.0:
    dependencies:
      "@jest/environment": 29.7.0
      "@jest/fake-timers": 29.7.0
      "@jest/globals": 29.7.0
      "@jest/source-map": 29.6.3
      "@jest/test-result": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      chalk: 4.1.2
      cjs-module-lexer: 1.3.1
      collect-v8-coverage: 1.0.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color

  jest-snapshot@29.7.0:
    dependencies:
      "@babel/core": 7.24.7
      "@babel/generator": 7.24.7
      "@babel/plugin-syntax-jsx": 7.24.7(@babel/core@7.24.7)
      "@babel/plugin-syntax-typescript": 7.24.7(@babel/core@7.24.7)
      "@babel/types": 7.24.7
      "@jest/expect-utils": 29.7.0
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      babel-preset-current-node-syntax: 1.0.1(@babel/core@7.24.7)
      chalk: 4.1.2
      expect: 29.7.0
      graceful-fs: 4.2.11
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      natural-compare: 1.4.0
      pretty-format: 29.7.0
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color

  jest-util@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-validate@29.7.0:
    dependencies:
      "@jest/types": 29.6.3
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 29.6.3
      leven: 3.1.0
      pretty-format: 29.7.0

  jest-watcher@29.7.0:
    dependencies:
      "@jest/test-result": 29.7.0
      "@jest/types": 29.6.3
      "@types/node": 20.14.2
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.13.1
      jest-util: 29.7.0
      string-length: 4.0.2

  jest-worker@29.7.0:
    dependencies:
      "@types/node": 20.14.2
      jest-util: 29.7.0
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)):
    dependencies:
      "@jest/core": 29.7.0(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      "@jest/types": 29.6.3
      import-local: 3.1.0
      jest-cli: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
    transitivePeerDependencies:
      - "@types/node"
      - babel-plugin-macros
      - supports-color
      - ts-node

  joycon@3.1.1: {}

  js-base64@3.7.2: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@2.5.2: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@1.0.0: {}

  json5@2.2.3: {}

  kind-of@6.0.3: {}

  kleur@3.0.3: {}

  lazystream@1.0.1:
    dependencies:
      readable-stream: 2.3.8

  leven@3.1.0: {}

  lilconfig@3.1.2: {}

  lines-and-columns@1.2.4: {}

  load-json-file@4.0.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0

  load-tsconfig@0.2.5: {}

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  lodash.defaults@4.2.0: {}

  lodash.difference@4.5.0: {}

  lodash.flatten@4.4.0: {}

  lodash.isplainobject@4.0.6: {}

  lodash.memoize@4.1.2: {}

  lodash.union@4.6.0: {}

  lodash@4.17.21: {}

  log-symbols@4.1.0:
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0

  long@4.0.0: {}

  lower-case@2.0.2:
    dependencies:
      tslib: 2.6.3

  lru-cache@10.2.2: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.1

  make-error@1.3.6: {}

  makeerror@1.0.12:
    dependencies:
      tmpl: 1.0.5

  map-obj@1.0.1: {}

  map-obj@4.3.0: {}

  marked@12.0.2: {}

  memorystream@0.3.1: {}

  meow@9.0.0:
    dependencies:
      "@types/minimist": 1.2.5
      camelcase-keys: 6.2.2
      decamelize: 1.2.0
      decamelize-keys: 1.1.1
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 3.0.3
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.18.1
      yargs-parser: 20.2.9

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.7:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@2.1.0: {}

  mimic-response@3.1.0: {}

  min-indent@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.4:
    dependencies:
      brace-expansion: 2.0.1

  minimist-options@4.1.0:
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3

  minimist@1.2.8: {}

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0

  minipass@5.0.0: {}

  minipass@7.1.2: {}

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0

  mkdirp-classic@0.5.3: {}

  mkdirp@1.0.4: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nan@2.20.0:
    optional: true

  nanoid@3.3.8:
    optional: true

  napi-build-utils@2.0.0: {}

  natural-compare@1.4.0: {}

  neo-async@2.6.2: {}

  nice-try@1.0.5: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.6.3

  node-abi@3.74.0:
    dependencies:
      semver: 7.7.1

  node-addon-api@5.1.0: {}

  node-addon-api@6.1.0: {}

  node-domexception@1.0.0: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 13.0.0

  node-int64@0.4.0: {}

  node-releases@2.0.14: {}

  node-watch@0.7.4: {}

  nopt@5.0.0:
    dependencies:
      abbrev: 1.1.1

  normalize-package-data@2.5.0:
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.8
      semver: 5.7.2
      validate-npm-package-license: 3.0.4

  normalize-package-data@3.0.3:
    dependencies:
      hosted-git-info: 4.1.0
      is-core-module: 2.13.1
      semver: 7.7.1
      validate-npm-package-license: 3.0.4

  normalize-path@3.0.0: {}

  npm-run-all@4.1.5:
    dependencies:
      ansi-styles: 3.2.1
      chalk: 2.4.2
      cross-spawn: 6.0.5
      memorystream: 0.3.1
      minimatch: 3.1.2
      pidtree: 0.3.1
      read-pkg: 3.0.0
      shell-quote: 1.8.1
      string.prototype.padend: 3.1.6

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npmlog@5.0.1:
    dependencies:
      are-we-there-yet: 2.0.0
      console-control-strings: 1.1.0
      gauge: 3.0.2
      set-blocking: 2.0.0

  object-assign@4.1.1: {}

  object-inspect@1.13.1: {}

  object-keys@1.1.1: {}

  object.assign@4.1.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1

  ollama@0.5.12:
    dependencies:
      whatwg-fetch: 3.6.20

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  onnx-proto@4.0.4:
    dependencies:
      protobufjs: 6.11.4

  onnxruntime-common@1.14.0: {}

  onnxruntime-node@1.14.0:
    dependencies:
      onnxruntime-common: 1.14.0
    optional: true

  onnxruntime-web@1.14.0:
    dependencies:
      flatbuffers: 1.12.0
      guid-typescript: 1.0.9
      long: 4.0.0
      onnx-proto: 4.0.4
      onnxruntime-common: 1.14.0
      platform: 1.3.6

  openai@4.51.0:
    dependencies:
      "@types/node": 18.19.34
      "@types/node-fetch": 2.6.11
      abort-controller: 3.0.0
      agentkeepalive: 4.5.0
      form-data-encoder: 1.7.2
      formdata-node: 4.4.1
      node-fetch: 2.7.0
      web-streams-polyfill: 3.3.3
    transitivePeerDependencies:
      - encoding

  openapi-generator-plus@2.20.0(@openapi-generator-plus/core@2.23.0(openapi-types@12.1.3)):
    dependencies:
      "@openapi-generator-plus/core": 2.23.0(openapi-types@12.1.3)
      ansi-colors: 4.1.3
      getopts: 2.3.0
      glob: 10.4.1
      node-fetch: 2.7.0
      node-watch: 0.7.4
      yaml: 2.4.5
    transitivePeerDependencies:
      - encoding

  openapi-types@12.1.3: {}

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-try@2.2.0: {}

  param-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.6.3

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      "@babel/code-frame": 7.24.7
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3

  path-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.6.3

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@2.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.2.2
      minipass: 7.1.2

  path-type@3.0.0:
    dependencies:
      pify: 3.0.0

  path-type@4.0.0: {}

  picocolors@1.0.1: {}

  picocolors@1.1.1:
    optional: true

  picomatch@2.3.1: {}

  pidtree@0.3.1: {}

  pify@3.0.0: {}

  pirates@4.0.6: {}

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  platform@1.3.6: {}

  plur@4.0.0:
    dependencies:
      irregular-plurals: 3.5.0

  pluralize@8.0.0: {}

  possible-typed-array-names@1.0.0: {}

  postcss-load-config@4.0.2(postcss@8.5.3)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)):
    dependencies:
      lilconfig: 3.1.2
      yaml: 2.4.5
    optionalDependencies:
      postcss: 8.5.3
      ts-node: 10.9.2(@types/node@20.14.2)(typescript@5.4.5)

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1
    optional: true

  prebuild-install@7.1.3:
    dependencies:
      detect-libc: 2.0.3
      expand-template: 2.0.3
      github-from-package: 0.0.0
      minimist: 1.2.8
      mkdirp-classic: 0.5.3
      napi-build-utils: 2.0.0
      node-abi: 3.74.0
      pump: 3.0.0
      rc: 1.2.8
      simple-get: 4.0.1
      tar-fs: 2.0.1
      tunnel-agent: 0.6.0

  prettier@2.8.7: {}

  pretty-format@29.7.0:
    dependencies:
      "@jest/schemas": 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  process-nextick-args@2.0.1: {}

  process@0.11.10: {}

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  proper-lockfile@4.1.2:
    dependencies:
      graceful-fs: 4.2.11
      retry: 0.12.0
      signal-exit: 3.0.7

  properties-reader@2.3.0:
    dependencies:
      mkdirp: 1.0.4

  protobufjs@6.11.4:
    dependencies:
      "@protobufjs/aspromise": 1.1.2
      "@protobufjs/base64": 1.1.2
      "@protobufjs/codegen": 2.0.4
      "@protobufjs/eventemitter": 1.1.0
      "@protobufjs/fetch": 1.1.0
      "@protobufjs/float": 1.0.2
      "@protobufjs/inquire": 1.1.0
      "@protobufjs/path": 1.1.2
      "@protobufjs/pool": 1.1.0
      "@protobufjs/utf8": 1.1.0
      "@types/long": 4.0.2
      "@types/node": 20.14.2
      long: 4.0.0

  pump@3.0.0:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode@2.3.1: {}

  pure-rand@6.1.0: {}

  qs@6.11.2:
    dependencies:
      side-channel: 1.0.6

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  queue-tick@1.0.1: {}

  quick-lru@4.0.1: {}

  rc@1.2.8:
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1

  react-is@18.3.1: {}

  read-pkg-up@7.0.1:
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1

  read-pkg@3.0.0:
    dependencies:
      load-json-file: 4.0.0
      normalize-package-data: 2.5.0
      path-type: 3.0.0

  read-pkg@5.2.0:
    dependencies:
      "@types/normalize-package-data": 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readable-stream@4.5.2:
    dependencies:
      abort-controller: 3.0.0
      buffer: 6.0.3
      events: 3.3.0
      process: 0.11.10
      string_decoder: 1.3.0

  readdir-glob@1.1.3:
    dependencies:
      minimatch: 5.1.6

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  regexp.prototype.flags@1.5.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  requires-port@1.0.0: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@5.0.0: {}

  resolve.exports@2.0.2: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  retry@0.12.0: {}

  reusify@1.0.4: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rimraf@5.0.7:
    dependencies:
      glob: 10.4.1

  rollup@4.18.0:
    dependencies:
      "@types/estree": 1.0.5
    optionalDependencies:
      "@rollup/rollup-android-arm-eabi": 4.18.0
      "@rollup/rollup-android-arm64": 4.18.0
      "@rollup/rollup-darwin-arm64": 4.18.0
      "@rollup/rollup-darwin-x64": 4.18.0
      "@rollup/rollup-linux-arm-gnueabihf": 4.18.0
      "@rollup/rollup-linux-arm-musleabihf": 4.18.0
      "@rollup/rollup-linux-arm64-gnu": 4.18.0
      "@rollup/rollup-linux-arm64-musl": 4.18.0
      "@rollup/rollup-linux-powerpc64le-gnu": 4.18.0
      "@rollup/rollup-linux-riscv64-gnu": 4.18.0
      "@rollup/rollup-linux-s390x-gnu": 4.18.0
      "@rollup/rollup-linux-x64-gnu": 4.18.0
      "@rollup/rollup-linux-x64-musl": 4.18.0
      "@rollup/rollup-win32-arm64-msvc": 4.18.0
      "@rollup/rollup-win32-ia32-msvc": 4.18.0
      "@rollup/rollup-win32-x64-msvc": 4.18.0
      fsevents: 2.3.3

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-array-concat@1.1.2:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      isarray: 2.0.5

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-regex-test@1.0.3:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-regex: 1.1.4

  safer-buffer@2.1.2: {}

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.7.1: {}

  sentence-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.6.3
      upper-case-first: 2.0.2

  set-blocking@2.0.0: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  sharp@0.32.6:
    dependencies:
      color: 4.2.3
      detect-libc: 2.0.3
      node-addon-api: 6.1.0
      prebuild-install: 7.1.3
      semver: 7.7.1
      simple-get: 4.0.1
      tar-fs: 3.0.6
      tunnel-agent: 0.6.0

  shebang-command@1.2.0:
    dependencies:
      shebang-regex: 1.0.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@1.0.0: {}

  shebang-regex@3.0.0: {}

  shell-quote@1.8.1: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.1

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  simple-concat@1.0.1: {}

  simple-get@4.0.1:
    dependencies:
      decompress-response: 6.0.0
      once: 1.4.0
      simple-concat: 1.0.1

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  sisteransi@1.0.5: {}

  slash@3.0.0: {}

  snake-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.6.3

  source-map-js@1.2.1:
    optional: true

  source-map-support@0.5.13:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.8.0-beta.0:
    dependencies:
      whatwg-url: 13.0.0

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.18

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.18

  spdx-license-ids@3.0.18: {}

  split-ca@1.0.1: {}

  sprintf-js@1.0.3: {}

  ssh-remote-port-forward@1.0.4:
    dependencies:
      "@types/ssh2": 0.5.52
      ssh2: 1.15.0

  ssh2@1.15.0:
    dependencies:
      asn1: 0.2.6
      bcrypt-pbkdf: 1.0.2
    optionalDependencies:
      cpu-features: 0.0.10
      nan: 2.20.0

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  streamx@2.18.0:
    dependencies:
      fast-fifo: 1.3.2
      queue-tick: 1.0.1
      text-decoder: 1.1.1
    optionalDependencies:
      bare-events: 2.4.2

  string-length@4.0.2:
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.padend@3.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trim@1.2.9:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trimend@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.0.1

  strip-bom@3.0.0: {}

  strip-bom@4.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@2.0.1: {}

  strip-json-comments@3.1.1: {}

  strnum@1.0.5: {}

  sucrase@3.35.0:
    dependencies:
      "@jridgewell/gen-mapping": 0.3.5
      commander: 4.1.1
      glob: 10.4.1
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-hyperlinks@2.3.0:
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0

  supports-preserve-symlinks-flag@1.0.0: {}

  tar-fs@2.0.1:
    dependencies:
      chownr: 1.1.4
      mkdirp-classic: 0.5.3
      pump: 3.0.0
      tar-stream: 2.2.0

  tar-fs@3.0.6:
    dependencies:
      pump: 3.0.0
      tar-stream: 3.1.7
    optionalDependencies:
      bare-fs: 2.3.1
      bare-path: 2.1.3

  tar-stream@2.2.0:
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.4
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2

  tar-stream@3.1.7:
    dependencies:
      b4a: 1.6.6
      fast-fifo: 1.3.2
      streamx: 2.18.0

  tar@6.2.1:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0

  test-exclude@6.0.0:
    dependencies:
      "@istanbuljs/schema": 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  testcontainers@10.10.3:
    dependencies:
      "@balena/dockerignore": 1.0.2
      "@types/dockerode": 3.3.29
      archiver: 5.3.2
      async-lock: 1.4.1
      byline: 5.0.0
      debug: 4.3.5
      docker-compose: 0.24.8
      dockerode: 3.3.5
      get-port: 5.1.1
      node-fetch: 2.7.0
      proper-lockfile: 4.1.2
      properties-reader: 2.3.0
      ssh-remote-port-forward: 1.0.4
      tar-fs: 3.0.6
      tmp: 0.2.3
    transitivePeerDependencies:
      - encoding
      - supports-color

  text-decoder@1.1.1:
    dependencies:
      b4a: 1.6.6

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  tmp@0.2.3: {}

  tmpl@1.0.5: {}

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  tr46@4.1.1:
    dependencies:
      punycode: 2.3.1

  tree-kill@1.2.2: {}

  trim-newlines@3.0.1: {}

  ts-interface-checker@0.1.13: {}

  ts-jest@29.1.4(@babel/core@7.24.7)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.24.7))(esbuild@0.19.12)(jest@29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5)))(typescript@5.4.5):
    dependencies:
      bs-logger: 0.2.6
      fast-json-stable-stringify: 2.1.0
      jest: 29.7.0(@types/node@20.14.2)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      jest-util: 29.7.0
      json5: 2.2.3
      lodash.memoize: 4.1.2
      make-error: 1.3.6
      semver: 7.7.1
      typescript: 5.4.5
      yargs-parser: 21.1.1
    optionalDependencies:
      "@babel/core": 7.24.7
      "@jest/transform": 29.7.0
      "@jest/types": 29.6.3
      babel-jest: 29.7.0(@babel/core@7.24.7)
      esbuild: 0.19.12

  ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5):
    dependencies:
      "@cspotcode/source-map-support": 0.8.1
      "@tsconfig/node10": 1.0.11
      "@tsconfig/node12": 1.0.11
      "@tsconfig/node14": 1.0.3
      "@tsconfig/node16": 1.0.4
      "@types/node": 20.14.2
      acorn: 8.11.3
      acorn-walk: 8.3.2
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 5.4.5
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  tsd@0.28.1:
    dependencies:
      "@tsd/typescript": 5.0.4
      eslint-formatter-pretty: 4.1.0
      globby: 11.1.0
      jest-diff: 29.7.0
      meow: 9.0.0
      path-exists: 4.0.0
      read-pkg-up: 7.0.1

  tslib@1.14.1: {}

  tslib@2.6.3: {}

  tsup@7.3.0(postcss@8.5.3)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))(typescript@5.4.5):
    dependencies:
      bundle-require: 4.2.1(esbuild@0.19.12)
      cac: 6.7.14
      chokidar: 3.6.0
      debug: 4.3.5
      esbuild: 0.19.12
      execa: 5.1.1
      globby: 11.1.0
      joycon: 3.1.1
      postcss-load-config: 4.0.2(postcss@8.5.3)(ts-node@10.9.2(@types/node@20.14.2)(typescript@5.4.5))
      resolve-from: 5.0.0
      rollup: 4.18.0
      source-map: 0.8.0-beta.0
      sucrase: 3.35.0
      tree-kill: 1.2.2
    optionalDependencies:
      postcss: 8.5.3
      typescript: 5.4.5
    transitivePeerDependencies:
      - supports-color
      - ts-node

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  tweetnacl@0.14.5: {}

  type-detect@4.0.8: {}

  type-fest@0.18.1: {}

  type-fest@0.21.3: {}

  type-fest@0.6.0: {}

  type-fest@0.8.1: {}

  typed-array-buffer@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-typed-array: 1.1.13

  typed-array-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-byte-offset@1.0.2:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-length@1.0.6:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
      possible-typed-array-names: 1.0.0

  typescript@5.4.5: {}

  uglify-js@3.18.0:
    optional: true

  unbox-primitive@1.0.2:
    dependencies:
      call-bind: 1.0.7
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2

  undici-types@5.26.5: {}

  update-browserslist-db@1.0.16(browserslist@4.23.1):
    dependencies:
      browserslist: 4.23.1
      escalade: 3.1.2
      picocolors: 1.0.1

  upper-case-first@2.0.2:
    dependencies:
      tslib: 2.6.3

  upper-case@2.0.2:
    dependencies:
      tslib: 2.6.3

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-join@4.0.1: {}

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  util-deprecate@1.0.2: {}

  uuid@9.0.1: {}

  v8-compile-cache-lib@3.0.1: {}

  v8-to-istanbul@9.2.0:
    dependencies:
      "@jridgewell/trace-mapping": 0.3.25
      "@types/istanbul-lib-coverage": 2.0.6
      convert-source-map: 2.0.0

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  voyageai@0.0.3-1:
    dependencies:
      form-data: 4.0.0
      formdata-node: 6.0.3
      js-base64: 3.7.2
      node-fetch: 2.7.0
      qs: 6.11.2
      readable-stream: 4.5.2
      url-join: 4.0.1
    transitivePeerDependencies:
      - encoding

  walker@1.0.8:
    dependencies:
      makeerror: 1.0.12

  web-streams-polyfill@3.3.3: {}

  web-streams-polyfill@4.0.0: {}

  web-streams-polyfill@4.0.0-beta.3: {}

  webidl-conversions@7.0.0: {}

  whatwg-fetch@3.6.20: {}

  whatwg-url@13.0.0:
    dependencies:
      tr46: 4.1.1
      webidl-conversions: 7.0.0

  which-boxed-primitive@1.0.2:
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  which-typed-array@1.1.15:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.2

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wide-align@1.1.5:
    dependencies:
      string-width: 4.2.3

  wordwrap@1.0.0: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  write-file-atomic@4.0.2:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 3.0.7

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yaml@2.4.5: {}

  yargs-parser@20.2.9: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.1.2
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yn@3.1.1: {}

  yocto-queue@0.1.0: {}

  zip-stream@4.1.1:
    dependencies:
      archiver-utils: 3.0.4
      compress-commons: 4.1.2
      readable-stream: 3.6.2
