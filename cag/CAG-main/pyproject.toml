[project]
name = "CAG"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>"},
]
dependencies = [
    "python-dateutil>=2.9.0.post0",
    "aiosignal>=1.3.2",
    "annotated-types>=0.7.0",
    "anyio>=4.7.0",
    "async-timeout>=5.0.1",
    "attrs>=24.3.0",
    "beautifulsoup4>=4.12.3",
    "bert-score>=0.3.13",
    "bm25s>=0.2.6",
    "cachetools>=5.5.0",
    "certifi>=2024.12.14",
    "charset-normalizer>=3.4.1",
    "click>=8.1.8",
    "colorama>=0.4.6",
    "contourpy>=1.3.1",
    "cycler>=0.12.1",
    "dataclasses-json>=0.6.7",
    "Deprecated>=1.2.15",
    "dirtyjson>=1.0.8",
    "distro>=1.9.0",
    "exceptiongroup>=1.2.2",
    "filelock>=3.16.1",
    "filetype>=1.2.0",
    "fonttools>=4.55.3",
    "frozenlist>=1.5.0",
    "fsspec>=2024.12.0",
    "google-ai-generativelanguage>=0.6.14",
    "google-api-core>=2.24.0",
    "google-api-python-client>=2.156.0",
    "google-auth>=2.37.0",
    "google-auth-httplib2>=0.2.0",
    "googleapis-common-protos>=1.66.0",
    "greenlet>=3.1.1",
    "grpcio>=1.68.1",
    "grpcio-status>=1.68.1",
    "h11>=0.14.0",
    "httpcore>=1.0.7",
    "httplib2>=0.22.0",
    "httpx>=0.28.1",
    "huggingface-hub>=0.27.0",
    "idna>=3.10",
    "Jinja2>=3.1.5",
    "jiter>=0.8.2",
    "joblib>=1.4.2",
    "kiwisolver>=1.4.8",
    "llama-cloud>=0.1.7",
    "llama-index>=0.12.9",
    "llama-index-agent-openai>=0.4.1",
    "llama-index-cli>=0.4.0",
    "llama-index-core>=0.12.9",
    "llama-index-embeddings-openai>=0.3.1",
    "llama-index-indices-managed-llama-cloud>=0.6.3",
    "llama-index-legacy>=0.9.48.post4",
    "llama-index-llms-openai>=0.3.12",
    "llama-index-multi-modal-llms-openai>=0.4.2",
    "llama-index-program-openai>=0.3.1",
    "llama-index-question-gen-openai>=0.3.0",
    "llama-index-readers-file>=0.4.1",
    "llama-index-readers-llama-parse>=0.4.0",
    "llama-index-retrievers-bm25>=0.5.0",
    "llama-parse>=0.5.19",
    "MarkupSafe>=3.0.2",
    "marshmallow>=3.23.2",
    "matplotlib>=3.10.0",
    "mpmath>=1.3.0",
    "multidict>=6.1.0",
    "mypy-extensions>=1.0.0",
    "nest-asyncio>=1.6.0",
    "networkx>=3.4.2",
    "nltk>=3.9.1",
    "numpy>=2.2.1",
    "openai>=1.58.1",
    "packaging>=24.2",
    "pandas>=2.2.3",
    "pillow>=11.1.0",
    "propcache>=0.2.1",
    "proto-plus>=1.25.0",
    "protobuf>=5.29.2",
    "pyasn1>=0.6.1",
    "pyasn1-modules>=0.4.1",
    "pydantic>=2.10.4",
    "pydantic-core>=2.27.2",
    "pyparsing>=3.2.1",
    "pypdf>=5.1.0",
    "PyStemmer>=*******",
    "pytz>=2024.2",
    "PyYAML>=6.0.2",
    "regex>=2024.11.6",
    "requests>=2.32.3",
    "rsa>=4.9",
    "safetensors>=0.4.5",
    "scikit-learn>=1.6.0",
    "scipy>=1.14.1",
    "sentence-transformers>=3.3.1",
    "six>=1.17.0",
    "sniffio>=1.3.1",
    "soupsieve>=2.6",
    "SQLAlchemy>=2.0.36",
    "striprtf>=0.0.26",
    "sympy>=1.13.1",
    "tenacity>=8.5.0",
    "threadpoolctl>=3.5.0",
    "tiktoken>=0.8.0",
    "tokenizers>=0.21.0",
    "torch>=2.5.1",
    "tqdm>=4.67.1",
    "transformers>=4.47.1",
    "typing-inspect>=0.9.0",
    "typing-extensions>=4.12.2",
    "tzdata>=2024.2",
    "uritemplate>=4.1.1",
    "urllib3>=2.3.0",
    "wrapt>=1.17.0",
    "yarl>=1.18.3",
    "aiohappyeyeballs>=2.4.4",
    "python-dotenv>=1.0.1",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}


[tool.pdm]
distribution = false

[tool.pdm.scripts]
download = {cmd='sh ./downloads.sh'}
