name: Installation Issue
description: Request for install help with Chroma
title: "[Install issue]: "
labels: ["installation trouble"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this issue report!
  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Tell us what you see!
#       value: "A bug happened!"
    validations:
      required: true
  - type: textarea
    id: versions
    attributes:
      label: Versions
      description: We need your Chroma, Python, and OS versions, as well as whatever else you think relevant.
      placeholder: Chroma v0.3.14, Python 3.9.6, MacOS 12.5
#       value: "A bug happened!"
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: shell
#   - type: checkboxes
#     id: terms
#     attributes:
#       label: Code of Conduct
#       description: By submitting this issue, you agree to follow our [Code of Conduct](https://example.com)
#       options:
#         - label: I agree to follow this project's Code of Conduct
#           required: true
