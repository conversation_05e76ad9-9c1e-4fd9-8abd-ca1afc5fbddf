"""
LM Studio Orchestrator Tool for Agent Zero
Integrates LM Studio with multi-model orchestration
"""

import sys
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add lib to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "lib"))

from python.helpers.tool import Tool, Response
from python.helpers.print_style import PrintStyle


class LMStudioOrchestrator(Tool):
    """
    LM Studio Orchestrator for Agent Zero
    Connects local LM Studio models with Agent Zero's orchestration system
    """

    def __init__(self, agent=None, name="LM Studio Orchestrator", method="execute", args=None, message=""):
        super().__init__(agent, name, method, args or {}, message)

    def execute(self, **kwargs) -> Response:
        """Execute LM Studio orchestrator commands"""
        
        action = kwargs.get("action", "help")
        
        if action == "connect":
            return self._connect_lm_studio(**kwargs)
        elif action == "list_models":
            return self._list_models(**kwargs)
        elif action == "recommend_models":
            return self._recommend_models(**kwargs)
        elif action == "send_prompt":
            return self._send_prompt(**kwargs)
        elif action == "orchestrate":
            return self._orchestrate_task(**kwargs)
        elif action == "setup_3_model":
            return self._setup_3_model_system(**kwargs)
        elif action == "performance_report":
            return self._get_performance_report(**kwargs)
        else:
            return self._show_help()
    
    def _connect_lm_studio(self, **kwargs) -> Response:
        """Connect to LM Studio and check status"""
        try:
            from lib.ai.lm_studio_integration import LMStudioManager
            
            lm = LMStudioManager()
            connection = lm.check_connection()
            
            if connection["success"]:
                models = lm.get_available_models()
                
                return Response(
                    message="✅ Successfully connected to LM Studio",
                    data={
                        "connection": connection,
                        "models": models,
                        "next_steps": [
                            "Use 'list_models' to see available models",
                            "Use 'recommend_models' for task-specific recommendations",
                            "Use 'setup_3_model' for optimal orchestration setup"
                        ]
                    }
                )
            else:
                return Response(
                    message="❌ Failed to connect to LM Studio",
                    data={
                        "error": connection["error"],
                        "troubleshooting": [
                            "Make sure LM Studio is running",
                            "Check if server is started on localhost:1234",
                            "Verify at least one model is loaded"
                        ]
                    }
                )
                
        except Exception as e:
            return Response(
                message=f"❌ Connection error: {str(e)}",
                data={"error": str(e)}
            )
    
    def _list_models(self, **kwargs) -> Response:
        """List available LM Studio models"""
        try:
            from lib.ai.lm_studio_integration import LMStudioManager
            
            lm = LMStudioManager()
            models_result = lm.get_available_models()
            
            if models_result["success"]:
                models = models_result["models"]
                
                # Categorize models
                code_models = [m for m in models if "code" in m["specialization"].lower()]
                reasoning_models = [m for m in models if "reasoning" in m["specialization"].lower()]
                general_models = [m for m in models if "instruction" in m["specialization"].lower()]
                
                return Response(
                    message=f"✅ Found {len(models)} models in LM Studio",
                    data={
                        "total_models": len(models),
                        "categories": {
                            "code_models": code_models,
                            "reasoning_models": reasoning_models,
                            "general_models": general_models
                        },
                        "all_models": models
                    }
                )
            else:
                return Response(
                    message="❌ Failed to get models from LM Studio",
                    data=models_result
                )
                
        except Exception as e:
            return Response(
                message=f"❌ Error listing models: {str(e)}",
                data={"error": str(e)}
            )
    
    def _recommend_models(self, **kwargs) -> Response:
        """Get model recommendations for specific tasks"""
        try:
            from lib.ai.lm_studio_integration import LMStudioManager
            
            task_type = kwargs.get("task_type", "coding")
            
            lm = LMStudioManager()
            lm.get_available_models()
            recommendations = lm.get_model_recommendations(task_type)
            
            if recommendations:
                return Response(
                    message=f"✅ Found {len(recommendations)} recommendations for {task_type}",
                    data={
                        "task_type": task_type,
                        "recommendations": recommendations,
                        "best_model": recommendations[0] if recommendations else None,
                        "usage_tip": f"Use the top model for best {task_type} performance"
                    }
                )
            else:
                return Response(
                    message=f"❌ No suitable models found for {task_type}",
                    data={
                        "task_type": task_type,
                        "suggestion": "Try loading a model specialized for this task type"
                    }
                )
                
        except Exception as e:
            return Response(
                message=f"❌ Error getting recommendations: {str(e)}",
                data={"error": str(e)}
            )
    
    def _send_prompt(self, **kwargs) -> Response:
        """Send prompt to LM Studio model"""
        try:
            from lib.ai.lm_studio_integration import LMStudioManager
            
            prompt = kwargs.get("prompt", "")
            model_id = kwargs.get("model_id")
            max_tokens = kwargs.get("max_tokens", 2048)
            temperature = kwargs.get("temperature", 0.7)
            
            if not prompt:
                return Response(
                    message="❌ Prompt is required",
                    data={"error": "No prompt provided"}
                )
            
            lm = LMStudioManager()
            result = lm.send_prompt(prompt, model_id, max_tokens, temperature)
            
            if result["success"]:
                return Response(
                    message="✅ Prompt executed successfully",
                    data={
                        "prompt": prompt[:100] + "..." if len(prompt) > 100 else prompt,
                        "response": result["response"],
                        "model_used": result["model_used"],
                        "tokens_used": result.get("tokens_used", 0)
                    }
                )
            else:
                return Response(
                    message="❌ Prompt execution failed",
                    data=result
                )
                
        except Exception as e:
            return Response(
                message=f"❌ Error sending prompt: {str(e)}",
                data={"error": str(e)}
            )
    
    def _orchestrate_task(self, **kwargs) -> Response:
        """Orchestrate task across multiple models"""
        try:
            from lib.ai.multi_model_orchestrator import orchestrate_ai_task
            
            task = kwargs.get("task", "")
            task_type = kwargs.get("task_type", "general")
            priority = kwargs.get("priority", "balanced")
            
            if not task:
                return Response(
                    message="❌ Task is required",
                    data={"error": "No task provided"}
                )
            
            # Run orchestration
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    orchestrate_ai_task(task, task_type, {"priority": priority})
                )
                
                return Response(
                    message="✅ Task orchestration completed",
                    data={
                        "task": task,
                        "task_type": task_type,
                        "priority": priority,
                        "result": result,
                        "models_used": result.get("models_used", []),
                        "optimization": "Multi-model orchestration applied"
                    }
                )
            finally:
                loop.close()
                
        except Exception as e:
            return Response(
                message=f"❌ Orchestration error: {str(e)}",
                data={"error": str(e)}
            )
    
    def _setup_3_model_system(self, **kwargs) -> Response:
        """Setup optimal 3-model orchestration system"""
        try:
            from lib.ai.multi_model_orchestrator import MultiModelOrchestrator
            from lib.ai.lm_studio_integration import LMStudioManager
            
            orchestrator = MultiModelOrchestrator()
            recommendations = orchestrator.recommend_setup()
            
            # Check LM Studio availability
            lm = LMStudioManager()
            connection = lm.check_connection()
            models = lm.get_available_models() if connection["success"] else {"models": []}
            
            # Get specific recommendations based on available models
            available_models = models.get("models", [])
            code_models = [m for m in available_models if "code" in m["specialization"].lower()]
            reasoning_models = [m for m in available_models if "reasoning" in m["specialization"].lower()]
            
            setup_plan = {
                "model_1_coding": {
                    "provider": "LM Studio",
                    "recommended": code_models[0] if code_models else "Load CodeLlama or similar",
                    "purpose": "Fast local coding, debugging, code review",
                    "priority": "Speed + Cost efficiency"
                },
                "model_2_reasoning": {
                    "provider": "OpenRouter (DeepSeek)",
                    "recommended": "deepseek/deepseek-chat",
                    "purpose": "Complex reasoning, analysis, planning",
                    "priority": "Quality + Advanced capabilities"
                },
                "model_3_specialized": {
                    "provider": "Professional Prompts + LM Studio",
                    "recommended": reasoning_models[0] if reasoning_models else "Mistral-Instruct",
                    "purpose": "UI generation, specialized tasks with expert prompts",
                    "priority": "Specialized expertise"
                }
            }
            
            return Response(
                message="✅ 3-Model orchestration system designed",
                data={
                    "setup_plan": setup_plan,
                    "orchestration_strategy": recommendations["orchestration_strategy"],
                    "benefits": recommendations["benefits"],
                    "lm_studio_status": connection,
                    "available_local_models": len(available_models),
                    "next_steps": [
                        "Load recommended models in LM Studio",
                        "Test each model with sample tasks",
                        "Use 'orchestrate' action for automatic model selection"
                    ]
                }
            )
            
        except Exception as e:
            return Response(
                message=f"❌ Setup error: {str(e)}",
                data={"error": str(e)}
            )
    
    def _get_performance_report(self, **kwargs) -> Response:
        """Get performance report for orchestration system"""
        try:
            from lib.ai.multi_model_orchestrator import MultiModelOrchestrator
            
            orchestrator = MultiModelOrchestrator()
            report = orchestrator.get_performance_report()
            
            return Response(
                message="✅ Performance report generated",
                data=report
            )
            
        except Exception as e:
            return Response(
                message=f"❌ Report error: {str(e)}",
                data={"error": str(e)}
            )
    
    def _show_help(self) -> Response:
        """Show help information"""
        help_text = """
🚀 LM Studio Orchestrator for Agent Zero

Available Actions:
• connect - Connect to LM Studio and check status
• list_models - List all available LM Studio models
• recommend_models - Get model recommendations for specific tasks
• send_prompt - Send prompt directly to LM Studio model
• orchestrate - Orchestrate task across multiple models
• setup_3_model - Setup optimal 3-model orchestration system
• performance_report - Get performance metrics

Examples:
• action: "connect"
• action: "list_models"
• action: "recommend_models", task_type: "coding"
• action: "send_prompt", prompt: "Write a Python function", model_id: "codellama"
• action: "orchestrate", task: "Build a React app", task_type: "coding", priority: "quality"
• action: "setup_3_model"

Recommended 3-Model Setup:
1. 🖥️  LM Studio (Local) - Fast coding & debugging
2. 🌐 OpenRouter/DeepSeek - Advanced reasoning
3. 🎯 Professional Prompts - Specialized tasks

Benefits:
✅ Cost optimization (local models)
✅ Speed optimization (no API latency)
✅ Quality optimization (specialized models)
✅ Reliability (multiple fallbacks)
"""
        
        return Response(
            message="LM Studio Orchestrator Help",
            data={"help": help_text}
        )


# Register the tool
def get_tool() -> Tool:
    return LMStudioOrchestrator()
