{"name": "chromadb-root", "private": true, "version": "2.4.7", "description": "A JavaScript interface for chroma", "keywords": [], "author": "", "license": "Apache-2.0", "devDependencies": {"@jest/globals": "^29.7.0", "@jest/types": "^29.6.3", "@openapi-generator-plus/typescript-fetch-client-generator": "^1.11.0", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.0", "@types/node": "^20.8.10", "bcrypt": "^5.1.1", "chalk": "^4.1.2", "jest": "^29.5.0", "npm-run-all": "^4.1.5", "openapi-generator-plus": "^2.20.0", "openapi-types": "^12.1.3", "prettier": "2.8.7", "rimraf": "^5.0.0", "testcontainers": "^10.9.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "tsd": "^0.28.1", "tsup": "^7.2.0", "typescript": "^5.0.4"}, "type": "module", "main": "dist/cjs/chromadb.cjs", "types": "dist/chromadb.d.ts", "module": "dist/chromadb.legacy-esm.js", "exports": {".": {"import": {"types": "./dist/chromadb.d.ts", "default": "./dist/chromadb.mjs"}, "require": {"types": "./dist/cjs/chromadb.d.cts", "default": "./dist/cjs/chromadb.cjs"}}}, "files": ["src", "dist"], "scripts": {"build": "pnpm -r build", "build:core": "NODE_OPTIONS=--enable-source-maps pnpm --filter @internal/chromadb-core build", "build:packages": "NODE_OPTIONS=--enable-source-maps pnpm --filter \"!@internal/chromadb-core\" build", "test": "pnpm -r test", "publish:packages": "pnpm publish -r --access public --no-git-checks --filter \"!@internal/chromadb-core\"", "release": "pnpm build && pnpm publish:packages", "release_alpha": "pnpm build && pnpm publish:packages --tag alpha", "release_dev": "pnpm build && pnpm publish:packages --tag dev", "genapi": "./genapi.sh", "prettier": "prettier --write ."}, "engines": {"node": ">=14.17.0"}, "dependencies": {"cliui": "^8.0.1", "isomorphic-fetch": "^3.0.0"}, "peerDependencies": {"@google/generative-ai": "^0.1.1", "cohere-ai": "^5.0.0 || ^6.0.0 || ^7.0.0", "ollama": "^0.5.0", "openai": "^3.0.0 || ^4.0.0", "voyageai": "^0.0.3-1"}, "peerDependenciesMeta": {"@google/generative-ai": {"optional": true}, "cohere-ai": {"optional": true}, "openai": {"optional": true}, "voyageai": {"optional": true}, "ollama": {"optional": true}}, "pnpm": {"overrides": {"whatwg-url": "13.0.0"}}}