{"name": "chroma-js-client", "private": true, "description": "Monorepo for the Chroma JS client and embedding providers", "scripts": {"build": "pnpm --filter \"ai-embeddings-common\" build && pnpm --filter \"default-embed\" build && pnpm --filter \"chromadb\" build && pnpm -r --filter \"./packages/**\" --filter \"!ai-embeddings-common\" --filter \"!default-embed\" --filter \"!chromadb\" --filter \"!all\" build && pnpm --filter \"all\" build", "build:chromadb": "pnpm --filter \"chromadb\" build", "test": "pnpm -r --filter \"./packages/**\" test", "genapi:chromadb": "pnpm --filter \"chromadb\" genapi", "prettier": "pnpm --filter \"chromadb\" prettier"}, "devDependencies": {"@types/node": "^20.8.10", "dotenv": "^16.3.1", "npm-run-all": "^4.1.5", "rimraf": "^5.0.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "prettier": "^3.6.0"}, "engines": {"node": ">=14.17.0", "pnpm": ">=8"}, "version": "0.0.1"}