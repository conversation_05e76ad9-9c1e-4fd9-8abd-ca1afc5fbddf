name: 🚀 Feature request
description: Suggest an idea for Chroma
title: "[Feature Request]: "
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to request this feature!
  - type: textarea
    id: problem
    attributes:
      label: Describe the problem
      description: Please provide a clear and concise description the problem this feature would solve. The more information you can provide here, the better.
      placeholder: I prefer if...
    validations:
      required: true
  - type: textarea
    id: solution
    attributes:
      label: Describe the proposed solution
      description: Please provide a clear and concise description of what you would like to happen.
      placeholder: I would like to see...
    validations:
      required: true
  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives considered
      description: "Please provide a clear and concise description of any alternative solutions or features you've considered."
  - type: dropdown
    id: importance
    attributes:
      label: Importance
      description: How important is this feature to you?
      options:
        - nice to have
        - would make my life easier
        - i cannot use Chroma without it
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: Additional Information
      description: Add any other context or screenshots about the feature request here.
