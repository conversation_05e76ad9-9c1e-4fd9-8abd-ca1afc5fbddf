#!/bin/bash
set -e

echo "=== Agent Zero Development Environment Setup ==="

# Update system packages
sudo apt-get update

# Install Python 3.12 and development tools
sudo apt-get install -y python3.12 python3.12-venv python3.12-dev python3-pip
sudo apt-get install -y build-essential curl git

# Install Node.js and npm for JavaScript components
sudo apt-get install -y nodejs npm

# Create Python virtual environment
python3.12 -m venv .venv
source .venv/bin/activate

# Add virtual environment activation to profile
echo "source $(pwd)/.venv/bin/activate" >> $HOME/.profile

# Upgrade pip and install UV package manager
pip install --upgrade pip
pip install uv

# Install Python dependencies using UV for faster installation
uv pip install -r requirements.txt

# Install additional testing dependencies
uv pip install pytest pytest-asyncio pytest-mock pytest-cov

# Install Playwright browsers for testing
export PLAYWRIGHT_BROWSERS_PATH=$(pwd)/tmp/playwright
playwright install chromium --only-shell

# Create test directories
mkdir -p tests/unit/helpers
mkdir -p tests/unit/tools
mkdir -p tests/unit/models
mkdir -p tests/integration
mkdir -p tmp/test_files

# Create pytest configuration
cat > pytest.ini << 'EOF'
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short --strict-markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
asyncio_mode = auto
EOF

# Create test files for core functionality
cat > tests/unit/helpers/test_files.py << 'EOF'
import pytest
import os
import tempfile
import shutil
from unittest.mock import patch, mock_open
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from python.helpers import files


class TestFiles:
    def setup_method(self):
        """Setup test environment before each test"""
        self.test_dir = tempfile.mkdtemp()
        self.original_get_base_dir = files.get_base_dir
        files.get_base_dir = lambda: self.test_dir

    def teardown_method(self):
        """Cleanup after each test"""
        files.get_base_dir = self.original_get_base_dir
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_get_abs_path(self):
        """Test absolute path generation"""
        result = files.get_abs_path("test", "file.txt")
        expected = os.path.join(self.test_dir, "test", "file.txt")
        assert result == expected

    def test_write_and_read_file(self):
        """Test writing and reading files"""
        content = "Test content"
        file_path = "test_file.txt"
        
        files.write_file(file_path, content)
        result = files.read_file(file_path)
        
        assert result == content

    def test_replace_placeholders_text(self):
        """Test text placeholder replacement"""
        content = "Hello {{name}}, welcome to {{place}}!"
        result = files.replace_placeholders_text(content, name="John", place="Agent Zero")
        expected = "Hello John, welcome to Agent Zero!"
        assert result == expected

    def test_replace_placeholders_json(self):
        """Test JSON placeholder replacement"""
        content = '{"name": {{name}}, "age": {{age}}}'
        result = files.replace_placeholders_json(content, name="John", age=30)
        expected = '{"name": "John", "age": 30}'
        assert result == expected

    def test_list_files(self):
        """Test file listing"""
        # Create test files
        test_dir = "test_dir"
        os.makedirs(files.get_abs_path(test_dir), exist_ok=True)
        
        test_files = ["file1.txt", "file2.py", "file3.md"]
        for file_name in test_files:
            files.write_file(os.path.join(test_dir, file_name), "content")
        
        # Test listing all files
        result = files.list_files(test_dir)
        assert set(result) == set(test_files)
        
        # Test filtering
        py_files = files.list_files(test_dir, "*.py")
        assert py_files == ["file2.py"]

    def test_exists(self):
        """Test file existence check"""
        file_path = "test_exists.txt"
        assert not files.exists(file_path)
        
        files.write_file(file_path, "content")
        assert files.exists(file_path)

    def test_remove_code_fences(self):
        """Test code fence removal"""
        content = """```python
print("Hello World")
```"""
        result = files.remove_code_fences(content)
        expected = 'print("Hello World")'
        assert result == expected

    def test_is_full_json_template(self):
        """Test JSON template detection"""
        json_content = """```json
{"key": "value"}
```"""
        assert files.is_full_json_template(json_content)
        
        non_json_content = "Regular text"
        assert not files.is_full_json_template(non_json_content)
EOF

cat > tests/unit/helpers/test_strings.py << 'EOF'
import pytest
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from python.helpers import strings


class TestStrings:
    def test_sanitize_string(self):
        """Test string sanitization"""
        # Test normal string
        result = strings.sanitize_string("Hello World")
        assert result == "Hello World"
        
        # Test non-string input
        result = strings.sanitize_string(123)
        assert result == "123"
        
        # Test None input
        result = strings.sanitize_string(None)
        assert result == "None"

    def test_format_key(self):
        """Test key formatting"""
        # Test camelCase
        result = strings.format_key("camelCaseKey")
        assert result == "Camel Case Key"
        
        # Test snake_case
        result = strings.format_key("snake_case_key")
        assert result == "Snake Case Key"
        
        # Test mixed case
        result = strings.format_key("mixedCase_key")
        assert result == "Mixed Case Key"

    def test_dict_to_text(self):
        """Test dictionary to text conversion"""
        test_dict = {
            "first_name": "John",
            "last_name": "Doe",
            "age": 30
        }
        result = strings.dict_to_text(test_dict)
        
        assert "First Name:" in result
        assert "John" in result
        assert "Last Name:" in result
        assert "Doe" in result
        assert "Age:" in result
        assert "30" in result

    def test_calculate_valid_match_lengths(self):
        """Test string matching calculation"""
        first = "Hello World"
        second = "Hello World"
        
        result = strings.calculate_valid_match_lengths(first, second)
        assert result == (len(first), len(second))
        
        # Test partial match
        first = "Hello"
        second = "Hello World"
        result = strings.calculate_valid_match_lengths(first, second)
        assert result[0] == len(first)
EOF

cat > tests/unit/test_models.py << 'EOF'
import pytest
import os
import sys
from unittest.mock import patch, MagicMock
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

import models
from models import ModelType, ModelProvider


class TestModels:
    def test_model_type_enum(self):
        """Test ModelType enum values"""
        assert ModelType.CHAT.value == "Chat"
        assert ModelType.EMBEDDING.value == "Embedding"

    def test_model_provider_enum(self):
        """Test ModelProvider enum values"""
        assert ModelProvider.OPENAI.value == "OpenAI"
        assert ModelProvider.ANTHROPIC.value == "Anthropic"
        assert ModelProvider.OLLAMA.value == "Ollama"

    def test_get_api_key(self):
        """Test API key retrieval"""
        with patch('python.helpers.dotenv.get_dotenv_value') as mock_dotenv:
            mock_dotenv.return_value = "test_key"
            result = models.get_api_key("openai")
            assert result == "test_key"
            
            # Test fallback to "None"
            mock_dotenv.return_value = None
            result = models.get_api_key("nonexistent")
            assert result == "None"

    @patch('python.helpers.dotenv.get_dotenv_value')
    def test_get_lmstudio_base_url(self, mock_dotenv):
        """Test LM Studio base URL generation"""
        mock_dotenv.return_value = None
        with patch('python.helpers.runtime.get_local_url', return_value="localhost"):
            result = models.get_lmstudio_base_url()
            assert "localhost:1234/v1" in result

    def test_get_model_function_name_generation(self):
        """Test that model getter function names are generated correctly"""
        # Test that the function name generation logic works
        provider = ModelProvider.OPENAI
        model_type = ModelType.CHAT
        expected_function_name = f"get_{provider.name.lower()}_{model_type.name.lower()}"
        assert expected_function_name == "get_openai_chat"
EOF

cat > tests/unit/test_agent.py << 'EOF'
import pytest
import os
import sys
from unittest.mock import Mock, patch, MagicMock
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from agent import Agent, AgentConfig, AgentContext, AgentContextType, ModelConfig
from models import ModelProvider


class TestAgentConfig:
    def test_agent_config_creation(self):
        """Test AgentConfig dataclass creation"""
        chat_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        utility_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-3.5-turbo", kwargs={})
        embeddings_model = ModelConfig(provider=ModelProvider.OPENAI, name="text-embedding-ada-002", kwargs={})
        browser_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        
        config = AgentConfig(
            chat_model=chat_model,
            utility_model=utility_model,
            embeddings_model=embeddings_model,
            browser_model=browser_model,
            mcp_servers="{}",
            prompts_subdir="default",
            memory_subdir="default"
        )
        
        assert config.chat_model.provider == ModelProvider.OPENAI
        assert config.chat_model.name == "gpt-4"
        assert config.prompts_subdir == "default"
        assert config.memory_subdir == "default"
        assert config.code_exec_docker_enabled == False


class TestAgentContext:
    def setup_method(self):
        """Setup test environment"""
        self.chat_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        self.utility_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-3.5-turbo", kwargs={})
        self.embeddings_model = ModelConfig(provider=ModelProvider.OPENAI, name="text-embedding-ada-002", kwargs={})
        self.browser_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        
        self.config = AgentConfig(
            chat_model=self.chat_model,
            utility_model=self.utility_model,
            embeddings_model=self.embeddings_model,
            browser_model=self.browser_model,
            mcp_servers="{}"
        )

    def test_agent_context_creation(self):
        """Test AgentContext creation"""
        context = AgentContext(config=self.config, name="test_context")
        
        assert context.name == "test_context"
        assert context.config == self.config
        assert context.paused == False
        assert context.type == AgentContextType.USER
        assert context.agent0 is not None

    def test_agent_context_type_enum(self):
        """Test AgentContextType enum"""
        assert AgentContextType.USER.value == "user"
        assert AgentContextType.TASK.value == "task"
        assert AgentContextType.MCP.value == "mcp"


class TestAgent:
    def setup_method(self):
        """Setup test environment"""
        self.chat_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        self.utility_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-3.5-turbo", kwargs={})
        self.embeddings_model = ModelConfig(provider=ModelProvider.OPENAI, name="text-embedding-ada-002", kwargs={})
        self.browser_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        
        self.config = AgentConfig(
            chat_model=self.chat_model,
            utility_model=self.utility_model,
            embeddings_model=self.embeddings_model,
            browser_model=self.browser_model,
            mcp_servers="{}"
        )
        self.context = AgentContext(config=self.config)

    def test_agent_creation(self):
        """Test Agent creation"""
        agent = Agent(number=1, config=self.config, context=self.context)
        
        assert agent.number == 1
        assert agent.agent_name == "Agent 1"
        assert agent.config == self.config
        assert agent.context == self.context
        assert agent.data == {}

    def test_agent_data_methods(self):
        """Test agent data storage methods"""
        agent = Agent(number=1, config=self.config, context=self.context)
        
        # Test set_data and get_data
        agent.set_data("test_key", "test_value")
        assert agent.get_data("test_key") == "test_value"
        
        # Test get_data with default
        assert agent.get_data("nonexistent", "default") == "default"

    def test_agent_constants(self):
        """Test Agent class constants"""
        assert Agent.DATA_NAME_SUPERIOR == "_superior"
        assert Agent.DATA_NAME_SUBORDINATE == "_subordinate"
        assert Agent.DATA_NAME_CTX_WINDOW == "ctx_window"
EOF

cat > tests/unit/tools/test_knowledge_tool.py << 'EOF'
import pytest
import os
import sys
from unittest.mock import Mock, patch, AsyncMock
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../'))

from python.tools.knowledge_tool import Knowledge, SEARCH_ENGINE_RESULTS
from python.helpers.tool import Response
from agent import Agent, AgentConfig, AgentContext, ModelConfig
from models import ModelProvider


class TestKnowledgeTool:
    def setup_method(self):
        """Setup test environment"""
        self.chat_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        self.utility_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-3.5-turbo", kwargs={})
        self.embeddings_model = ModelConfig(provider=ModelProvider.OPENAI, name="text-embedding-ada-002", kwargs={})
        self.browser_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        
        self.config = AgentConfig(
            chat_model=self.chat_model,
            utility_model=self.utility_model,
            embeddings_model=self.embeddings_model,
            browser_model=self.browser_model,
            mcp_servers="{}"
        )
        self.context = AgentContext(config=self.config)
        self.agent = Agent(number=1, config=self.config, context=self.context)

    def test_knowledge_tool_creation(self):
        """Test Knowledge tool creation"""
        tool = Knowledge(
            agent=self.agent,
            name="knowledge",
            method=None,
            args={"question": "test question"},
            message="test message"
        )
        
        assert tool.agent == self.agent
        assert tool.name == "knowledge"
        assert tool.args["question"] == "test question"

    @pytest.mark.asyncio
    async def test_searxng_search(self):
        """Test SearXNG search functionality"""
        tool = Knowledge(
            agent=self.agent,
            name="knowledge",
            method=None,
            args={"question": "test question"},
            message="test message"
        )
        
        mock_result = {
            "results": [
                {"title": "Test Result", "url": "http://test.com", "content": "Test content"}
            ]
        }
        
        with patch('python.helpers.searxng.search', new_callable=AsyncMock) as mock_search:
            mock_search.return_value = mock_result
            result = await tool.searxng_search("test question")
            
            assert result == mock_result
            mock_search.assert_called_once_with("test question")

    @pytest.mark.asyncio
    async def test_mem_search(self):
        """Test memory search functionality"""
        tool = Knowledge(
            agent=self.agent,
            name="knowledge",
            method=None,
            args={"question": "test question"},
            message="test message"
        )
        
        with patch('python.helpers.memory.search', new_callable=AsyncMock) as mock_search:
            mock_search.return_value = "memory search result"
            result = await tool.mem_search("test question")
            
            assert result == "memory search result"

    def test_search_engine_results_constant(self):
        """Test SEARCH_ENGINE_RESULTS constant"""
        assert SEARCH_ENGINE_RESULTS == 10
EOF

cat > tests/integration/test_basic_functionality.py << 'EOF'
import pytest
import os
import sys
import tempfile
import shutil
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from python.helpers import files, strings
from agent import AgentConfig, AgentContext, Agent, ModelConfig
from models import ModelProvider


class TestBasicIntegration:
    def setup_method(self):
        """Setup test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_get_base_dir = files.get_base_dir
        files.get_base_dir = lambda: self.test_dir

    def teardown_method(self):
        """Cleanup after each test"""
        files.get_base_dir = self.original_get_base_dir
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_file_operations_integration(self):
        """Test integrated file operations"""
        # Test writing a file with placeholders
        template_content = "Hello {{name}}, your age is {{age}}!"
        files.write_file("template.txt", template_content)
        
        # Test reading and processing placeholders
        result = files.read_file("template.txt", name="John", age=30)
        expected = "Hello John, your age is 30!"
        assert result == expected

    def test_agent_creation_integration(self):
        """Test agent creation with all components"""
        chat_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        utility_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-3.5-turbo", kwargs={})
        embeddings_model = ModelConfig(provider=ModelProvider.OPENAI, name="text-embedding-ada-002", kwargs={})
        browser_model = ModelConfig(provider=ModelProvider.OPENAI, name="gpt-4", kwargs={})
        
        config = AgentConfig(
            chat_model=chat_model,
            utility_model=utility_model,
            embeddings_model=embeddings_model,
            browser_model=browser_model,
            mcp_servers="{}"
        )
        
        context = AgentContext(config=config, name="test_integration")
        agent = Agent(number=1, config=config, context=context)
        
        # Test agent properties
        assert agent.number == 1
        assert agent.agent_name == "Agent 1"
        assert context.name == "test_integration"
        
        # Test data storage
        agent.set_data("integration_test", True)
        assert agent.get_data("integration_test") == True

    def test_string_formatting_integration(self):
        """Test string formatting with real data"""
        test_data = {
            "firstName": "John",
            "lastName": "Doe",
            "userAge": 30,
            "email_address": "<EMAIL>"
        }
        
        formatted_text = strings.dict_to_text(test_data)
        
        # Check that all keys are properly formatted
        assert "First Name:" in formatted_text
        assert "Last Name:" in formatted_text
        assert "User Age:" in formatted_text
        assert "Email Address:" in formatted_text
        
        # Check that values are present
        assert "John" in formatted_text
        assert "Doe" in formatted_text
        assert "30" in formatted_text
        assert "<EMAIL>" in formatted_text
EOF

echo "=== Test files created successfully ==="
echo "=== Setup completed ==="