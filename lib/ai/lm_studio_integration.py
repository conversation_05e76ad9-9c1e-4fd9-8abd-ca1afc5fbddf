"""
LM Studio Integration for Agent Zero
Connects local LM Studio models with Agent Zero orchestrator
"""

import requests
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class LMStudioModel:
    """LM Studio model information"""
    id: str
    name: str
    size: str
    architecture: str
    specialization: str
    recommended_use: str


class LMStudioManager:
    """Manages LM Studio integration with Agent Zero"""
    
    def __init__(self, base_url: str = "http://localhost:1234/v1"):
        self.base_url = base_url
        self.api_key = "lm-studio"  # LM Studio doesn't require real API key
        self.available_models = {}
        self.current_model = None
        
    def check_connection(self) -> Dict[str, Any]:
        """Check if LM Studio is running and accessible"""
        try:
            response = requests.get(f"{self.base_url}/models", timeout=5)
            if response.status_code == 200:
                models = response.json()
                return {
                    "success": True,
                    "status": "Connected to LM Studio",
                    "models_available": len(models.get("data", [])),
                    "endpoint": self.base_url
                }
            else:
                return {
                    "success": False,
                    "error": f"LM Studio responded with status {response.status_code}"
                }
        except requests.exceptions.ConnectionError:
            return {
                "success": False,
                "error": "Cannot connect to LM Studio. Make sure it's running on localhost:1234"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Connection error: {str(e)}"
            }
    
    def get_available_models(self) -> Dict[str, Any]:
        """Get list of available models from LM Studio"""
        try:
            response = requests.get(f"{self.base_url}/models")
            if response.status_code == 200:
                models_data = response.json()
                models = []
                
                for model in models_data.get("data", []):
                    model_info = self._analyze_model(model["id"])
                    models.append({
                        "id": model["id"],
                        "name": model_info["name"],
                        "specialization": model_info["specialization"],
                        "recommended_use": model_info["recommended_use"],
                        "size": model_info["size"]
                    })
                
                self.available_models = {m["id"]: m for m in models}
                
                return {
                    "success": True,
                    "models": models,
                    "total_models": len(models)
                }
            else:
                return {
                    "success": False,
                    "error": f"Failed to get models: {response.status_code}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Error getting models: {str(e)}"
            }
    
    def _analyze_model(self, model_id: str) -> Dict[str, str]:
        """Analyze model ID to determine specialization"""
        model_lower = model_id.lower()
        
        # Code-focused models
        if any(keyword in model_lower for keyword in ["code", "coder", "codellama", "starcoder"]):
            return {
                "name": model_id,
                "specialization": "Code Generation & Debugging",
                "recommended_use": "Programming, debugging, code review",
                "size": self._extract_size(model_id)
            }
        
        # Instruction-following models
        elif any(keyword in model_lower for keyword in ["instruct", "chat", "assistant"]):
            return {
                "name": model_id,
                "specialization": "Instruction Following",
                "recommended_use": "General tasks, conversation, reasoning",
                "size": self._extract_size(model_id)
            }
        
        # Reasoning models
        elif any(keyword in model_lower for keyword in ["wizard", "mistral", "llama"]):
            return {
                "name": model_id,
                "specialization": "Advanced Reasoning",
                "recommended_use": "Complex problem solving, analysis",
                "size": self._extract_size(model_id)
            }
        
        # Default
        else:
            return {
                "name": model_id,
                "specialization": "General Purpose",
                "recommended_use": "Various tasks",
                "size": self._extract_size(model_id)
            }
    
    def _extract_size(self, model_id: str) -> str:
        """Extract model size from model ID"""
        import re
        
        # Look for patterns like 7B, 13B, 34B, etc.
        size_match = re.search(r'(\d+\.?\d*)[Bb]', model_id)
        if size_match:
            return f"{size_match.group(1)}B"
        
        # Look for patterns like Q4_K_M, Q8_0, etc.
        quant_match = re.search(r'[Qq]\d+_[KkMm]?_?[MmSs]?', model_id)
        if quant_match:
            return f"Quantized ({quant_match.group()})"
        
        return "Unknown"
    
    def send_prompt(self, prompt: str, model_id: str = None, 
                   max_tokens: int = 2048, temperature: float = 0.7) -> Dict[str, Any]:
        """Send prompt to LM Studio model"""
        try:
            # Use current model if none specified
            if not model_id and self.current_model:
                model_id = self.current_model
            elif not model_id:
                return {
                    "success": False,
                    "error": "No model specified and no current model set"
                }
            
            payload = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                return {
                    "success": True,
                    "response": content,
                    "model_used": model_id,
                    "tokens_used": result.get("usage", {}).get("total_tokens", 0)
                }
            else:
                return {
                    "success": False,
                    "error": f"LM Studio error: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"Error sending prompt: {str(e)}"
            }
    
    def set_current_model(self, model_id: str) -> Dict[str, Any]:
        """Set the current active model"""
        if model_id in self.available_models:
            self.current_model = model_id
            return {
                "success": True,
                "message": f"Current model set to: {model_id}",
                "model_info": self.available_models[model_id]
            }
        else:
            return {
                "success": False,
                "error": f"Model {model_id} not found in available models"
            }
    
    def get_model_recommendations(self, task_type: str) -> List[Dict[str, Any]]:
        """Get model recommendations based on task type"""
        if not self.available_models:
            self.get_available_models()
        
        recommendations = []
        
        for model_id, model_info in self.available_models.items():
            score = 0
            
            # Score based on task type
            if task_type.lower() in ["coding", "programming", "debug"]:
                if "code" in model_info["specialization"].lower():
                    score += 10
                elif "instruct" in model_info["specialization"].lower():
                    score += 5
            
            elif task_type.lower() in ["reasoning", "analysis", "problem"]:
                if "reasoning" in model_info["specialization"].lower():
                    score += 10
                elif "wizard" in model_id.lower() or "mistral" in model_id.lower():
                    score += 8
            
            elif task_type.lower() in ["chat", "conversation", "general"]:
                if "instruct" in model_info["specialization"].lower():
                    score += 10
                elif "chat" in model_id.lower():
                    score += 8
            
            # Bonus for larger models (generally better performance)
            if "13B" in model_info["size"] or "34B" in model_info["size"]:
                score += 3
            elif "7B" in model_info["size"]:
                score += 1
            
            if score > 0:
                recommendations.append({
                    **model_info,
                    "score": score,
                    "task_fit": task_type
                })
        
        # Sort by score
        recommendations.sort(key=lambda x: x["score"], reverse=True)
        return recommendations[:3]  # Top 3 recommendations


# Helper functions for Agent Zero integration
def setup_lm_studio() -> Dict[str, Any]:
    """Setup LM Studio connection for Agent Zero"""
    lm = LMStudioManager()
    
    # Check connection
    connection = lm.check_connection()
    if not connection["success"]:
        return connection
    
    # Get available models
    models = lm.get_available_models()
    return {
        "connection": connection,
        "models": models
    }


def get_best_model_for_task(task_type: str) -> Dict[str, Any]:
    """Get the best LM Studio model for a specific task"""
    lm = LMStudioManager()
    lm.get_available_models()
    
    recommendations = lm.get_model_recommendations(task_type)
    
    if recommendations:
        best_model = recommendations[0]
        return {
            "success": True,
            "recommended_model": best_model,
            "all_recommendations": recommendations
        }
    else:
        return {
            "success": False,
            "error": f"No suitable models found for task: {task_type}"
        }


# Example usage
if __name__ == "__main__":
    lm = LMStudioManager()
    
    # Test connection
    print("Testing LM Studio connection...")
    connection = lm.check_connection()
    print(f"Connection: {connection}")
    
    if connection["success"]:
        # Get models
        models = lm.get_available_models()
        print(f"Available models: {len(models.get('models', []))}")
        
        # Get recommendations for coding
        recommendations = lm.get_model_recommendations("coding")
        print(f"Coding recommendations: {recommendations}")
